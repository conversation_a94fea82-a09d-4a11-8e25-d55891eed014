"""
Préparation des données pour fine-tuning Ollama
Convertit wasifis/bank-assistant-qa au format Ollama
"""

import json
import os
from pathlib import Path

def load_wasifis_data():
    """Charge les données wasifis/bank-assistant-qa"""
    print("=== Chargement des données wasifis/bank-assistant-qa ===")
    
    data_files = {
        'train': 'processed_data/train.json',
        'validation': 'processed_data/validation.json',
        'test': 'processed_data/test.json'
    }
    
    all_data = []
    
    for split_name, file_path in data_files.items():
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = [json.loads(line) for line in f]
            
            print(f"✅ {split_name}: {len(data)} exemples")
            all_data.extend(data)
        else:
            print(f"⚠️ Fichier non trouvé: {file_path}")
    
    print(f"📊 Total: {len(all_data)} exemples chargés")
    return all_data

def convert_to_ollama_format(data, output_file="ollama_training_data.jsonl"):
    """Convertit les données au format Ollama"""
    print(f"\n=== Conversion au format Ollama ===")
    
    ollama_data = []
    
    for item in data:
        if 'input' in item and 'output' in item:
            # Format Ollama pour le fine-tuning
            ollama_item = {
                "prompt": f"<|im_start|>system\nVous êtes un assistant bancaire expert et professionnel. Répondez aux questions des clients de manière précise et courtoise.<|im_end|>\n<|im_start|>user\n{item['input']}<|im_end|>\n<|im_start|>assistant\n",
                "completion": f"{item['output']}<|im_end|>"
            }
            ollama_data.append(ollama_item)
    
    # Sauvegarder au format JSONL
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in ollama_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"✅ {len(ollama_data)} exemples convertis")
    print(f"✅ Fichier sauvegardé: {output_file}")
    
    return output_file

def create_modelfile(base_model="llama3.1:8b", training_file="ollama_training_data.jsonl"):
    """Crée le Modelfile pour Ollama"""
    print(f"\n=== Création du Modelfile ===")
    
    modelfile_content = f"""FROM {base_model}

# Paramètres de fine-tuning
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1

# Prompt système pour le banking
SYSTEM \"\"\"Vous êtes un assistant bancaire expert et professionnel. 
Vous aidez les clients avec leurs questions bancaires de manière précise, courtoise et professionnelle.
Utilisez vos connaissances spécialisées en services bancaires pour fournir des réponses utiles et appropriées.\"\"\"

# Template de conversation
TEMPLATE \"\"\"<|im_start|>system
{{{{ .System }}}}<|im_end|>
<|im_start|>user
{{{{ .Prompt }}}}<|im_end|>
<|im_start|>assistant
\"\"\"

# Données d'entraînement
ADAPTER {training_file}
"""
    
    with open("Modelfile", 'w', encoding='utf-8') as f:
        f.write(modelfile_content)
    
    print("✅ Modelfile créé")
    return "Modelfile"

def create_training_script():
    """Crée le script de fine-tuning Ollama"""
    print(f"\n=== Création du script d'entraînement ===")
    
    script_content = """#!/bin/bash
# Script de fine-tuning Ollama pour chatbot bancaire

echo "🏦 Fine-tuning Ollama - Chatbot Bancaire"
echo "========================================"

# Vérifier qu'Ollama est démarré
echo "1. Vérification d'Ollama..."
ollama list > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Ollama n'est pas démarré. Lancez 'ollama serve' d'abord."
    exit 1
fi
echo "✅ Ollama est actif"

# Créer le modèle fine-tuné
echo "2. Création du modèle fine-tuné..."
ollama create banking-assistant -f Modelfile

if [ $? -eq 0 ]; then
    echo "✅ Modèle 'banking-assistant' créé avec succès!"
    echo ""
    echo "🎯 Pour utiliser le modèle:"
    echo "ollama run banking-assistant"
    echo ""
    echo "🧪 Test rapide:"
    ollama run banking-assistant "Quels sont les frais de tenue de compte?"
else
    echo "❌ Erreur lors de la création du modèle"
    exit 1
fi
"""
    
    with open("train_ollama.sh", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # Version Windows
    script_content_win = """@echo off
REM Script de fine-tuning Ollama pour chatbot bancaire

echo 🏦 Fine-tuning Ollama - Chatbot Bancaire
echo ========================================

REM Vérifier qu'Ollama est démarré
echo 1. Vérification d'Ollama...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama n'est pas démarré. Lancez 'ollama serve' d'abord.
    exit /b 1
)
echo ✅ Ollama est actif

REM Créer le modèle fine-tuné
echo 2. Création du modèle fine-tuné...
ollama create banking-assistant -f Modelfile

if %errorlevel% equ 0 (
    echo ✅ Modèle 'banking-assistant' créé avec succès!
    echo.
    echo 🎯 Pour utiliser le modèle:
    echo ollama run banking-assistant
    echo.
    echo 🧪 Test rapide:
    ollama run banking-assistant "Quels sont les frais de tenue de compte?"
) else (
    echo ❌ Erreur lors de la création du modèle
    exit /b 1
)
"""
    
    with open("train_ollama.bat", 'w', encoding='utf-8') as f:
        f.write(script_content_win)
    
    print("✅ Scripts d'entraînement créés:")
    print("  - train_ollama.sh (Linux/Mac)")
    print("  - train_ollama.bat (Windows)")

def create_test_script():
    """Crée un script de test du modèle fine-tuné"""
    test_script = """#!/usr/bin/env python3
\"\"\"
Test du modèle Ollama fine-tuné
\"\"\"

import ollama
import json

def test_banking_model():
    print("🧪 Test du modèle banking-assistant")
    print("=" * 40)
    
    client = ollama.Client()
    
    # Questions de test
    test_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Quelles sont les conditions pour un prêt immobilier?",
        "Comment activer ma carte bancaire?",
        "Que faire en cas de perte de carte?",
        "Quels sont les horaires d'ouverture?",
        "Comment faire un virement international?",
        "Quels documents pour ouvrir un compte?"
    ]
    
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\\n{i}. Question: {question}")
        
        try:
            response = client.chat(
                model='banking-assistant',
                messages=[
                    {'role': 'user', 'content': question}
                ]
            )
            
            answer = response['message']['content']
            print(f"   Réponse: {answer[:150]}...")
            
            results.append({
                "question": question,
                "answer": answer,
                "status": "success"
            })
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            results.append({
                "question": question,
                "error": str(e),
                "status": "error"
            })
    
    # Sauvegarder les résultats
    with open("test_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\\n✅ Test terminé. Résultats sauvegardés dans test_results.json")
    
    # Statistiques
    success_count = sum(1 for r in results if r['status'] == 'success')
    print(f"📊 Réussites: {success_count}/{len(test_questions)}")

if __name__ == "__main__":
    test_banking_model()
"""
    
    with open("test_model.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Script de test créé: test_model.py")

def main():
    """Fonction principale"""
    print("🏦 Préparation Fine-tuning Ollama")
    print("=" * 40)
    
    # 1. Charger les données
    data = load_wasifis_data()
    if not data:
        print("❌ Aucune donnée trouvée")
        return
    
    # 2. Convertir au format Ollama
    training_file = convert_to_ollama_format(data)
    
    # 3. Créer le Modelfile
    modelfile = create_modelfile(training_file=training_file)
    
    # 4. Créer les scripts
    create_training_script()
    create_test_script()
    
    print(f"\\n" + "=" * 50)
    print("🎉 PRÉPARATION TERMINÉE!")
    print("=" * 50)
    print("📁 Fichiers créés:")
    print(f"  ✅ {training_file} - Données d'entraînement")
    print(f"  ✅ Modelfile - Configuration du modèle")
    print(f"  ✅ train_ollama.bat - Script d'entraînement Windows")
    print(f"  ✅ train_ollama.sh - Script d'entraînement Linux/Mac")
    print(f"  ✅ test_model.py - Script de test")
    
    print(f"\\n🚀 PROCHAINES ÉTAPES:")
    print(f"1. Vérifiez qu'Ollama est démarré: ollama serve")
    print(f"2. Lancez l'entraînement: train_ollama.bat")
    print(f"3. Testez le modèle: python test_model.py")
    print(f"4. Utilisez le modèle: ollama run banking-assistant")

if __name__ == "__main__":
    main()
