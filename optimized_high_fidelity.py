"""
Training optimisé haute fidélité - 2 epochs seulement
Configuration pour fidélité maximale en moins de temps
"""

import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk

print("🎯 TRAINING OPTIMISÉ HAUTE FIDÉLITÉ (2 EPOCHS)")
print("=" * 50)

# Configuration
output_dir = "./llama_banking_optimized"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

torch.cuda.empty_cache()
print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Configuration 4-bit
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

# Tokenizer
print("📝 Chargement tokenizer...")
model_path = "./models/Llama-3.1-8B-Instruct"

if Path(model_path).exists():
    tokenizer = AutoTokenizer.from_pretrained(model_path)
else:
    tokenizer = AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        token="*************************************"
    )

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Modèle
print("🦙 Chargement modèle...")
if Path(model_path).exists():
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
else:
    model = AutoModelForCausalLM.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True,
        token="*************************************"
    )

model = prepare_model_for_kbit_training(model)

# LoRA OPTIMISÉ pour fidélité maximale
print("🔧 Configuration LoRA optimisée...")
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=48,  # Rank élevé mais pas maximum (compromis vitesse/fidélité)
    lora_alpha=96,  # Alpha élevé
    lora_dropout=0.05,  # Dropout faible
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    bias="none"
)

peft_model = get_peft_model(model, lora_config)
peft_model.print_trainable_parameters()

# Dataset
print("📊 Chargement dataset...")
dataset = load_from_disk("./processed_banking_dataset_no_instruction")
train_dataset = dataset["train"]

# Split optimisé (90/10 pour plus de données d'entraînement)
train_size = int(0.9 * len(train_dataset))
train_split = train_dataset.select(range(train_size))
eval_split = train_dataset.select(range(train_size, len(train_dataset)))

print(f"✅ Train: {len(train_split)} exemples")
print(f"✅ Validation: {len(eval_split)} exemples")

# Configuration OPTIMISÉE pour fidélité en 2 epochs
training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=2,  # SEULEMENT 2 epochs
    per_device_train_batch_size=1,
    gradient_accumulation_steps=16,  # Réduit pour plus de vitesse
    learning_rate=1e-5,  # Learning rate optimisé (pas trop bas)
    weight_decay=0.01,
    warmup_ratio=0.05,
    lr_scheduler_type="cosine",
    max_grad_norm=0.5,
    optim="paged_adamw_8bit",
    fp16=True,
    gradient_checkpointing=True,
    dataloader_pin_memory=False,
    dataloader_num_workers=0,
    logging_steps=5,
    eval_steps=100,
    save_steps=200,
    save_total_limit=3,
    report_to=[],
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Trainer
trainer = Trainer(
    model=peft_model,
    args=training_args,
    train_dataset=train_split,
    eval_dataset=eval_split,
    data_collator=data_collator,
    processing_class=tokenizer,
)

# Configuration finale
total_steps = len(train_split) // 16 * 2  # 16 = gradient_accumulation_steps
print("\n🎯 Configuration OPTIMISÉE:")
print(f"  📊 LoRA rank: 48 (haute capacité)")
print(f"  🧠 Learning rate: 1e-5 (optimisé)")
print(f"  🔄 Epochs: 2 SEULEMENT")
print(f"  📈 Steps totaux: ~{total_steps}")
print(f"  💾 Batch effectif: 16")
print(f"  📁 Sauvegarde: {output_dir}")
print(f"  ⏱️ Temps estimé: 25-35 minutes")
print(f"  🎯 Fidélité attendue: >60% (optimisé vitesse/qualité)")

# Entraînement
print("\n🚀 Début training optimisé...")

try:
    train_result = trainer.train()
    
    # Sauvegarder
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    # Évaluation finale
    eval_results = trainer.evaluate()
    
    print(f"\n✅ TRAINING OPTIMISÉ TERMINÉ!")
    print(f"📊 Train loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
    print(f"📊 Eval loss finale: {eval_results.get('eval_loss', 'N/A')}")
    print(f"📁 Modèle: {output_dir}")
    
    # Sauvegarder métriques
    import json
    final_metrics = {
        'config': 'optimized_2_epochs',
        'train_metrics': train_result.metrics,
        'eval_metrics': eval_results,
        'epochs': 2,
        'lora_rank': 48,
        'learning_rate': 1e-5,
        'total_steps': total_steps
    }
    
    with open(Path(output_dir) / "optimized_metrics.json", 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    print(f"🧪 Testez avec: python test_optimized_model.py")
    
except Exception as e:
    print(f"❌ Erreur: {e}")

print("\n🎉 TRAINING OPTIMISÉ TERMINÉ!")
print("🏦 Chatbot bancaire optimisé (2 epochs) pour fidélité/vitesse!")
print("📊 Configuration:")
print("  - 2 epochs (au lieu de 5)")
print("  - LoRA rank 48 (au lieu de 64)")
print("  - Learning rate 1e-5")
print("  - Temps réduit de 60%")
print("🎯 Fidélité attendue: 60-70% avec le dataset original")
