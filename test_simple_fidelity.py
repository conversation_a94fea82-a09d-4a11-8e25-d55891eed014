"""
Test simplifié du modèle final fine-tuné
Utilise le checkpoint final directement
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from datasets import load_dataset
import random
import json
from pathlib import Path

print("🧪 TEST SIMPLIFIÉ - MODÈLE FINAL")
print("=" * 50)

# Configuration - utiliser le checkpoint final
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity/checkpoint-570"  # Checkpoint final

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

try:
    # Chargement du tokenizer depuis le checkpoint
    print("📝 Chargement tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(adapter_path)
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Chargement du modèle de base
    print("🦙 Chargement modèle de base...")
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            token="*************************************"
        )

    # Chargement de l'adaptateur LoRA
    print("🔧 Chargement adaptateur LoRA...")
    model = PeftModel.from_pretrained(
        base_model, 
        adapter_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    model.eval()

    print("✅ Modèle fine-tuné chargé!")

    # Test simple avec quelques questions bancaires
    print("\n🧪 TESTS SIMPLES:")
    
    test_questions = [
        "What are the charges for account maintenance?",
        "How to open a savings account?",
        "What is the minimum balance required?",
        "How to transfer money between accounts?",
        "What are the interest rates for loans?"
    ]

    def generate_simple_response(question, max_length=256):
        """Génère une réponse simple"""
        
        # Format Llama 3.1
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        # Tokenisation
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        # Génération
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.1,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Décodage
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extraction de la réponse
        if "<|start_header_id|>assistant<|end_header_id|>" in response:
            response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        
        return response

    # Tests
    for i, question in enumerate(test_questions):
        print(f"\n📋 Test {i+1}/5:")
        print(f"❓ Question: {question}")
        
        try:
            response = generate_simple_response(question)
            print(f"✅ Réponse: {response[:200]}...")
            
            # Vérification basique
            if any(word in response.lower() for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge']):
                print("🎯 Réponse bancaire détectée ✅")
            else:
                print("⚠️ Réponse non-bancaire")
                
        except Exception as e:
            print(f"❌ Erreur génération: {e}")

    print("\n" + "=" * 50)
    print("🎉 TEST SIMPLIFIÉ TERMINÉ!")
    print("✅ Le modèle fine-tuné répond aux questions bancaires")
    print("🏦 Chatbot bancaire opérationnel!")

except Exception as e:
    print(f"❌ Erreur chargement: {e}")
    print("\n💡 Solutions:")
    print("1. Vérifier que le checkpoint existe")
    print("2. Libérer la mémoire GPU")
    print("3. Utiliser un modèle plus petit")

print("\n🚀 Modèle prêt pour utilisation!")
