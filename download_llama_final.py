"""
Téléchargement final de Llama-3.1-8B-Instruct
Version simple et efficace
"""

import os
from pathlib import Path

def main():
    print("🦙 Téléchargement Llama-3.1-8B-Instruct")
    print("CUDA 12.9 détecté - Compatible ✅")
    print("=" * 50)
    
    # Configuration
    token = "*************************************"
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = "./models/Llama-3.1-8B-Instruct"
    
    # Créer le répertoire
    Path(local_dir).mkdir(parents=True, exist_ok=True)
    
    print(f"📥 Modèle: {model_id}")
    print(f"📁 Destination: {local_dir}")
    print(f"💾 Taille: ~16 GB")
    print(f"🎮 GPU: RTX 4060 (8GB) - Quantification 4-bit requise")
    
    # Vérifier si déjà téléchargé
    existing_files = list(Path(local_dir).glob("*"))
    if existing_files:
        total_size = sum(f.stat().st_size for f in existing_files if f.is_file()) / (1024**3)
        print(f"\n📊 Téléchargement existant: {total_size:.1f} GB")
        
        if total_size > 15:
            choice = input("Modèle semble complet. Re-télécharger? (y/n): ")
            if choice.lower() != 'y':
                print("✅ Utilisation du modèle existant")
                return
    
    confirm = input(f"\nCommencer le téléchargement? (y/n): ")
    if confirm.lower() != 'y':
        print("Téléchargement annulé")
        return
    
    try:
        from huggingface_hub import snapshot_download
        
        print(f"\n🚀 Téléchargement en cours...")
        print(f"💡 Cela peut prendre 1-2 heures selon votre connexion")
        
        # Téléchargement
        snapshot_download(
            repo_id=model_id,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True,
            token=token
        )
        
        print(f"\n✅ Téléchargement terminé!")
        
        # Vérification
        files = list(Path(local_dir).glob("*"))
        total_size = sum(f.stat().st_size for f in files if f.is_file()) / (1024**3)
        
        print(f"📊 Résultat:")
        print(f"  📄 Fichiers: {len(files)}")
        print(f"  💾 Taille: {total_size:.1f} GB")
        
        # Fichiers importants
        important_files = [
            "config.json",
            "tokenizer.model", 
            "model-00001-of-00004.safetensors",
            "model-00002-of-00004.safetensors",
            "model-00003-of-00004.safetensors", 
            "model-00004-of-00004.safetensors"
        ]
        
        print(f"\n📋 Fichiers essentiels:")
        for file_name in important_files:
            file_path = Path(local_dir) / file_name
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024**2)
                print(f"  ✅ {file_name} ({size_mb:.0f} MB)")
            else:
                print(f"  ❌ {file_name} manquant")
        
        # Créer config pour le fine-tuning
        import json
        config = {
            "model_id": model_id,
            "local_path": local_dir,
            "cuda_version": "12.9",
            "gpu": "RTX 4060 8GB",
            "quantization_required": True,
            "ready_for_finetuning": True
        }
        
        with open("model_config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n🎯 Modèle prêt pour le fine-tuning!")
        print(f"✅ Configuration sauvée: model_config.json")
        print(f"\n🚀 Prochaines étapes:")
        print(f"  1. python prepare_banking_dataset.py")
        print(f"  2. python llama_finetuning_cuda.py")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        print(f"💡 Vérifiez votre connexion et réessayez")

if __name__ == "__main__":
    main()
