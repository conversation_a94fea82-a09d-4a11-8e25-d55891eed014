"""
Test de fidélité du modèle fine-tuné par rapport au dataset wasifis/bank-assistant-qa
Mesure la précision et la cohérence des réponses
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from datasets import load_dataset
import time
import random
import json
from difflib import SequenceMatcher
import re

print("🎯 TEST DE FIDÉLITÉ - DATASET BANCAIRE")
print("=" * 60)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Vérifications
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Vider le cache GPU
torch.cuda.empty_cache()

# Configuration quantization
quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

def load_finetuned_model():
    """Charge le modèle fine-tuné optimisé"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle fine-tuné...")
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            token="*************************************"
        )
    
    # Charger l'adaptateur LoRA
    finetuned_model = PeftModel.from_pretrained(
        base_model, 
        adapter_path,
        torch_dtype=torch.float16
    )
    
    print("✅ Modèle fine-tuné chargé!")
    return tokenizer, finetuned_model

# Chargement du modèle
tokenizer, model = load_finetuned_model()

# Chargement du dataset original
print("\n📊 Chargement dataset wasifis/bank-assistant-qa...")
dataset = load_dataset("wasifis/bank-assistant-qa")
train_data = dataset["train"]
print(f"✅ Dataset chargé: {len(train_data)} exemples")

def generate_response(question, max_length=200):
    """Génère une réponse avec le modèle fine-tuné"""
    
    prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
    model_device = next(model.parameters()).device
    inputs = {k: v.to(model_device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            temperature=0.1,  # Très bas pour fidélité maximale
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            repetition_penalty=1.1
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    if "<|start_header_id|>assistant<|end_header_id|>" in response:
        response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
    
    return response

def calculate_similarity(generated, expected):
    """Calcule la similarité entre deux textes"""
    
    # Nettoyer les textes
    gen_clean = re.sub(r'[^\w\s]', '', generated.lower())
    exp_clean = re.sub(r'[^\w\s]', '', expected.lower())
    
    # Similarité de séquence
    seq_similarity = SequenceMatcher(None, gen_clean, exp_clean).ratio()
    
    # Mots communs
    gen_words = set(gen_clean.split())
    exp_words = set(exp_clean.split())
    
    if len(exp_words) == 0:
        word_similarity = 0.0
    else:
        common_words = gen_words.intersection(exp_words)
        word_similarity = len(common_words) / len(exp_words)
    
    # Score combiné
    combined_score = (seq_similarity * 0.3) + (word_similarity * 0.7)
    
    return {
        "sequence_similarity": seq_similarity,
        "word_similarity": word_similarity,
        "combined_score": combined_score,
        "common_words": len(gen_words.intersection(exp_words)),
        "expected_words": len(exp_words)
    }

def evaluate_banking_relevance(response):
    """Évalue la pertinence bancaire d'une réponse"""
    banking_keywords = [
        'account', 'bank', 'balance', 'transfer', 'loan', 'interest', 
        'charge', 'fee', 'credit', 'debit', 'deposit', 'withdrawal',
        'savings', 'checking', 'mortgage', 'payment', 'transaction',
        'branch', 'atm', 'card', 'statement', 'overdraft', 'customer',
        'service', 'application', 'minimum', 'maximum', 'annual'
    ]
    
    response_lower = response.lower()
    matches = [word for word in banking_keywords if word in response_lower]
    
    return {
        "banking_score": len(matches),
        "banking_words": matches,
        "relevance_percentage": min(100, len(matches) * 4)  # Max 100%
    }

# Sélection d'exemples de test
print("\n🎲 Sélection d'exemples de test...")
test_size = min(20, len(train_data))  # Tester 20 exemples maximum
test_samples = random.sample(list(train_data), test_size)
print(f"📋 {test_size} exemples sélectionnés pour le test")

# Tests de fidélité
print("\n" + "="*60)
print("🧪 DÉBUT DES TESTS DE FIDÉLITÉ")
print("="*60)

results = []
total_similarity = 0
total_banking_score = 0

for i, sample in enumerate(test_samples):
    question = sample["input"]
    expected = sample["output"]
    
    print(f"\n📋 Test {i+1}/{test_size}:")
    print(f"❓ Question: {question[:80]}...")
    
    # Génération
    start_time = time.time()
    generated = generate_response(question)
    generation_time = time.time() - start_time
    
    # Calcul de similarité
    similarity = calculate_similarity(generated, expected)
    
    # Évaluation bancaire
    banking_eval = evaluate_banking_relevance(generated)
    
    print(f"⏱️ Temps: {generation_time:.2f}s")
    print(f"📊 Similarité: {similarity['combined_score']:.2%}")
    print(f"🏦 Score bancaire: {banking_eval['banking_score']} mots-clés")
    print(f"✅ Généré: {generated[:100]}...")
    print(f"🎯 Attendu: {expected[:100]}...")
    
    # Évaluation qualitative
    if similarity['combined_score'] >= 0.7:
        print("🏆 EXCELLENT: Très haute fidélité")
    elif similarity['combined_score'] >= 0.5:
        print("✅ BON: Bonne fidélité")
    elif similarity['combined_score'] >= 0.3:
        print("⚠️ MOYEN: Fidélité modérée")
    else:
        print("❌ FAIBLE: Fidélité insuffisante")
    
    total_similarity += similarity['combined_score']
    total_banking_score += banking_eval['banking_score']
    
    results.append({
        "question": question,
        "expected": expected,
        "generated": generated,
        "generation_time": generation_time,
        "similarity": similarity,
        "banking_eval": banking_eval
    })

# Analyse finale
print("\n" + "="*60)
print("📊 ANALYSE FINALE DE FIDÉLITÉ")
print("="*60)

avg_similarity = total_similarity / len(test_samples)
avg_banking_score = total_banking_score / len(test_samples)

# Distribution des scores
excellent_count = sum(1 for r in results if r["similarity"]["combined_score"] >= 0.7)
good_count = sum(1 for r in results if 0.5 <= r["similarity"]["combined_score"] < 0.7)
moderate_count = sum(1 for r in results if 0.3 <= r["similarity"]["combined_score"] < 0.5)
poor_count = sum(1 for r in results if r["similarity"]["combined_score"] < 0.3)

print(f"🎯 FIDÉLITÉ MOYENNE: {avg_similarity:.2%}")
print(f"🏦 SCORE BANCAIRE MOYEN: {avg_banking_score:.1f} mots-clés")

print(f"\n📊 DISTRIBUTION DES RÉSULTATS:")
print(f"  🏆 Excellent (≥70%): {excellent_count}/{test_size} ({excellent_count/test_size*100:.1f}%)")
print(f"  ✅ Bon (50-69%): {good_count}/{test_size} ({good_count/test_size*100:.1f}%)")
print(f"  ⚠️ Moyen (30-49%): {moderate_count}/{test_size} ({moderate_count/test_size*100:.1f}%)")
print(f"  ❌ Faible (<30%): {poor_count}/{test_size} ({poor_count/test_size*100:.1f}%)")

# Verdict final
print(f"\n🏆 VERDICT FINAL:")
if avg_similarity >= 0.7:
    print(f"🥇 EXCELLENT: Fine-tuning très réussi! Fidélité exceptionnelle.")
elif avg_similarity >= 0.5:
    print(f"🥈 BON: Fine-tuning réussi! Bonne fidélité au dataset.")
elif avg_similarity >= 0.3:
    print(f"🥉 MOYEN: Fine-tuning partiellement réussi. Amélioration possible.")
else:
    print(f"❌ FAIBLE: Fine-tuning à améliorer. Fidélité insuffisante.")

print(f"\n📈 PERFORMANCE:")
print(f"  ⏱️ Temps moyen: {sum(r['generation_time'] for r in results)/len(results):.2f}s")
print(f"  🎯 Fidélité: {avg_similarity:.2%}")
print(f"  🏦 Spécialisation bancaire: {avg_banking_score:.1f}/25 mots-clés")

# Sauvegarde des résultats
results_file = "fidelity_test_results.json"
with open(results_file, 'w', encoding='utf-8') as f:
    json.dump({
        "summary": {
            "avg_similarity": avg_similarity,
            "avg_banking_score": avg_banking_score,
            "test_size": test_size,
            "excellent_count": excellent_count,
            "good_count": good_count,
            "moderate_count": moderate_count,
            "poor_count": poor_count
        },
        "detailed_results": results
    }, f, indent=2, ensure_ascii=False)

print(f"\n💾 Résultats détaillés sauvegardés: {results_file}")
print(f"🎉 TEST DE FIDÉLITÉ TERMINÉ!")
print(f"🏦 Votre chatbot bancaire a une fidélité de {avg_similarity:.2%} au dataset original!")
