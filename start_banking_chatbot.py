"""
Script de démarrage optimisé pour le chatbot bancaire
Gère automatiquement le fallback vers la version rapide
"""

import subprocess
import sys
import time
import psutil
import torch

def check_gpu_memory():
    """Vérifie la mémoire GPU disponible"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        gpu_memory_gb = gpu_memory / (1024**3)
        print(f"🚀 GPU détecté: {torch.cuda.get_device_name()}")
        print(f"💾 Mémoire GPU: {gpu_memory_gb:.1f} GB")
        return gpu_memory_gb > 6  # Besoin d'au moins 6GB
    return False

def check_system_resources():
    """Vérifie les ressources système"""
    # RAM disponible
    ram = psutil.virtual_memory()
    ram_available_gb = ram.available / (1024**3)
    
    # Espace disque
    disk = psutil.disk_usage('.')
    disk_free_gb = disk.free / (1024**3)
    
    print(f"💾 RAM disponible: {ram_available_gb:.1f} GB")
    print(f"💿 Espace disque libre: {disk_free_gb:.1f} GB")
    
    return ram_available_gb > 8 and disk_free_gb > 15

def start_backend(use_fast=False):
    """Démarre le backend approprié"""
    if use_fast:
        print("🚀 Démarrage version RAPIDE...")
        script = "backend_rag_fast.py"
    else:
        print("🚀 Démarrage version COMPLÈTE...")
        script = "backend_rag_api.py"
    
    try:
        process = subprocess.Popen([sys.executable, script])
        return process
    except Exception as e:
        print(f"❌ Erreur démarrage {script}: {e}")
        return None

def main():
    print("🏦 DÉMARRAGE CHATBOT BANCAIRE")
    print("=" * 50)
    
    # Vérifications système
    has_gpu = check_gpu_memory()
    has_resources = check_system_resources()
    
    print("\n🔍 DIAGNOSTIC:")
    print(f"✅ GPU adapté: {'Oui' if has_gpu else 'Non'}")
    print(f"✅ Ressources suffisantes: {'Oui' if has_resources else 'Non'}")
    
    # Décision automatique
    if has_gpu and has_resources:
        print("\n🎯 RECOMMANDATION: Version complète")
        print("⏱️ Temps de démarrage: 5-15 minutes")
        print("🏆 Qualité: Excellente")
        
        choice = input("\n🤔 Utiliser version complète? (o/n/auto): ").lower()
        
        if choice in ['o', 'oui', 'y', 'yes', '']:
            use_fast = False
        elif choice == 'auto':
            # Essayer version complète avec timeout
            print("\n🚀 Tentative version complète (timeout 3 minutes)...")
            process = start_backend(use_fast=False)
            if process:
                try:
                    # Attendre 3 minutes max
                    process.wait(timeout=180)
                    return
                except subprocess.TimeoutExpired:
                    print("⏰ Timeout - Basculement vers version rapide...")
                    process.terminate()
                    use_fast = True
            else:
                use_fast = True
        else:
            use_fast = True
    else:
        print("\n🎯 RECOMMANDATION: Version rapide")
        print("⏱️ Temps de démarrage: 10-30 secondes")
        print("⭐ Qualité: Bonne pour tests")
        use_fast = True
    
    # Démarrage final
    print(f"\n🚀 Démarrage version {'RAPIDE' if use_fast else 'COMPLÈTE'}...")
    process = start_backend(use_fast=use_fast)
    
    if process:
        print(f"✅ Backend démarré!")
        print(f"🌐 API: http://localhost:8000")
        print(f"📱 Interface: Démarrez le frontend séparément")
        print(f"\n💡 Pour le frontend:")
        print(f"   cd \"chat-bank-nexus-main(frontend v0)\"")
        print(f"   npm run dev")
        
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Arrêt du serveur...")
            process.terminate()
    else:
        print("❌ Impossible de démarrer le backend")
        print("💡 Vérifiez les dépendances et l'installation")

if __name__ == "__main__":
    main()
