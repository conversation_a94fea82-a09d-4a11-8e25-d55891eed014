"""
Test avec quantization 4-bit pour économiser la mémoire GPU
Solution optimale pour RTX 4060 Laptop 8GB
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
import time

print("🏦 TEST QUANTIZED - CHATBOT BANCAIRE")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Vérification GPU
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Vider le cache GPU
torch.cuda.empty_cache()

# Configuration quantization 4-bit
quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

def load_quantized_model():
    """Charge le modèle avec quantization 4-bit"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle quantized 4-bit...")
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            token="*************************************"
        )
    
    print("✅ Modèle quantized chargé")
    
    print("🔧 Chargement adaptateur LoRA...")
    try:
        finetuned_model = PeftModel.from_pretrained(
            base_model, 
            adapter_path,
            torch_dtype=torch.float16
        )
        print("✅ Modèle fine-tuné chargé avec succès!")
        return tokenizer, finetuned_model
    except Exception as e:
        print(f"⚠️ Erreur adaptateur principal: {e}")
        try:
            checkpoint_path = f"{adapter_path}/checkpoint-570"
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                checkpoint_path,
                torch_dtype=torch.float16
            )
            print("✅ Modèle fine-tuné chargé (checkpoint)!")
            return tokenizer, finetuned_model
        except Exception as e2:
            print(f"❌ Impossible de charger le fine-tuné: {e2}")
            print("💡 Utilisation du modèle de base quantized")
            return tokenizer, base_model

# Chargement du modèle
try:
    tokenizer, model = load_quantized_model()
    
    # Vérification mémoire GPU
    print(f"\n💾 Mémoire GPU utilisée: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    print(f"💾 Mémoire GPU libre: {(torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()) / 1024**3:.2f} GB")
    
    def generate_response(question, max_length=150):
        """Génère une réponse"""
        
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.1,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        generation_time = time.time() - start_time
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        if "<|start_header_id|>assistant<|end_header_id|>" in response:
            response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        
        return response, generation_time

    # Test simple
    print("\n🧪 TEST SIMPLE:")
    question = "What are the charges for account maintenance?"
    print(f"❓ Question: {question}")
    
    response, response_time = generate_response(question)
    
    banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                       if word in response.lower())
    
    print(f"⏱️ Temps: {response_time:.2f}s")
    print(f"🎯 Termes bancaires: {banking_words}")
    print(f"💬 Réponse: {response}")
    
    print(f"\n✅ SUCCÈS! Modèle quantized fonctionne!")
    print(f"💾 Mémoire finale: {torch.cuda.memory_allocated() / 1024**3:.2f} GB utilisés")
    
    # Test interactif optionnel
    print(f"\n💬 Test interactif? (y/n)")
    if input().lower() == 'y':
        while True:
            user_question = input("\n❓ Question (ou 'quit'): ").strip()
            if user_question.lower() in ['quit', 'q']:
                break
            if user_question:
                response, time_taken = generate_response(user_question)
                print(f"⏱️ {time_taken:.2f}s | 💬 {response}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n💡 SOLUTIONS ALTERNATIVES:")
    print("1. Installer bitsandbytes: pip install bitsandbytes")
    print("2. Utiliser un modèle plus petit (Llama 3.1 1B)")
    print("3. Utiliser CPU seulement (plus lent)")
    print("4. Augmenter la mémoire virtuelle Windows")

print(f"\n🎯 DIAGNOSTIC COMPLET TERMINÉ!")
