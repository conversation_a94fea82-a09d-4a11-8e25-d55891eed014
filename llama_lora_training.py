"""
Fine-tuning LoRA spécifique pour Llama 3.1-8B
"""

import os
import torch
import json
from datasets import load_from_disk
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
import ollama
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

class LlamaLoRATrainer:
    """Classe pour le fine-tuning LoRA de Llama 3.1"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.ollama_client = ollama.Client()
        
    def check_requirements(self):
        """Vérifie les prérequis"""
        print("=== Vérification des prérequis ===")
        
        # Vérifier le token HF
        hf_token = os.getenv('HF_TOKEN')
        if not hf_token or hf_token.startswith('hf_xxx'):
            print("❌ Token Hugging Face non configuré")
            print("Configurez HF_TOKEN dans le fichier .env")
            return False
        
        print("✅ Token Hugging Face configuré")
        
        # Vérifier Ollama
        try:
            models = self.ollama_client.list()
            llama_found = any('llama3.1:8b' in model.get('name', '') for model in models['models'])
            if llama_found:
                print("✅ llama3.1:8b trouvé dans Ollama")
            else:
                print("⚠️ llama3.1:8b non trouvé dans Ollama")
        except:
            print("⚠️ Ollama non accessible")
        
        # Vérifier CUDA
        if torch.cuda.is_available():
            print(f"✅ CUDA disponible: {torch.cuda.get_device_name()}")
        else:
            print("⚠️ CUDA non disponible, utilisation du CPU (plus lent)")
        
        return True
    
    def load_llama_model(self):
        """Charge le modèle Llama 3.1"""
        print("\n=== Chargement de Llama 3.1-8B ===")
        
        model_name = "meta-llama/Meta-Llama-3.1-8B"
        
        try:
            # Charger le tokenizer
            print("Chargement du tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                use_auth_token=True
            )
            
            # Ajouter le token de padding
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            print("✅ Tokenizer Llama chargé")
            
            # Charger le modèle avec optimisations mémoire
            print("Chargement du modèle (cela peut prendre du temps)...")
            
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                use_auth_token=True,
                low_cpu_mem_usage=True,
            )
            
            print(f"✅ Modèle Llama chargé: {self.model.num_parameters():,} paramètres")
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            print("\nVérifiez que:")
            print("1. Vous avez l'accès au modèle Llama 3.1")
            print("2. Votre token est valide: huggingface-cli login")
            return False
    
    def create_lora_config(self):
        """Crée la configuration LoRA pour Llama"""
        print("\n=== Configuration LoRA pour Llama ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=int(os.getenv('LORA_RANK', 16)),
            lora_alpha=int(os.getenv('LORA_ALPHA', 32)),
            lora_dropout=float(os.getenv('LORA_DROPOUT', 0.1)),
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],  # Modules spécifiques à Llama
            bias="none",
        )
        
        print(f"Configuration LoRA:")
        print(f"  - Rank: {lora_config.r}")
        print(f"  - Alpha: {lora_config.lora_alpha}")
        print(f"  - Dropout: {lora_config.lora_dropout}")
        print(f"  - Target modules: {lora_config.target_modules}")
        
        return lora_config
    
    def apply_lora(self, lora_config):
        """Applique LoRA au modèle Llama"""
        print("\n=== Application LoRA ===")
        
        try:
            self.peft_model = get_peft_model(self.model, lora_config)
            self.peft_model.print_trainable_parameters()
            print("✅ LoRA appliqué avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur LoRA: {e}")
            return False
    
    def load_and_prepare_data(self):
        """Charge et prépare les données"""
        print("\n=== Préparation des données ===")
        
        try:
            # Charger les données formatées
            dataset = load_from_disk("./formatted_data")
            print(f"Dataset chargé: {len(dataset['train'])} exemples")
            
            # Tokeniser
            def tokenize_function(examples):
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,
                    max_length=512,  # Longueur raisonnable pour Llama
                    return_tensors=None
                )
                tokenized["labels"] = tokenized["input_ids"].copy()
                return tokenized
            
            print("Tokenisation en cours...")
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=dataset["train"].column_names,
                desc="Tokenisation"
            )
            
            print("✅ Données préparées")
            return tokenized_dataset
            
        except Exception as e:
            print(f"❌ Erreur de préparation des données: {e}")
            return None
    
    def train_model(self, dataset):
        """Lance l'entraînement"""
        print("\n=== Entraînement LoRA ===")
        
        # Configuration d'entraînement optimisée pour Llama
        training_args = TrainingArguments(
            output_dir="./llama_lora_results",
            num_train_epochs=3,
            per_device_train_batch_size=1,  # Petit batch pour économiser la mémoire
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=8,  # Compenser le petit batch
            learning_rate=2e-4,
            weight_decay=0.01,
            warmup_steps=100,
            logging_steps=10,
            eval_steps=100,
            save_steps=100,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,
            remove_unused_columns=False,
            dataloader_pin_memory=False,
            fp16=torch.cuda.is_available(),  # Utiliser FP16 si CUDA disponible
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["validation"],
            data_collator=data_collator,
        )
        
        print("Démarrage de l'entraînement...")
        try:
            trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained("./llama_lora_results")
            
            print("✅ Entraînement terminé et modèle sauvegardé")
            return True
            
        except Exception as e:
            print(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_fine_tuned_model(self):
        """Teste le modèle fine-tuné"""
        print("\n=== Test du modèle fine-tuné ===")
        
        test_prompts = [
            "Question: Quels sont les frais de tenue de compte? Réponse:",
            "Question: Comment ouvrir un compte épargne? Réponse:",
            "Question: Quelles sont les conditions pour un prêt immobilier? Réponse:"
        ]
        
        for prompt in test_prompts:
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\nQ: {prompt.replace('Question: ', '').replace(' Réponse:', '')}")
            print(f"R: {response}")

def main():
    """Fonction principale"""
    print("=== Fine-tuning LoRA pour Llama 3.1-8B ===\n")
    
    trainer = LlamaLoRATrainer()
    
    # 1. Vérifier les prérequis
    if not trainer.check_requirements():
        print("\n❌ Prérequis non satisfaits. Exécutez d'abord:")
        print("python setup_llama_access.py")
        return
    
    # 2. Charger le modèle
    if not trainer.load_llama_model():
        return
    
    # 3. Configurer LoRA
    lora_config = trainer.create_lora_config()
    
    # 4. Appliquer LoRA
    if not trainer.apply_lora(lora_config):
        return
    
    # 5. Préparer les données
    dataset = trainer.load_and_prepare_data()
    if dataset is None:
        return
    
    # 6. Entraîner
    print("\nVoulez-vous lancer l'entraînement? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if trainer.train_model(dataset):
            print("\n✅ Fine-tuning LoRA terminé avec succès!")
            trainer.test_fine_tuned_model()
        else:
            print("\n❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé. Configuration LoRA prête.")

if __name__ == "__main__":
    main()
