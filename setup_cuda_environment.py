"""
Configuration de l'environnement CUDA pour fine-tuning Llama 3.1 8B
"""

import subprocess
import sys
import torch
import os

def check_cuda_availability():
    """Vérifie la disponibilité de CUDA"""
    print("=== Vérification CUDA ===")
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA disponible: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"Version CUDA: {torch.version.cuda}")
        print(f"Nombre de GPUs: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_props = torch.cuda.get_device_properties(i)
            print(f"GPU {i}: {gpu_props.name}")
            print(f"  - Mémoire totale: {gpu_props.total_memory / 1024**3:.1f} GB")
            print(f"  - Compute capability: {gpu_props.major}.{gpu_props.minor}")
        
        # Test simple CUDA
        try:
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ Test CUDA réussi")
            return True
        except Exception as e:
            print(f"❌ Erreur test CUDA: {e}")
            return False
    else:
        print("❌ CUDA non disponible")
        return False

def install_cuda_dependencies():
    """Installe les dépendances CUDA optimisées"""
    print("\n=== Installation des dépendances CUDA ===")
    
    dependencies = [
        "torch>=2.1.0",
        "torchvision>=0.16.0", 
        "torchaudio>=2.1.0",
        "--index-url https://download.pytorch.org/whl/cu121",  # CUDA 12.1
        "transformers>=4.36.0",
        "datasets>=2.15.0",
        "accelerate>=0.25.0",
        "peft>=0.7.0",
        "bitsandbytes>=0.41.0",
        "flash-attn>=2.3.0",  # Attention optimisée
        "xformers>=0.0.22",   # Optimisations mémoire
        "deepspeed>=0.12.0",  # Optimisations distribuées
        "wandb>=0.16.0",      # Monitoring
        "scipy>=1.11.0",
        "scikit-learn>=1.3.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0"
    ]
    
    print("Installation des packages optimisés CUDA...")
    for dep in dependencies:
        try:
            if dep.startswith("--index-url"):
                continue  # Skip URL
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Problème avec {dep}: {e}")

def optimize_cuda_settings():
    """Configure les paramètres CUDA optimaux"""
    print("\n=== Optimisation CUDA ===")
    
    # Variables d'environnement CUDA
    cuda_settings = {
        "CUDA_LAUNCH_BLOCKING": "0",  # Async pour performance
        "TORCH_CUDA_ARCH_LIST": "8.0;8.6;8.9",  # Architectures modernes
        "CUDA_VISIBLE_DEVICES": "0",  # Utiliser le premier GPU
        "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:512",  # Gestion mémoire
        "TOKENIZERS_PARALLELISM": "false",  # Éviter les warnings
    }
    
    for key, value in cuda_settings.items():
        os.environ[key] = value
        print(f"✅ {key} = {value}")
    
    # Configuration PyTorch
    if torch.cuda.is_available():
        torch.backends.cuda.matmul.allow_tf32 = True  # Performance
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True  # Optimisation auto
        print("✅ Optimisations PyTorch activées")

def create_cuda_config():
    """Crée un fichier de configuration CUDA"""
    config = {
        "cuda_settings": {
            "device": "cuda:0",
            "mixed_precision": "fp16",
            "gradient_checkpointing": True,
            "dataloader_pin_memory": True,
            "dataloader_num_workers": 4,
            "max_memory_per_gpu": "auto"
        },
        "model_settings": {
            "load_in_8bit": False,
            "load_in_4bit": True,  # Quantification 4-bit pour économiser la mémoire
            "bnb_4bit_compute_dtype": "float16",
            "bnb_4bit_use_double_quant": True,
            "bnb_4bit_quant_type": "nf4"
        },
        "training_settings": {
            "per_device_train_batch_size": 1,
            "gradient_accumulation_steps": 16,
            "max_grad_norm": 1.0,
            "warmup_ratio": 0.1,
            "lr_scheduler_type": "cosine",
            "optim": "paged_adamw_8bit"
        }
    }
    
    import json
    with open("cuda_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration CUDA sauvegardée: cuda_config.json")
    return config

def memory_benchmark():
    """Benchmark de la mémoire GPU"""
    print("\n=== Benchmark Mémoire GPU ===")
    
    if not torch.cuda.is_available():
        print("❌ CUDA non disponible")
        return
    
    device = torch.device("cuda:0")
    
    # Mémoire initiale
    torch.cuda.empty_cache()
    initial_memory = torch.cuda.memory_allocated(device)
    total_memory = torch.cuda.get_device_properties(device).total_memory
    
    print(f"Mémoire GPU totale: {total_memory / 1024**3:.1f} GB")
    print(f"Mémoire utilisée: {initial_memory / 1024**3:.3f} GB")
    print(f"Mémoire libre: {(total_memory - initial_memory) / 1024**3:.1f} GB")
    
    # Test d'allocation
    try:
        # Simuler un batch de training
        batch_size = 1
        seq_length = 2048
        hidden_size = 4096
        
        # Tenseur simulant les embeddings
        embeddings = torch.randn(batch_size, seq_length, hidden_size, device=device, dtype=torch.float16)
        
        # Tenseur simulant les gradients
        gradients = torch.randn_like(embeddings)
        
        current_memory = torch.cuda.memory_allocated(device)
        print(f"Mémoire après allocation test: {current_memory / 1024**3:.3f} GB")
        
        # Nettoyage
        del embeddings, gradients
        torch.cuda.empty_cache()
        
        print("✅ Test d'allocation réussi")
        
        # Estimation pour Llama 3.1 8B
        estimated_model_memory = 16  # GB en FP16
        estimated_training_memory = estimated_model_memory * 1.5  # Avec gradients
        
        print(f"\nEstimation pour Llama 3.1 8B:")
        print(f"  - Modèle (FP16): ~{estimated_model_memory} GB")
        print(f"  - Training: ~{estimated_training_memory} GB")
        
        if (total_memory / 1024**3) >= estimated_training_memory:
            print("✅ Mémoire suffisante pour le fine-tuning")
        else:
            print("⚠️ Mémoire limitée - utiliser quantification 4-bit")
        
    except Exception as e:
        print(f"❌ Erreur test mémoire: {e}")

def main():
    """Fonction principale"""
    print("🚀 Configuration CUDA pour Fine-tuning Llama 3.1 8B")
    print("=" * 60)
    
    # 1. Vérifier CUDA
    if not check_cuda_availability():
        print("❌ CUDA requis pour le fine-tuning")
        return
    
    # 2. Installer les dépendances
    print("\nVoulez-vous installer les dépendances CUDA? (y/n)")
    if input().lower() == 'y':
        install_cuda_dependencies()
    
    # 3. Optimiser les paramètres
    optimize_cuda_settings()
    
    # 4. Créer la configuration
    config = create_cuda_config()
    
    # 5. Benchmark mémoire
    memory_benchmark()
    
    print("\n" + "=" * 60)
    print("✅ ENVIRONNEMENT CUDA CONFIGURÉ")
    print("=" * 60)
    print("Prêt pour le fine-tuning Llama 3.1 8B!")
    print("\nProchaines étapes:")
    print("1. Préparer le dataset wasifis/bank-assistant-qa")
    print("2. Configurer le script de fine-tuning")
    print("3. Lancer l'entraînement")

if __name__ == "__main__":
    main()
