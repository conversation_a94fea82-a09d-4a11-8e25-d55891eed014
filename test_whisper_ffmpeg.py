"""
Test simple pour vérifier que Whisper + FFmpeg fonctionne
"""

import whisper
import tempfile
import subprocess
import os

def test_whisper_ffmpeg():
    print("🧪 Test Whisper + FFmpeg")
    print("=" * 40)
    
    try:
        # 1. Charger Whisper
        print("📥 Chargement Whisper...")
        model = whisper.load_model("base")
        print("✅ Whisper chargé")
        
        # 2. Tester FFmpeg
        print("🔧 Test FFmpeg...")
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg disponible")
        else:
            print("❌ FFmpeg non disponible")
            return False
        
        # 3. Créer un fichier audio test (silence)
        print("🎵 Création fichier test...")
        with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as temp_file:
            webm_path = temp_file.name
        
        # Créer 2 secondes de silence en WebM
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'anullsrc=channel_layout=mono:sample_rate=16000',
            '-t', '2', '-c:a', 'libopus', '-y', webm_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Erreur création WebM: {result.stderr}")
            return False
        
        print("✅ Fichier WebM créé")
        
        # 4. Tester transcription
        print("🎤 Test transcription...")
        result = model.transcribe(webm_path, language="fr")
        print(f"✅ Transcription réussie: '{result['text']}'")
        
        # 5. Nettoyage
        os.unlink(webm_path)
        print("🗑️ Nettoyage terminé")
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("Whisper + FFmpeg fonctionne correctement")
        return True
        
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False

if __name__ == "__main__":
    test_whisper_ffmpeg()
