"""
Test du chatbot bancaire Llama 3.1 8B fine-tuné
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from pathlib import Path

class BankingChatbotTester:
    """Testeur pour le chatbot bancaire fine-tuné"""
    
    def __init__(self, model_path="./llama_banking_fresh"):
        self.model_path = Path(model_path)
        self.base_model = None
        self.finetuned_model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def load_models(self):
        """Charge le modèle fine-tuné"""
        print("=== Chargement du modèle fine-tuné ===")
        
        try:
            # Configuration 4-bit
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Charger le tokenizer
            print("📝 Chargement du tokenizer...")
            local_model_path = "./models/Llama-3.1-8B-Instruct"
            
            if Path(local_model_path).exists():
                self.tokenizer = AutoTokenizer.from_pretrained(local_model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            print("🦙 Chargement du modèle de base...")
            if Path(local_model_path).exists():
                self.base_model = AutoModelForCausalLM.from_pretrained(
                    local_model_path,
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            else:
                self.base_model = AutoModelForCausalLM.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    token="*************************************"
                )
            
            # Charger le modèle fine-tuné
            print("🔧 Chargement des adapters LoRA...")
            self.finetuned_model = PeftModel.from_pretrained(
                self.base_model,
                self.model_path
            )
            
            print("✅ Modèle fine-tuné chargé avec succès")
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            return False
    
    def generate_response(self, question, max_length=200):
        """Génère une réponse avec le modèle fine-tuné"""
        
        # Format de chat Llama 3.1
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel. Vous aidez les clients avec leurs questions bancaires de manière précise, courtoise et professionnelle.<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = self.finetuned_model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_length,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        # Nettoyer la réponse
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        return response
    
    def test_banking_scenarios(self):
        """Teste avec des scénarios bancaires"""
        print("\n🧪 Test des scénarios bancaires")
        print("=" * 50)
        
        scenarios = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt immobilier?",
            "Comment activer ma carte bancaire?",
            "Que faire en cas de perte de carte?",
            "Quels sont les horaires d'ouverture de la banque?",
            "Comment faire un virement international?",
            "Quels documents pour ouvrir un compte?"
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 Test {i}/8:")
            print(f"❓ Question: {scenario}")
            
            try:
                response = self.generate_response(scenario)
                print(f"🤖 Réponse: {response}")
            except Exception as e:
                print(f"❌ Erreur: {e}")
            
            print("-" * 50)
    
    def interactive_test(self):
        """Test interactif"""
        print("\n💬 Test Interactif du Chatbot Bancaire")
        print("=" * 50)
        print("Posez vos questions bancaires (tapez 'quit' pour quitter)")
        print("-" * 50)
        
        while True:
            user_input = input("\n👤 Vous: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                print("👋 Au revoir!")
                break
            
            if user_input:
                try:
                    print("🤖 Assistant bancaire:", end=" ")
                    response = self.generate_response(user_input)
                    print(response)
                except Exception as e:
                    print(f"❌ Erreur: {e}")
    
    def quick_test(self):
        """Test rapide"""
        print("\n⚡ Test Rapide")
        print("=" * 20)
        
        test_question = "Quels sont les frais de compte?"
        print(f"❓ Question: {test_question}")
        
        try:
            response = self.generate_response(test_question)
            print(f"🤖 Réponse: {response}")
            print("\n✅ Test rapide réussi!")
            return True
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False

def main():
    """Fonction principale"""
    print("🏦 Test du Chatbot Bancaire Llama 3.1 8B Fine-tuné")
    print("=" * 60)
    
    # Vérifier CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA disponible: {torch.cuda.get_device_name()}")
    else:
        print("⚠️ CUDA non disponible, utilisation du CPU")
    
    # Initialiser le testeur
    tester = BankingChatbotTester()
    
    # Charger le modèle
    if not tester.load_models():
        print("❌ Impossible de charger le modèle")
        return
    
    # Menu de test
    while True:
        print("\n" + "=" * 60)
        print("OPTIONS DE TEST:")
        print("1. ⚡ Test rapide")
        print("2. 🧪 Tests des scénarios bancaires")
        print("3. 💬 Test interactif")
        print("4. 🚪 Quitter")
        print("=" * 60)
        
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == "1":
            tester.quick_test()
        
        elif choice == "2":
            tester.test_banking_scenarios()
        
        elif choice == "3":
            tester.interactive_test()
        
        elif choice == "4":
            print("👋 Au revoir!")
            break
        
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
