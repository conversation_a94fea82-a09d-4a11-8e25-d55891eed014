"""
Test de fidélité du modèle actuel avec questions exactes du dataset
"""

import torch
import random
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from datasets import load_dataset
from pathlib import Path
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def calculate_similarity(text1, text2):
    """Calcule la similarité TF-IDF"""
    try:
        vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
        tfidf_matrix = vectorizer.fit_transform([text1, text2])
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        return similarity
    except:
        return 0.0

def load_model():
    """Charge le modèle fine-tuné"""
    print("🦙 Chargement du modèle fine-tuné...")
    
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    # Tokenizer
    base_model_path = "./models/Llama-3.1-8B-Instruct"
    if Path(base_model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Modèle de base
    if Path(base_model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            token="*************************************"
        )
    
    # Modèle fine-tuné
    model = PeftModel.from_pretrained(base_model, "./llama_banking_fresh")
    
    return model, tokenizer

def generate_response(model, tokenizer, question):
    """Génère une réponse"""
    
    # Format exact du dataset
    chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are an expert bank customer service bot for NUST Bank. Provide accurate, helpful, and concise answers to banking questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    
    inputs = tokenizer(chat_prompt, return_tensors="pt").to("cuda")
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=inputs['input_ids'].shape[1] + 150,
            temperature=0.1,  # Très déterministe
            do_sample=True,
            top_p=0.9,
            repetition_penalty=1.1,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    response = generated_text[len(chat_prompt):].strip()
    
    if response.endswith("<|eot_id|>"):
        response = response[:-9].strip()
    
    return response

def test_fidelity():
    """Test de fidélité avec questions exactes"""
    print("🧪 Test de Fidélité - Modèle Actuel")
    print("=" * 50)
    
    # Charger le dataset original
    print("📊 Chargement dataset original...")
    dataset = load_dataset("wasifis/bank-assistant-qa")
    print(f"✅ {len(dataset['train'])} exemples chargés")
    
    # Charger le modèle
    model, tokenizer = load_model()
    print("✅ Modèle chargé")
    
    # Sélectionner 10 questions aléatoirement
    test_samples = random.sample(list(dataset['train']), 10)
    
    print(f"\n🔍 Test avec 10 questions exactes du dataset")
    print("=" * 60)
    
    results = []
    high_fidelity_count = 0
    
    for i, sample in enumerate(test_samples, 1):
        print(f"\n--- Test {i}/10 ---")
        print(f"❓ Question: {sample['input']}")
        print(f"✅ Réponse attendue: {sample['output'][:100]}...")
        
        # Générer la réponse
        generated_response = generate_response(model, tokenizer, sample['input'])
        print(f"🤖 Réponse générée: {generated_response[:100]}...")
        
        # Calculer la similarité
        similarity = calculate_similarity(generated_response, sample['output'])
        print(f"📊 Similarité: {similarity:.3f}")
        
        # Évaluer la fidélité
        if similarity > 0.5:
            print("✅ HAUTE FIDÉLITÉ")
            high_fidelity_count += 1
        elif similarity > 0.3:
            print("⚠️ FIDÉLITÉ MOYENNE")
        else:
            print("❌ FAIBLE FIDÉLITÉ")
        
        results.append({
            'question': sample['input'],
            'expected': sample['output'],
            'generated': generated_response,
            'similarity': similarity,
            'high_fidelity': similarity > 0.5
        })
    
    # Statistiques finales
    fidelity_rate = (high_fidelity_count / 10) * 100
    avg_similarity = sum(r['similarity'] for r in results) / len(results)
    
    print(f"\n📊 RÉSULTATS FIDÉLITÉ:")
    print(f"✅ Haute fidélité: {high_fidelity_count}/10 ({fidelity_rate:.1f}%)")
    print(f"📈 Similarité moyenne: {avg_similarity:.3f}")
    
    if fidelity_rate >= 70:
        print("🎉 EXCELLENT: Modèle très fidèle au dataset!")
    elif fidelity_rate >= 50:
        print("👍 BON: Modèle généralement fidèle")
    elif fidelity_rate >= 30:
        print("⚠️ MOYEN: Fidélité partielle")
    else:
        print("❌ FAIBLE: Fidélité insuffisante")
    
    # Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    if fidelity_rate < 50:
        print("  🔄 Re-fine-tuning recommandé avec:")
        print("    - Plus d'epochs (3-5)")
        print("    - Learning rate plus bas (1e-5)")
        print("    - Plus de steps")
    else:
        print("  ✅ Modèle acceptable")
        print("  🔧 Améliorations possibles:")
        print("    - Fine-tuning supplémentaire")
        print("    - Ajustement des hyperparamètres")
    
    return results

def test_specific_examples():
    """Test avec des exemples spécifiques"""
    print(f"\n🎯 Test avec exemples spécifiques")
    print("=" * 40)
    
    # Charger le modèle
    model, tokenizer = load_model()
    
    # Questions spécifiques
    test_questions = [
        "What are the charges for account maintenance?",
        "How to open a savings account?",
        "What documents are required for loan application?",
        "What are the processing charges?",
        "How to activate my debit card?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Question: {question}")
        response = generate_response(model, tokenizer, question)
        print(f"   Réponse: {response[:150]}...")

def main():
    """Fonction principale"""
    print("🔍 Test de Fidélité du Modèle Actuel")
    print("=" * 40)
    
    # Vérifier si le modèle existe
    if not Path("./llama_banking_fresh").exists():
        print("❌ Modèle fine-tuné non trouvé")
        print("💡 Lancez d'abord: python fresh_finetuning.py")
        return
    
    print("Options:")
    print("1. 🧪 Test fidélité complet (10 questions)")
    print("2. 🎯 Test exemples spécifiques")
    print("3. 📊 Les deux tests")
    
    choice = input("\nVotre choix (1-3): ").strip()
    
    if choice == "1":
        test_fidelity()
    elif choice == "2":
        test_specific_examples()
    elif choice == "3":
        test_fidelity()
        test_specific_examples()
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
