"""
Script pour préparer les données pour le fine-tuning LoRA
"""

import json
import os
from datasets import Dataset, DatasetDict, load_from_disk
from transformers import AutoTokenizer
from config import data_config, training_config, chatbot_config
import pandas as pd

def load_processed_data(data_dir="./processed_data"):
    """Charge les données préparées"""
    print(f"Chargement des données depuis {data_dir}...")
    
    dataset_dict = {}
    
    # Charger chaque split
    for split_name in ['train', 'validation', 'test']:
        file_path = os.path.join(data_dir, f"{split_name}.json")
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = [json.loads(line) for line in f]
            dataset_dict[split_name] = Dataset.from_list(data)
            print(f"Chargé {split_name}: {len(data)} exemples")
    
    return DatasetDict(dataset_dict)

def create_chat_prompts(dataset):
    """Crée des prompts de chat structurés pour le fine-tuning"""
    print("Création des prompts de chat...")
    
    def format_chat_prompt(example):
        # Format de conversation pour Llama
        system_prompt = chatbot_config.system_prompt
        user_message = example['input']
        assistant_message = example['output']
        
        # Format de chat Llama 3.1
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

{assistant_message}<|eot_id|>"""
        
        return {
            "text": chat_prompt,
            "input": user_message,
            "output": assistant_message
        }
    
    formatted_dataset = {}
    for split_name, split_data in dataset.items():
        formatted_split = split_data.map(format_chat_prompt)
        formatted_dataset[split_name] = formatted_split
        print(f"Split '{split_name}' formaté: {len(formatted_split)} exemples")
    
    return DatasetDict(formatted_dataset)

def tokenize_dataset(dataset, model_name="meta-llama/Llama-3.1-8B"):
    """Tokenise le dataset pour l'entraînement"""
    print(f"Tokenisation avec le modèle {model_name}...")
    
    try:
        # Charger le tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        # Ajouter un token de padding si nécessaire
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        def tokenize_function(examples):
            # Tokeniser les textes
            tokenized = tokenizer(
                examples["text"],
                truncation=True,
                padding=False,  # Pas de padding ici, on le fera plus tard
                max_length=training_config.max_length,
                return_tensors=None
            )
            
            # Ajouter les labels (même que input_ids pour le language modeling)
            tokenized["labels"] = tokenized["input_ids"].copy()
            
            return tokenized
        
        # Tokeniser chaque split
        tokenized_dataset = {}
        for split_name, split_data in dataset.items():
            tokenized_split = split_data.map(
                tokenize_function,
                batched=True,
                remove_columns=split_data.column_names
            )
            tokenized_dataset[split_name] = tokenized_split
            print(f"Split '{split_name}' tokenisé: {len(tokenized_split)} exemples")
        
        return DatasetDict(tokenized_dataset), tokenizer
        
    except Exception as e:
        print(f"Erreur lors de la tokenisation: {e}")
        print("Utilisation d'un tokenizer de base...")
        
        # Fallback: utiliser un tokenizer plus simple
        from transformers import LlamaTokenizer
        tokenizer = LlamaTokenizer.from_pretrained("huggingface/CodeBERTa-small-v1")
        
        def simple_tokenize(examples):
            return {"text": examples["text"]}
        
        return dataset.map(simple_tokenize, batched=True), tokenizer

def filter_and_clean_data(dataset):
    """Filtre et nettoie les données"""
    print("Filtrage et nettoyage des données...")
    
    def filter_function(example):
        # Filtrer les exemples trop courts ou trop longs
        text_length = len(example['text'])
        return 50 <= text_length <= training_config.max_length * 4  # Approximation
    
    def clean_function(example):
        # Nettoyer le texte
        text = example['text']
        # Supprimer les caractères de contrôle
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        # Normaliser les espaces
        text = ' '.join(text.split())
        
        return {"text": text}
    
    cleaned_dataset = {}
    for split_name, split_data in dataset.items():
        # Nettoyer
        cleaned_split = split_data.map(clean_function)
        # Filtrer
        filtered_split = cleaned_split.filter(filter_function)
        
        cleaned_dataset[split_name] = filtered_split
        
        original_size = len(split_data)
        final_size = len(filtered_split)
        print(f"Split '{split_name}': {original_size} -> {final_size} exemples ({final_size/original_size*100:.1f}%)")
    
    return DatasetDict(cleaned_dataset)

def save_training_data(dataset, tokenizer, output_dir="./training_data"):
    """Sauvegarde les données d'entraînement"""
    print(f"Sauvegarde des données d'entraînement dans {output_dir}...")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Sauvegarder le dataset
    dataset.save_to_disk(os.path.join(output_dir, "dataset"))
    
    # Sauvegarder le tokenizer
    tokenizer.save_pretrained(os.path.join(output_dir, "tokenizer"))
    
    # Sauvegarder les statistiques
    stats = {
        "splits": {name: len(data) for name, data in dataset.items()},
        "total_examples": sum(len(data) for data in dataset.values()),
        "max_length": training_config.max_length,
        "vocab_size": tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else "unknown"
    }
    
    with open(os.path.join(output_dir, "training_stats.json"), 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"Données d'entraînement sauvegardées dans {output_dir}")
    return output_dir

def create_sample_prompts():
    """Crée quelques exemples de prompts pour tester"""
    print("Création d'exemples de prompts...")
    
    sample_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment puis-je ouvrir un compte épargne?",
        "Quelles sont les conditions pour un prêt immobilier?",
        "Comment activer ma carte bancaire?",
        "Quels sont les horaires d'ouverture de la banque?"
    ]
    
    samples = []
    for question in sample_questions:
        prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{chatbot_config.system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        samples.append({
            "question": question,
            "prompt": prompt
        })
    
    # Sauvegarder les exemples
    with open("sample_prompts.json", 'w', encoding='utf-8') as f:
        json.dump(samples, f, indent=2, ensure_ascii=False)
    
    print("Exemples de prompts sauvegardés dans sample_prompts.json")

def main():
    """Fonction principale"""
    print("=== Préparation des données pour le fine-tuning LoRA ===\n")
    
    # 1. Charger les données préparées
    dataset = load_processed_data()
    
    # 2. Créer des prompts de chat
    dataset = create_chat_prompts(dataset)
    
    # 3. Filtrer et nettoyer
    dataset = filter_and_clean_data(dataset)
    
    # 4. Tokeniser (optionnel pour maintenant)
    # dataset, tokenizer = tokenize_dataset(dataset)
    
    # 5. Sauvegarder
    # output_dir = save_training_data(dataset, tokenizer)
    
    # Pour l'instant, sauvegarder juste le dataset formaté
    dataset.save_to_disk("./formatted_data")
    
    # 6. Créer des exemples de prompts
    create_sample_prompts()
    
    print(f"\n=== Préparation terminée ===")
    print(f"Données formatées disponibles dans: ./formatted_data")
    print(f"Prêt pour la configuration LoRA!")
    
    # Afficher quelques statistiques
    print(f"\nStatistiques:")
    for split_name, split_data in dataset.items():
        print(f"  {split_name}: {len(split_data)} exemples")

if __name__ == "__main__":
    main()
