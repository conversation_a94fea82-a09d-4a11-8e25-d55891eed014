"""
Configuration file for the banking chatbot with LoRA fine-tuning
"""

import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class ModelConfig:
    """Configuration for the base model and LoRA parameters"""
    base_model_name: str = "llama3.1:8b"
    ollama_host: str = "http://localhost:11434"
    
    # LoRA configuration
    lora_r: int = 16  # Rank
    lora_alpha: int = 32  # Alpha parameter
    lora_dropout: float = 0.1
    lora_target_modules: list = None
    
    def __post_init__(self):
        if self.lora_target_modules is None:
            # Default target modules for Llama
            self.lora_target_modules = [
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ]

@dataclass
class TrainingConfig:
    """Configuration for training parameters"""
    output_dir: str = "./results"
    num_train_epochs: int = 3
    per_device_train_batch_size: int = 4
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 4
    learning_rate: float = 2e-4
    weight_decay: float = 0.01
    warmup_steps: int = 100
    logging_steps: int = 10
    eval_steps: int = 500
    save_steps: int = 500
    max_length: int = 512
    
    # Early stopping
    early_stopping_patience: int = 3
    early_stopping_threshold: float = 0.01

@dataclass
class DataConfig:
    """Configuration for data processing"""
    dataset_name: str = "wasifis/bank-assistant-qa"
    train_split: float = 0.8
    val_split: float = 0.1
    test_split: float = 0.1
    max_samples: Optional[int] = None  # None for all samples
    
    # Columns to remove
    columns_to_remove: list = None
    
    def __post_init__(self):
        if self.columns_to_remove is None:
            self.columns_to_remove = ["instruction"]  # Remove instruction column as requested

@dataclass
class ChatbotConfig:
    """Configuration for the chatbot interface"""
    max_history_length: int = 10
    temperature: float = 0.7
    max_tokens: int = 512
    system_prompt: str = """Vous êtes un assistant bancaire intelligent et serviable. 
    Répondez aux questions des clients de manière professionnelle, précise et courtoise. 
    Utilisez vos connaissances bancaires pour fournir des informations utiles et des conseils appropriés."""

# Global configuration instances
model_config = ModelConfig()
training_config = TrainingConfig()
data_config = DataConfig()
chatbot_config = ChatbotConfig()
