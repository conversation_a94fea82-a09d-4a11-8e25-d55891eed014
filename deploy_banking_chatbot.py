"""
Déploiement du chatbot bancaire Llama 3.1 fine-tuné
Interface web avec Gradio et API FastAPI
"""

import torch
import gradio as gr
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict
from pathlib import Path
import json
import time
from datetime import datetime

from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

class BankingChatbotAPI:
    """API pour le chatbot bancaire fine-tuné"""
    
    def __init__(self, model_path: str = "./llama_banking_finetuned"):
        self.model_path = Path(model_path)
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.conversation_history = []
        
        # Charger le modèle
        self.load_model()
    
    def load_model(self):
        """Charge le modèle fine-tuné"""
        print("🔄 Chargement du modèle fine-tuné...")
        
        try:
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                trust_remote_code=True
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            
            # Charger les adapters LoRA
            self.model = PeftModel.from_pretrained(base_model, self.model_path)
            
            print("✅ Modèle chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            raise
    
    def generate_response(self, user_message: str, max_length: int = 512) -> str:
        """Génère une réponse du chatbot"""
        
        # Format de chat Llama 3.1
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel. Vous aidez les clients avec leurs questions bancaires de manière précise, courtoise et professionnelle. Utilisez vos connaissances spécialisées en services bancaires pour fournir des réponses utiles et appropriées.<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_length,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        # Nettoyer la réponse
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        return response
    
    def chat(self, message: str, history: List = None) -> tuple:
        """Interface de chat pour Gradio"""
        if history is None:
            history = []
        
        try:
            # Générer la réponse
            response = self.generate_response(message)
            
            # Ajouter à l'historique
            history.append((message, response))
            
            # Sauvegarder dans l'historique interne
            self.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "user": message,
                "assistant": response
            })
            
            return history, ""
            
        except Exception as e:
            error_msg = f"Erreur: {str(e)}"
            history.append((message, error_msg))
            return history, ""
    
    def clear_history(self):
        """Efface l'historique"""
        self.conversation_history = []
        return []
    
    def save_conversation(self):
        """Sauvegarde la conversation"""
        if not self.conversation_history:
            return "Aucune conversation à sauvegarder"
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversation_banking_{timestamp}.json"
        
        conversation_data = {
            "timestamp": datetime.now().isoformat(),
            "model": "Llama-3.1-8B-Banking-FineTuned",
            "total_messages": len(self.conversation_history),
            "conversation": self.conversation_history
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, indent=2, ensure_ascii=False)
        
        return f"✅ Conversation sauvegardée: {filename}"

# Modèles Pydantic pour l'API
class ChatRequest(BaseModel):
    message: str
    max_length: Optional[int] = 512

class ChatResponse(BaseModel):
    response: str
    timestamp: str

# Initialiser le chatbot
chatbot = BankingChatbotAPI()

# FastAPI App
app = FastAPI(title="Banking Chatbot API", version="1.0.0")

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Endpoint de chat API"""
    try:
        response = chatbot.generate_response(request.message, request.max_length)
        return ChatResponse(
            response=response,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Vérification de santé"""
    return {"status": "healthy", "model_loaded": chatbot.model is not None}

def create_gradio_interface():
    """Crée l'interface Gradio"""
    
    with gr.Blocks(
        title="Chatbot Bancaire Llama 3.1",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
            margin: auto !important;
        }
        .chat-message {
            padding: 10px;
            margin: 5px 0;
            border-radius: 10px;
        }
        """
    ) as interface:
        
        gr.Markdown("# 🏦 Chatbot Bancaire Llama 3.1 Fine-tuné")
        gr.Markdown("Assistant bancaire intelligent utilisant **Llama 3.1 8B** fine-tuné avec **LoRA** sur le dataset **wasifis/bank-assistant-qa**")
        
        with gr.Row():
            with gr.Column(scale=3):
                # Zone de chat principale
                chatbot_ui = gr.Chatbot(
                    value=[],
                    label="💬 Conversation avec l'assistant bancaire",
                    height=500,
                    show_label=True,
                    container=True,
                    bubble_full_width=False,
                    avatar_images=("👤", "🤖")
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="Votre question bancaire",
                        placeholder="Ex: Quels sont les frais de tenue de compte? Comment ouvrir un compte épargne?",
                        scale=4,
                        container=False
                    )
                    send_btn = gr.Button("Envoyer", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    save_btn = gr.Button("💾 Sauvegarder", variant="secondary")
            
            with gr.Column(scale=1):
                # Panneau d'informations
                gr.Markdown("### ℹ️ Informations du modèle")
                model_info = gr.Markdown(f"""
**Modèle:** Llama 3.1 8B  
**Fine-tuning:** LoRA  
**Dataset:** wasifis/bank-assistant-qa  
**Device:** {'🚀 CUDA' if torch.cuda.is_available() else '💻 CPU'}  
**Status:** ✅ Prêt
                """)
                
                gr.Markdown("### 🎯 Questions suggérées")
                suggestions = [
                    "Frais de tenue de compte",
                    "Ouvrir un compte épargne",
                    "Conditions prêt immobilier",
                    "Activer carte bancaire",
                    "Perte de carte bancaire",
                    "Virement international",
                    "Horaires d'ouverture",
                    "Documents requis"
                ]
                
                for suggestion in suggestions:
                    suggest_btn = gr.Button(
                        suggestion, 
                        variant="outline", 
                        size="sm",
                        scale=1
                    )
                    suggest_btn.click(
                        lambda s=suggestion: s,
                        outputs=[msg]
                    )
                
                # Zone de statut
                status = gr.Textbox(
                    label="Statut",
                    value="✅ Prêt à répondre",
                    interactive=False,
                    container=False
                )
        
        # Actions des boutons
        send_btn.click(
            chatbot.chat,
            inputs=[msg, chatbot_ui],
            outputs=[chatbot_ui, msg]
        )
        
        msg.submit(
            chatbot.chat,
            inputs=[msg, chatbot_ui],
            outputs=[chatbot_ui, msg]
        )
        
        clear_btn.click(
            chatbot.clear_history,
            outputs=[chatbot_ui]
        )
        
        save_btn.click(
            chatbot.save_conversation,
            outputs=[status]
        )
    
    return interface

def main():
    """Fonction principale"""
    print("🚀 Déploiement du Chatbot Bancaire Llama 3.1")
    print("=" * 50)
    
    if chatbot.model is None:
        print("❌ Modèle non chargé")
        return
    
    print("✅ Chatbot prêt!")
    
    # Menu de déploiement
    while True:
        print("\n" + "=" * 50)
        print("OPTIONS DE DÉPLOIEMENT:")
        print("1. 🌐 Interface web Gradio (recommandé)")
        print("2. 🔌 API FastAPI")
        print("3. 💬 Chat en console")
        print("4. 🚪 Quitter")
        print("=" * 50)
        
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == "1":
            print("\n🌐 Lancement de l'interface Gradio...")
            interface = create_gradio_interface()
            interface.launch(
                server_name="127.0.0.1",
                server_port=7860,
                share=False,
                show_error=True,
                show_tips=True
            )
        
        elif choice == "2":
            print("\n🔌 Lancement de l'API FastAPI...")
            print("API disponible sur: http://127.0.0.1:8000")
            print("Documentation: http://127.0.0.1:8000/docs")
            uvicorn.run(app, host="127.0.0.1", port=8000)
        
        elif choice == "3":
            print("\n💬 Chat en console (tapez 'quit' pour quitter)")
            while True:
                user_input = input("\n👤 Vous: ").strip()
                if user_input.lower() in ['quit', 'exit', 'quitter']:
                    break
                
                if user_input:
                    response = chatbot.generate_response(user_input)
                    print(f"🤖 Assistant: {response}")
        
        elif choice == "4":
            print("👋 Au revoir!")
            break
        
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
