"""
Vérification complète de CUDA + PyTorch
"""

def check_cuda_installation():
    print("🔍 Vérification CUDA + PyTorch")
    print("=" * 40)
    
    # 1. Vérifier CUDA système
    import subprocess
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA Toolkit installé")
            for line in result.stdout.split('\n'):
                if 'release' in line:
                    print(f"  📋 {line.strip()}")
        else:
            print("❌ CUDA Toolkit non trouvé")
    except:
        print("❌ nvcc non disponible")
    
    # 2. Vérifier nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Driver NVIDIA fonctionnel")
            # Extraire info GPU
            for line in result.stdout.split('\n'):
                if 'GeForce' in line or 'RTX' in line:
                    print(f"  🎮 {line.strip()}")
                    break
        else:
            print("❌ nvidia-smi échoué")
    except:
        print("❌ nvidia-smi non disponible")
    
    # 3. Vérifier PyTorch
    try:
        import torch
        print(f"✅ PyTorch installé: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA disponible dans PyTorch")
            print(f"  📊 Version CUDA PyTorch: {torch.version.cuda}")
            print(f"  🎮 Nombre de GPUs: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"  🎯 GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            # Test simple
            try:
                x = torch.randn(1000, 1000).cuda()
                y = torch.randn(1000, 1000).cuda()
                z = torch.mm(x, y)
                print("✅ Test CUDA réussi")
                return True
            except Exception as e:
                print(f"❌ Test CUDA échoué: {e}")
                return False
        else:
            print("❌ CUDA non disponible dans PyTorch")
            print("💡 Réinstallez PyTorch avec CUDA")
            return False
            
    except ImportError:
        print("❌ PyTorch non installé")
        return False

def install_pytorch_cuda():
    """Guide d'installation PyTorch CUDA"""
    print("\n🔧 Installation PyTorch CUDA")
    print("=" * 30)
    
    print("Commandes d'installation:")
    print("1. Désinstaller PyTorch CPU:")
    print("   pip uninstall torch torchvision torchaudio -y")
    print("\n2. Installer PyTorch CUDA 12.1:")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
    print("\n3. Ou CUDA 11.8 (plus compatible):")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")

def main():
    if check_cuda_installation():
        print("\n🎉 CUDA + PyTorch prêts pour le fine-tuning!")
        print("🚀 Vous pouvez lancer: python start_finetuning_cuda.py")
    else:
        print("\n❌ Configuration CUDA incomplète")
        install_pytorch_cuda()

if __name__ == "__main__":
    main()
