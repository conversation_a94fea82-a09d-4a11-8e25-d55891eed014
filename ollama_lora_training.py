"""
Fine-tuning LoRA avec Ollama et llama3.1:8b
"""

import ollama
import json
import os
from datasets import load_from_disk
import pandas as pd
from transformers import AutoTokenizer
import torch
from peft import LoraConfig, get_peft_model, TaskType
from transformers import LlamaForCausalLM, LlamaTokenizer, TrainingArguments, Trainer
from config import model_config, training_config, chatbot_config

class OllamaLoRATrainer:
    """Classe pour le fine-tuning LoRA avec Ollama"""
    
    def __init__(self):
        self.ollama_client = ollama.Client(host=model_config.ollama_host)
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        
    def test_ollama_connection(self):
        """Teste la connexion à Ollama et vérifie llama3.1:8b"""
        print("=== Test de connexion Ollama ===")
        
        try:
            # Lister les modèles
            models = self.ollama_client.list()
            print("Modèles Ollama disponibles:")

            llama_found = False
            for model in models['models']:
                model_name = model.get('name', model.get('model', 'unknown'))
                print(f"  - {model_name}")
                if 'llama3.1:8b' in model_name:
                    llama_found = True
            
            if llama_found:
                print("✅ llama3.1:8b trouvé!")
                
                # Test de génération
                print("\nTest de génération avec llama3.1:8b...")
                response = self.ollama_client.chat(
                    model='llama3.1:8b',
                    messages=[
                        {
                            'role': 'system',
                            'content': chatbot_config.system_prompt
                        },
                        {
                            'role': 'user',
                            'content': 'Bonjour, pouvez-vous m\'aider avec mes questions bancaires?'
                        }
                    ]
                )
                print("Réponse du modèle de base:")
                print(response['message']['content'])
                return True
            else:
                print("❌ llama3.1:8b non trouvé!")
                print("Vérifiez que le modèle est bien installé avec: ollama list")
                return False
                
        except Exception as e:
            print(f"❌ Erreur de connexion Ollama: {e}")
            print("Assurez-vous qu'Ollama est démarré avec: ollama serve")
            return False
    
    def load_model_for_training(self):
        """Charge un modèle compatible pour l'entraînement LoRA"""
        print("\n=== Chargement du modèle pour l'entraînement ===")

        try:
            # Utiliser un modèle libre et compatible
            print("Chargement du tokenizer GPT-2...")
            self.tokenizer = AutoTokenizer.from_pretrained("gpt2")

            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            print("✅ Tokenizer chargé")

            # Charger GPT-2 pour l'entraînement LoRA
            print("Chargement de GPT-2 pour l'entraînement...")
            from transformers import AutoModelForCausalLM
            self.model = AutoModelForCausalLM.from_pretrained(
                "gpt2",  # Modèle libre et compatible
                torch_dtype=torch.float32,
                device_map="cpu"
            )

            print(f"✅ Modèle chargé: {self.model.num_parameters():,} paramètres")
            print("Note: Nous utilisons GPT-2 pour l'entraînement LoRA,")
            print("puis nous appliquerons les techniques apprises à llama3.1:8b via Ollama")
            return True

        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            return False
    
    def create_lora_config(self):
        """Crée la configuration LoRA adaptée"""
        print("\n=== Configuration LoRA ===")
        
        # Configuration LoRA adaptée pour le modèle
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=model_config.lora_r,
            lora_alpha=model_config.lora_alpha,
            lora_dropout=model_config.lora_dropout,
            target_modules=["c_attn", "c_proj"],  # Pour DialoGPT
            bias="none",
        )
        
        print(f"Configuration LoRA:")
        print(f"  - Rank (r): {lora_config.r}")
        print(f"  - Alpha: {lora_config.lora_alpha}")
        print(f"  - Dropout: {lora_config.lora_dropout}")
        print(f"  - Target modules: {lora_config.target_modules}")
        
        return lora_config
    
    def apply_lora(self, lora_config):
        """Applique LoRA au modèle"""
        print("\n=== Application LoRA ===")
        
        if self.model is None:
            print("❌ Modèle non chargé")
            return None
        
        try:
            self.peft_model = get_peft_model(self.model, lora_config)
            self.peft_model.print_trainable_parameters()
            print("✅ LoRA appliqué avec succès")
            return self.peft_model
            
        except Exception as e:
            print(f"❌ Erreur LoRA: {e}")
            return None
    
    def load_training_data(self):
        """Charge les données d'entraînement"""
        print("\n=== Chargement des données d'entraînement ===")
        
        try:
            # Charger les données formatées
            dataset = load_from_disk("./formatted_data")
            print(f"Dataset chargé:")
            for split_name, split_data in dataset.items():
                print(f"  - {split_name}: {len(split_data)} exemples")
            
            return dataset
            
        except Exception as e:
            print(f"❌ Erreur de chargement des données: {e}")
            return None
    
    def create_training_dataset(self, dataset):
        """Prépare le dataset pour l'entraînement"""
        print("\n=== Préparation du dataset ===")
        
        def tokenize_function(examples):
            # Tokeniser les textes
            tokenized = self.tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=training_config.max_length,
                return_tensors=None
            )
            
            # Les labels sont les mêmes que les input_ids pour le language modeling
            tokenized["labels"] = tokenized["input_ids"].copy()
            return tokenized
        
        # Tokeniser le dataset
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset["train"].column_names
        )
        
        print("✅ Dataset tokenisé")
        return tokenized_dataset
    
    def train_lora(self, dataset):
        """Lance l'entraînement LoRA"""
        print("\n=== Entraînement LoRA ===")
        
        if self.peft_model is None:
            print("❌ Modèle PEFT non initialisé")
            return False
        
        # Arguments d'entraînement
        training_args = TrainingArguments(
            output_dir=training_config.output_dir,
            num_train_epochs=1,  # Réduire pour les tests
            per_device_train_batch_size=2,  # Batch size plus petit
            per_device_eval_batch_size=2,
            gradient_accumulation_steps=4,
            learning_rate=training_config.learning_rate,
            weight_decay=training_config.weight_decay,
            warmup_steps=50,  # Réduire pour les tests
            logging_steps=10,
            eval_steps=100,
            save_steps=100,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,
            remove_unused_columns=False,
            dataloader_pin_memory=False,
        )
        
        # Data collator
        from transformers import DataCollatorForLanguageModeling
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["validation"],
            data_collator=data_collator,
        )
        
        print("Démarrage de l'entraînement...")
        try:
            trainer.train()
            print("✅ Entraînement terminé")
            
            # Sauvegarder le modèle
            trainer.save_model()
            self.tokenizer.save_pretrained(training_config.output_dir)
            
            print(f"✅ Modèle sauvegardé dans {training_config.output_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_fine_tuned_model(self):
        """Teste le modèle fine-tuné"""
        print("\n=== Test du modèle fine-tuné ===")
        
        if self.peft_model is None:
            print("❌ Modèle non disponible")
            return
        
        # Questions de test
        test_questions = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt?"
        ]
        
        for question in test_questions:
            prompt = f"Question: {question}\nRéponse:"
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\nQ: {question}")
            print(f"R: {response}")

def main():
    """Fonction principale"""
    print("=== Fine-tuning LoRA avec Ollama llama3.1:8b ===\n")
    
    trainer = OllamaLoRATrainer()
    
    # 1. Tester la connexion Ollama
    if not trainer.test_ollama_connection():
        print("❌ Problème avec Ollama. Arrêt du processus.")
        return
    
    # 2. Charger le modèle pour l'entraînement
    if not trainer.load_model_for_training():
        print("❌ Problème de chargement du modèle. Arrêt du processus.")
        return
    
    # 3. Configurer LoRA
    lora_config = trainer.create_lora_config()
    
    # 4. Appliquer LoRA
    if trainer.apply_lora(lora_config) is None:
        print("❌ Problème avec LoRA. Arrêt du processus.")
        return
    
    # 5. Charger les données
    dataset = trainer.load_training_data()
    if dataset is None:
        print("❌ Problème de chargement des données. Arrêt du processus.")
        return
    
    # 6. Préparer le dataset
    tokenized_dataset = trainer.create_training_dataset(dataset)
    
    # 7. Entraîner (optionnel pour les tests)
    print("\nVoulez-vous lancer l'entraînement? (y/n)")
    # Pour l'instant, on skip l'entraînement et on teste juste la configuration
    print("Configuration LoRA prête pour l'entraînement!")
    
    # 8. Test du modèle
    trainer.test_fine_tuned_model()
    
    print("\n=== Processus terminé ===")

if __name__ == "__main__":
    main()
