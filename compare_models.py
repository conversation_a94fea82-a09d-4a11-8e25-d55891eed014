"""
Comparaison entre le modèle fine-tuné et le modèle de base
Test Q/R bancaire avec évaluation comparative
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from datasets import load_dataset
import random
import json
from pathlib import Path
import time

print("🔍 COMPARAISON MODÈLES - FINE-TUNÉ vs BASE")
print("=" * 60)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

def load_base_model():
    """Charge le modèle de base"""
    print("\n📝 Chargement modèle de base...")
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
        model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model.eval()
    print("✅ Modèle de base chargé")
    return tokenizer, model

def load_finetuned_model(base_model, tokenizer):
    """Charge le modèle fine-tuné"""
    print("\n🔧 Chargement modèle fine-tuné...")
    
    try:
        # Essayer le dossier principal
        finetuned_model = PeftModel.from_pretrained(
            base_model, 
            adapter_path,
            torch_dtype=torch.float16
        )
        print("✅ Modèle fine-tuné chargé (dossier principal)")
        return finetuned_model
    except:
        try:
            # Essayer le checkpoint final
            checkpoint_path = f"{adapter_path}/checkpoint-570"
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                checkpoint_path,
                torch_dtype=torch.float16
            )
            print("✅ Modèle fine-tuné chargé (checkpoint-570)")
            return finetuned_model
        except Exception as e:
            print(f"❌ Erreur chargement fine-tuné: {e}")
            return None

def generate_response(model, tokenizer, question, max_length=200):
    """Génère une réponse"""
    
    # Format Llama 3.1
    prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    
    # Tokenisation
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # Génération
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            temperature=0.1,  # Bas pour cohérence
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            repetition_penalty=1.1
        )
    
    generation_time = time.time() - start_time
    
    # Décodage
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Extraction de la réponse
    if "<|start_header_id|>assistant<|end_header_id|>" in response:
        response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
    
    return response, generation_time

def evaluate_banking_relevance(response):
    """Évalue la pertinence bancaire d'une réponse"""
    banking_keywords = [
        'account', 'bank', 'balance', 'transfer', 'loan', 'interest', 
        'charge', 'fee', 'credit', 'debit', 'deposit', 'withdrawal',
        'savings', 'checking', 'mortgage', 'payment', 'transaction',
        'branch', 'atm', 'card', 'statement', 'overdraft'
    ]
    
    response_lower = response.lower()
    matches = sum(1 for keyword in banking_keywords if keyword in response_lower)
    
    # Score sur 10
    relevance_score = min(10, matches * 2)
    return relevance_score, matches

# Chargement des modèles
tokenizer, base_model = load_base_model()
finetuned_model = load_finetuned_model(base_model, tokenizer)

if finetuned_model is None:
    print("❌ Impossible de charger le modèle fine-tuné")
    print("💡 Utilisation du modèle de base seulement")
    finetuned_model = base_model

# Questions de test bancaires
test_questions = [
    "What are the charges for account maintenance?",
    "How to open a savings account?",
    "What is the minimum balance required?",
    "How to transfer money between accounts?",
    "What are the interest rates for loans?",
    "How to apply for a credit card?",
    "What are the ATM withdrawal limits?",
    "How to check my account balance?",
    "What documents are needed for account opening?",
    "How to report a lost debit card?"
]

print(f"\n🧪 TESTS COMPARATIFS ({len(test_questions)} questions)")
print("=" * 60)

results = []

for i, question in enumerate(test_questions):
    print(f"\n📋 Test {i+1}/{len(test_questions)}:")
    print(f"❓ Question: {question}")
    
    # Test modèle de base
    print("\n🔸 MODÈLE DE BASE:")
    try:
        base_response, base_time = generate_response(base_model, tokenizer, question)
        base_relevance, base_keywords = evaluate_banking_relevance(base_response)
        print(f"⏱️ Temps: {base_time:.2f}s")
        print(f"🎯 Pertinence: {base_relevance}/10 ({base_keywords} mots-clés)")
        print(f"💬 Réponse: {base_response[:150]}...")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        base_response, base_time, base_relevance = "Erreur", 0, 0
    
    # Test modèle fine-tuné
    print("\n🔹 MODÈLE FINE-TUNÉ:")
    try:
        ft_response, ft_time = generate_response(finetuned_model, tokenizer, question)
        ft_relevance, ft_keywords = evaluate_banking_relevance(ft_response)
        print(f"⏱️ Temps: {ft_time:.2f}s")
        print(f"🎯 Pertinence: {ft_relevance}/10 ({ft_keywords} mots-clés)")
        print(f"💬 Réponse: {ft_response[:150]}...")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        ft_response, ft_time, ft_relevance = "Erreur", 0, 0
    
    # Comparaison
    improvement = ft_relevance - base_relevance
    if improvement > 0:
        print(f"📈 AMÉLIORATION: +{improvement} points")
    elif improvement < 0:
        print(f"📉 DÉGRADATION: {improvement} points")
    else:
        print("➡️ ÉGALITÉ")
    
    # Sauvegarde des résultats
    results.append({
        "question": question,
        "base_response": base_response,
        "base_time": base_time,
        "base_relevance": base_relevance,
        "ft_response": ft_response,
        "ft_time": ft_time,
        "ft_relevance": ft_relevance,
        "improvement": improvement
    })

# Analyse finale
print("\n" + "=" * 60)
print("📊 ANALYSE COMPARATIVE FINALE")
print("=" * 60)

avg_base_relevance = sum(r["base_relevance"] for r in results) / len(results)
avg_ft_relevance = sum(r["ft_relevance"] for r in results) / len(results)
avg_improvement = avg_ft_relevance - avg_base_relevance

avg_base_time = sum(r["base_time"] for r in results) / len(results)
avg_ft_time = sum(r["ft_time"] for r in results) / len(results)

improvements = [r["improvement"] for r in results]
positive_improvements = sum(1 for imp in improvements if imp > 0)

print(f"🔸 MODÈLE DE BASE:")
print(f"  📊 Pertinence moyenne: {avg_base_relevance:.1f}/10")
print(f"  ⏱️ Temps moyen: {avg_base_time:.2f}s")

print(f"\n🔹 MODÈLE FINE-TUNÉ:")
print(f"  📊 Pertinence moyenne: {avg_ft_relevance:.1f}/10")
print(f"  ⏱️ Temps moyen: {avg_ft_time:.2f}s")

print(f"\n🎯 RÉSULTATS:")
print(f"  📈 Amélioration moyenne: {avg_improvement:+.1f} points")
print(f"  ✅ Questions améliorées: {positive_improvements}/{len(results)}")
print(f"  📊 Taux de réussite: {positive_improvements/len(results)*100:.1f}%")

# Verdict
if avg_improvement >= 2:
    print(f"\n🏆 EXCELLENT: Fine-tuning très réussi!")
elif avg_improvement >= 1:
    print(f"\n✅ BON: Fine-tuning réussi")
elif avg_improvement >= 0:
    print(f"\n⚠️ MOYEN: Fine-tuning partiellement réussi")
else:
    print(f"\n❌ FAIBLE: Fine-tuning à améliorer")

# Sauvegarde des résultats
results_file = "comparison_results.json"
with open(results_file, 'w', encoding='utf-8') as f:
    json.dump({
        "summary": {
            "avg_base_relevance": avg_base_relevance,
            "avg_ft_relevance": avg_ft_relevance,
            "avg_improvement": avg_improvement,
            "positive_improvements": positive_improvements,
            "total_tests": len(results)
        },
        "detailed_results": results
    }, f, indent=2, ensure_ascii=False)

print(f"\n💾 Résultats sauvegardés: {results_file}")
print("\n🎉 COMPARAISON TERMINÉE!")
