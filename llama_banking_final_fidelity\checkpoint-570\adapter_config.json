{"alpha_pattern": {}, "auto_mapping": null, "base_model_name_or_path": "./models/Llama-3.1-8B-Instruct", "bias": "none", "corda_config": null, "eva_config": null, "exclude_modules": null, "fan_in_fan_out": false, "inference_mode": true, "init_lora_weights": true, "layer_replication": null, "layers_pattern": null, "layers_to_transform": null, "loftq_config": {}, "lora_alpha": 128, "lora_bias": false, "lora_dropout": 0.03, "megatron_config": null, "megatron_core": "megatron.core", "modules_to_save": null, "peft_type": "LORA", "qalora_group_size": 16, "r": 64, "rank_pattern": {}, "revision": null, "target_modules": ["o_proj", "up_proj", "gate_proj", "down_proj", "k_proj", "q_proj", "v_proj"], "task_type": "CAUSAL_LM", "trainable_token_indices": null, "use_dora": false, "use_qalora": false, "use_rslora": false}