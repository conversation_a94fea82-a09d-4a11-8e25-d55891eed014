"""
Fine-tuning Llama 3.1 8B avec CUDA et LoRA pour chatbot bancaire
Optimisé pour GPU NVIDIA avec gestion mémoire avancée
"""

import os
import json
import torch
import wandb
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# Transformers et PEFT
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    prepare_model_for_kbit_training
)
from datasets import load_from_disk
import accelerate
from accelerate import Accelerator

# Configuration logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LlamaFineTuner:
    """Fine-tuner pour Llama 3.1 8B avec optimisations CUDA"""
    
    def __init__(self, config_path: str = "cuda_config.json"):
        self.config = self.load_config(config_path)
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.dataset = None
        self.accelerator = Accelerator()
        
        # Configuration CUDA
        self.setup_cuda_environment()
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Charge la configuration"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            logger.info(f"✅ Configuration chargée: {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"⚠️ Config non trouvée, utilisation des valeurs par défaut")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Configuration par défaut optimisée pour Llama 3.1 8B"""
        return {
            "model_settings": {
                "model_name": "meta-llama/Llama-3.1-8B-Instruct",
                "load_in_4bit": True,
                "bnb_4bit_compute_dtype": "float16",
                "bnb_4bit_use_double_quant": True,
                "bnb_4bit_quant_type": "nf4"
            },
            "lora_settings": {
                "r": 64,  # Rank plus élevé pour de meilleures performances
                "lora_alpha": 128,
                "lora_dropout": 0.05,
                "target_modules": [
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ],
                "bias": "none",
                "task_type": "CAUSAL_LM"
            },
            "training_settings": {
                "output_dir": "./llama_banking_finetuned",
                "num_train_epochs": 3,
                "per_device_train_batch_size": 1,
                "per_device_eval_batch_size": 1,
                "gradient_accumulation_steps": 16,
                "learning_rate": 2e-4,
                "weight_decay": 0.01,
                "warmup_ratio": 0.1,
                "lr_scheduler_type": "cosine",
                "max_grad_norm": 1.0,
                "optim": "paged_adamw_8bit",
                "fp16": True,
                "gradient_checkpointing": True,
                "dataloader_pin_memory": True,
                "dataloader_num_workers": 4,
                "logging_steps": 10,
                "eval_steps": 100,
                "save_steps": 500,
                "save_total_limit": 3,
                "load_best_model_at_end": True,
                "metric_for_best_model": "eval_loss",
                "greater_is_better": False,
                "report_to": ["wandb"],
                "run_name": f"llama-banking-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            }
        }
    
    def setup_cuda_environment(self):
        """Configure l'environnement CUDA"""
        logger.info("=== Configuration CUDA ===")
        
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA non disponible")
        
        # Optimisations CUDA
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        # Variables d'environnement
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        os.environ["CUDA_LAUNCH_BLOCKING"] = "0"
        
        # Informations GPU
        device = torch.cuda.current_device()
        gpu_props = torch.cuda.get_device_properties(device)
        logger.info(f"✅ GPU: {gpu_props.name}")
        logger.info(f"✅ Mémoire: {gpu_props.total_memory / 1024**3:.1f} GB")
        logger.info(f"✅ Compute: {gpu_props.major}.{gpu_props.minor}")
    
    def load_model_and_tokenizer(self):
        """Charge le modèle Llama 3.1 avec quantification"""
        logger.info("=== Chargement Llama 3.1 8B ===")
        
        model_name = self.config["model_settings"]["model_name"]
        
        try:
            # Configuration de quantification 4-bit
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=self.config["model_settings"]["load_in_4bit"],
                bnb_4bit_compute_dtype=getattr(torch, self.config["model_settings"]["bnb_4bit_compute_dtype"]),
                bnb_4bit_use_double_quant=self.config["model_settings"]["bnb_4bit_use_double_quant"],
                bnb_4bit_quant_type=self.config["model_settings"]["bnb_4bit_quant_type"]
            )
            
            # Charger le tokenizer
            logger.info("Chargement du tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                use_auth_token=True
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # Charger le modèle avec quantification
            logger.info("Chargement du modèle (cela peut prendre du temps)...")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=bnb_config,
                device_map="auto",
                trust_remote_code=True,
                use_auth_token=True,
                torch_dtype=torch.float16,
                attn_implementation="flash_attention_2"  # Attention optimisée
            )
            
            # Préparer pour l'entraînement k-bit
            self.model = prepare_model_for_kbit_training(self.model)
            
            logger.info(f"✅ Modèle chargé: {model_name}")
            logger.info(f"✅ Paramètres: {self.model.num_parameters():,}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def setup_lora(self):
        """Configure LoRA pour Llama 3.1"""
        logger.info("=== Configuration LoRA ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            **{k: v for k, v in self.config["lora_settings"].items() if k != "task_type"}
        )
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, lora_config)
        
        # Afficher les paramètres entraînables
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré")
        return True
    
    def load_dataset(self, dataset_path: str = "./processed_banking_dataset"):
        """Charge le dataset préparé"""
        logger.info(f"=== Chargement du dataset ===")
        
        try:
            self.dataset = load_from_disk(dataset_path)
            
            logger.info(f"✅ Dataset chargé:")
            for split_name, split_data in self.dataset.items():
                logger.info(f"  - {split_name}: {len(split_data)} exemples")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement dataset: {e}")
            return False
    
    def create_trainer(self):
        """Crée le trainer avec optimisations"""
        logger.info("=== Configuration du Trainer ===")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_settings"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=self.dataset["train"],
            eval_dataset=self.dataset.get("test", self.dataset.get("validation")),
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        logger.info("✅ Trainer configuré")
        return trainer
    
    def train(self):
        """Lance l'entraînement"""
        logger.info("=== Démarrage de l'entraînement ===")
        
        # Initialiser wandb
        if "wandb" in self.config["training_settings"]["report_to"]:
            wandb.init(
                project="llama-banking-finetuning",
                name=self.config["training_settings"]["run_name"],
                config=self.config
            )
        
        # Créer le trainer
        trainer = self.create_trainer()
        
        try:
            # Entraînement
            logger.info("🚀 Début de l'entraînement...")
            train_result = trainer.train()
            
            # Sauvegarder le modèle final
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_settings"]["output_dir"])
            
            # Sauvegarder les métriques
            metrics = train_result.metrics
            with open(os.path.join(self.config["training_settings"]["output_dir"], "training_metrics.json"), 'w') as f:
                json.dump(metrics, f, indent=2)
            
            logger.info("✅ Entraînement terminé avec succès!")
            logger.info(f"✅ Modèle sauvegardé: {self.config['training_settings']['output_dir']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False
        
        finally:
            if "wandb" in self.config["training_settings"]["report_to"]:
                wandb.finish()

def main():
    """Fonction principale"""
    print("🦙 Fine-tuning Llama 3.1 8B pour Chatbot Bancaire")
    print("=" * 60)
    
    # Vérifications préalables
    if not torch.cuda.is_available():
        print("❌ CUDA requis pour ce script")
        return
    
    # Initialiser le fine-tuner
    fine_tuner = LlamaFineTuner()
    
    # 1. Charger le modèle
    print("\n1. Chargement du modèle...")
    if not fine_tuner.load_model_and_tokenizer():
        print("❌ Échec du chargement du modèle")
        return
    
    # 2. Configurer LoRA
    print("\n2. Configuration LoRA...")
    fine_tuner.setup_lora()
    
    # 3. Charger le dataset
    print("\n3. Chargement du dataset...")
    if not fine_tuner.load_dataset():
        print("❌ Échec du chargement du dataset")
        print("💡 Exécutez d'abord: python prepare_banking_dataset.py")
        return
    
    # 4. Confirmer l'entraînement
    print("\n4. Prêt pour l'entraînement!")
    print("⚠️ L'entraînement peut prendre plusieurs heures")
    print("Voulez-vous continuer? (y/n)")
    
    if input().lower() != 'y':
        print("Entraînement annulé")
        return
    
    # 5. Lancer l'entraînement
    print("\n5. Lancement de l'entraînement...")
    if fine_tuner.train():
        print("\n🎉 Fine-tuning terminé avec succès!")
        print(f"📁 Modèle sauvegardé: {fine_tuner.config['training_settings']['output_dir']}")
    else:
        print("\n❌ Échec de l'entraînement")

if __name__ == "__main__":
    main()
