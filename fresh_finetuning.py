"""
Fine-tuning Llama 3.1 8B - Nouveau départ
Version optimisée et stable pour RTX 4060 8GB
"""

import os
import json
import torch
from datetime import datetime
from pathlib import Path

# Transformers et PEFT
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    prepare_model_for_kbit_training
)
from datasets import load_from_disk

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FreshLlamaFineTuner:
    """Fine-tuner Llama 3.1 8B - Version stable"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.dataset = None
        
        # Configuration optimisée pour stabilité
        self.config = {
            "model_settings": {
                "model_path": "./models/Llama-3.1-8B-Instruct",
                "load_in_4bit": True,
                "bnb_4bit_compute_dtype": "float16",
                "bnb_4bit_use_double_quant": True,
                "bnb_4bit_quant_type": "nf4"
            },
            "lora_settings": {
                "r": 16,  # Rank plus petit pour stabilité
                "lora_alpha": 32,
                "lora_dropout": 0.1,
                "target_modules": [
                    "q_proj", "k_proj", "v_proj", "o_proj"  # Seulement 4 modules
                ],
                "bias": "none"
            },
            "training_settings": {
                "output_dir": "./llama_banking_fresh",
                "num_train_epochs": 1,  # 1 epoch seulement
                "per_device_train_batch_size": 1,
                "per_device_eval_batch_size": 1,
                "gradient_accumulation_steps": 8,  # Réduit pour stabilité
                "learning_rate": 2e-4,
                "weight_decay": 0.01,
                "warmup_ratio": 0.1,
                "lr_scheduler_type": "linear",  # Plus simple
                "max_grad_norm": 1.0,
                "optim": "adamw_torch",  # Optimiseur standard
                "fp16": True,
                "gradient_checkpointing": True,
                "dataloader_pin_memory": False,  # Désactivé pour stabilité
                "dataloader_num_workers": 0,    # Pas de multiprocessing
                "logging_steps": 10,
                "eval_steps": 100,
                "save_steps": 200,
                "save_total_limit": 2,
                "max_steps": 300,  # Moins de steps pour test
                "report_to": [],
                "remove_unused_columns": False,
                "dataloader_drop_last": False,
            }
        }
    
    def setup_environment(self):
        """Configure l'environnement"""
        logger.info("=== Configuration environnement ===")
        
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA non disponible")
        
        # Optimisations CUDA
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        
        # Variables d'environnement
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        os.environ["CUDA_LAUNCH_BLOCKING"] = "0"
        
        # Vider le cache
        torch.cuda.empty_cache()
        
        # Informations GPU
        device = torch.cuda.current_device()
        gpu_props = torch.cuda.get_device_properties(device)
        logger.info(f"✅ GPU: {gpu_props.name}")
        logger.info(f"✅ Mémoire: {gpu_props.total_memory / 1024**3:.1f} GB")
    
    def load_model_and_tokenizer(self):
        """Charge le modèle avec quantification"""
        logger.info("=== Chargement Llama 3.1 8B ===")
        
        model_path = self.config["model_settings"]["model_path"]
        
        try:
            # Configuration 4-bit
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Tokenizer
            logger.info("Chargement du tokenizer...")
            if Path(model_path).exists():
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # Modèle
            logger.info("Chargement du modèle...")
            if Path(model_path).exists():
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    token="*************************************"
                )
            
            # Préparer pour l'entraînement
            self.model = prepare_model_for_kbit_training(self.model)
            
            logger.info(f"✅ Modèle chargé: {self.model.num_parameters():,} paramètres")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def setup_lora(self):
        """Configure LoRA"""
        logger.info("=== Configuration LoRA ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            **self.config["lora_settings"]
        )
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, lora_config)
        
        # Afficher les paramètres
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré")
        return True
    
    def load_dataset(self):
        """Charge le dataset"""
        logger.info("=== Chargement du dataset ===")
        
        dataset_path = "./processed_banking_dataset_no_instruction"
        
        try:
            self.dataset = load_from_disk(dataset_path)
            
            logger.info(f"✅ Dataset chargé:")
            for split_name, split_data in self.dataset.items():
                logger.info(f"  - {split_name}: {len(split_data)} exemples")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur dataset: {e}")
            return False
    
    def train(self):
        """Lance l'entraînement"""
        logger.info("=== Démarrage Fine-tuning ===")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_settings"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=self.dataset["train"],
            data_collator=data_collator,
            processing_class=self.tokenizer,  # Nouveau paramètre
        )
        
        try:
            # Entraînement
            logger.info("🚀 Début de l'entraînement...")
            train_result = trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_settings"]["output_dir"])
            
            # Métriques
            metrics = train_result.metrics
            with open(Path(self.config["training_settings"]["output_dir"]) / "training_metrics.json", 'w') as f:
                json.dump(metrics, f, indent=2)
            
            logger.info("✅ Fine-tuning terminé!")
            logger.info(f"✅ Modèle sauvegardé: {self.config['training_settings']['output_dir']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False

def main():
    """Fonction principale"""
    print("🦙 Fine-tuning Llama 3.1 8B - Nouveau Départ")
    print("🎮 RTX 4060 8GB + CUDA 12.9")
    print("=" * 60)
    
    fine_tuner = FreshLlamaFineTuner()
    
    # 1. Configuration
    fine_tuner.setup_environment()
    
    # 2. Modèle
    print("\n1. Chargement du modèle...")
    if not fine_tuner.load_model_and_tokenizer():
        print("❌ Échec du chargement")
        return
    
    # 3. LoRA
    print("\n2. Configuration LoRA...")
    fine_tuner.setup_lora()
    
    # 4. Dataset
    print("\n3. Chargement du dataset...")
    if not fine_tuner.load_dataset():
        print("❌ Dataset non trouvé")
        return
    
    # 5. Confirmer
    print("\n4. Configuration finale:")
    print("  🎯 300 steps (test rapide)")
    print("  📊 LoRA rank 16 (stable)")
    print("  💾 Batch size 1 + accumulation 8")
    print("  📁 Sauvegarde: ./llama_banking_fresh")
    
    confirm = input("\nLancer le fine-tuning? (y/n): ").lower()
    if confirm != 'y':
        print("Fine-tuning annulé")
        return
    
    # 6. Entraîner
    print("\n5. Lancement du fine-tuning...")
    if fine_tuner.train():
        print("\n🎉 FINE-TUNING TERMINÉ!")
        print("🏦 Chatbot bancaire prêt!")
        print("📁 Modèle: ./llama_banking_fresh")
    else:
        print("\n❌ Échec du fine-tuning")

if __name__ == "__main__":
    main()
