"""
Préparation du dataset wasifis/bank-assistant-qa pour fine-tuning Llama 3.1 8B
"""

import json
import os
from datasets import load_dataset, Dataset, DatasetDict
from transformers import AutoTokenizer
import torch
from typing import Dict, List
import pandas as pd

class BankingDatasetPreparator:
    """Préparateur de dataset bancaire pour Llama 3.1"""
    
    def __init__(self, model_name="meta-llama/Llama-3.1-8B-Instruct"):
        self.model_name = model_name
        self.tokenizer = None
        self.dataset = None
        self.processed_dataset = None
        
    def setup_tokenizer(self):
        """Configure le tokenizer Llama 3.1"""
        print("=== Configuration du tokenizer Llama 3.1 ===")
        
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                use_auth_token=True  # Nécessaire pour Llama
            )
            
            # Configuration des tokens spéciaux
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            print(f"✅ Tokenizer chargé: {self.model_name}")
            print(f"Vocabulaire: {len(self.tokenizer)} tokens")
            print(f"Tokens spéciaux:")
            print(f"  - BOS: {self.tokenizer.bos_token} ({self.tokenizer.bos_token_id})")
            print(f"  - EOS: {self.tokenizer.eos_token} ({self.tokenizer.eos_token_id})")
            print(f"  - PAD: {self.tokenizer.pad_token} ({self.tokenizer.pad_token_id})")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur tokenizer: {e}")
            print("💡 Assurez-vous d'avoir l'accès à Llama 3.1 sur Hugging Face")
            return False
    
    def load_wasifis_dataset(self):
        """Charge le dataset wasifis/bank-assistant-qa"""
        print("\n=== Chargement du dataset wasifis/bank-assistant-qa ===")
        
        try:
            # Charger le dataset depuis Hugging Face
            self.dataset = load_dataset("wasifis/bank-assistant-qa")
            
            print(f"✅ Dataset chargé:")
            for split_name, split_data in self.dataset.items():
                print(f"  - {split_name}: {len(split_data)} exemples")
            
            # Afficher quelques exemples
            print(f"\n📋 Exemples du dataset:")
            for i in range(min(3, len(self.dataset['train']))):
                example = self.dataset['train'][i]
                print(f"\nExemple {i+1}:")
                print(f"  Input: {example.get('input', 'N/A')[:100]}...")
                print(f"  Output: {example.get('output', 'N/A')[:100]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            print("💡 Vérifiez votre connexion internet et l'accès au dataset")
            return False
    
    def format_for_llama(self, examples: Dict) -> Dict:
        """Formate les données pour Llama 3.1 avec le format de chat"""
        
        formatted_texts = []
        
        for i in range(len(examples['input'])):
            user_input = examples['input'][i]
            assistant_output = examples['output'][i]
            
            # Format de chat Llama 3.1 Instruct
            conversation = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel. Vous aidez les clients avec leurs questions bancaires de manière précise, courtoise et professionnelle. Utilisez vos connaissances spécialisées en services bancaires pour fournir des réponses utiles et appropriées.<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

{assistant_output}<|eot_id|>"""
            
            formatted_texts.append(conversation)
        
        return {"text": formatted_texts}
    
    def tokenize_dataset(self, max_length: int = 2048):
        """Tokenise le dataset avec le tokenizer Llama"""
        print(f"\n=== Tokenisation du dataset (max_length={max_length}) ===")
        
        if self.dataset is None or self.tokenizer is None:
            print("❌ Dataset ou tokenizer non chargé")
            return False
        
        try:
            # Formater les données pour Llama
            print("Formatage des conversations...")
            formatted_dataset = self.dataset.map(
                self.format_for_llama,
                batched=True,
                remove_columns=self.dataset['train'].column_names,
                desc="Formatage Llama"
            )
            
            # Fonction de tokenisation
            def tokenize_function(examples):
                # Tokeniser les textes
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,  # Padding dynamique pendant l'entraînement
                    max_length=max_length,
                    return_tensors=None
                )
                
                # Pour le fine-tuning supervisé, labels = input_ids
                tokenized["labels"] = tokenized["input_ids"].copy()
                
                return tokenized
            
            # Tokeniser le dataset
            print("Tokenisation en cours...")
            self.processed_dataset = formatted_dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=["text"],
                desc="Tokenisation"
            )
            
            # Statistiques
            print(f"✅ Dataset tokenisé:")
            for split_name, split_data in self.processed_dataset.items():
                print(f"  - {split_name}: {len(split_data)} exemples")
                
                # Statistiques de longueur
                lengths = [len(item['input_ids']) for item in split_data]
                avg_length = sum(lengths) / len(lengths)
                max_len = max(lengths)
                print(f"    Longueur moyenne: {avg_length:.1f} tokens")
                print(f"    Longueur max: {max_len} tokens")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de tokenisation: {e}")
            return False
    
    def save_processed_dataset(self, output_dir: str = "./processed_banking_dataset"):
        """Sauvegarde le dataset traité"""
        print(f"\n=== Sauvegarde du dataset traité ===")
        
        if self.processed_dataset is None:
            print("❌ Dataset non traité")
            return False
        
        try:
            # Créer le répertoire
            os.makedirs(output_dir, exist_ok=True)
            
            # Sauvegarder le dataset
            self.processed_dataset.save_to_disk(output_dir)
            
            # Sauvegarder le tokenizer
            tokenizer_dir = os.path.join(output_dir, "tokenizer")
            self.tokenizer.save_pretrained(tokenizer_dir)
            
            # Créer un fichier de métadonnées
            metadata = {
                "model_name": self.model_name,
                "dataset_source": "wasifis/bank-assistant-qa",
                "total_examples": sum(len(split) for split in self.processed_dataset.values()),
                "max_length": 2048,
                "format": "llama_chat",
                "splits": {name: len(split) for name, split in self.processed_dataset.items()}
            }
            
            with open(os.path.join(output_dir, "metadata.json"), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Dataset sauvegardé dans: {output_dir}")
            print(f"📁 Structure:")
            print(f"  - dataset_dict.json")
            print(f"  - train/")
            print(f"  - test/ (si disponible)")
            print(f"  - tokenizer/")
            print(f"  - metadata.json")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de sauvegarde: {e}")
            return False
    
    def create_data_analysis(self):
        """Analyse le dataset pour optimiser l'entraînement"""
        print(f"\n=== Analyse du dataset ===")
        
        if self.processed_dataset is None:
            print("❌ Dataset non traité")
            return
        
        # Analyser les longueurs de séquence
        train_data = self.processed_dataset['train']
        lengths = [len(item['input_ids']) for item in train_data]
        
        import matplotlib.pyplot as plt
        import numpy as np
        
        # Statistiques
        stats = {
            "count": len(lengths),
            "mean": np.mean(lengths),
            "std": np.std(lengths),
            "min": np.min(lengths),
            "max": np.max(lengths),
            "percentiles": {
                "25%": np.percentile(lengths, 25),
                "50%": np.percentile(lengths, 50),
                "75%": np.percentile(lengths, 75),
                "95%": np.percentile(lengths, 95)
            }
        }
        
        print(f"📊 Statistiques des longueurs de séquence:")
        print(f"  - Nombre d'exemples: {stats['count']}")
        print(f"  - Longueur moyenne: {stats['mean']:.1f} tokens")
        print(f"  - Écart-type: {stats['std']:.1f}")
        print(f"  - Min/Max: {stats['min']}/{stats['max']}")
        print(f"  - Percentiles:")
        for p, v in stats['percentiles'].items():
            print(f"    {p}: {v:.0f} tokens")
        
        # Recommandations
        print(f"\n💡 Recommandations:")
        if stats['mean'] > 1500:
            print("  - Séquences longues détectées")
            print("  - Recommandé: gradient_checkpointing=True")
            print("  - Recommandé: batch_size=1, gradient_accumulation_steps=16")
        
        if stats['percentiles']['95%'] > 2000:
            print("  - Considérer max_length=2048 ou plus")
        
        # Créer un graphique de distribution
        try:
            plt.figure(figsize=(10, 6))
            plt.hist(lengths, bins=50, alpha=0.7, edgecolor='black')
            plt.axvline(stats['mean'], color='red', linestyle='--', label=f'Moyenne: {stats["mean"]:.0f}')
            plt.axvline(stats['percentiles']['95%'], color='orange', linestyle='--', label=f'95e percentile: {stats["percentiles"]["95%"]:.0f}')
            plt.xlabel('Longueur de séquence (tokens)')
            plt.ylabel('Fréquence')
            plt.title('Distribution des longueurs de séquence')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig('sequence_length_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ Graphique sauvegardé: sequence_length_distribution.png")
        except:
            print("⚠️ Impossible de créer le graphique (matplotlib requis)")

        # Sauvegarder les statistiques
        with open("dataset_analysis.json", 'w') as f:
            json.dump(stats, f, indent=2, default=float)

        print(f"✅ Analyse sauvegardée: dataset_analysis.json")

def main():
    """Fonction principale"""
    print("🏦 Préparation Dataset Bancaire pour Llama 3.1 8B")
    print("=" * 60)
    
    preparator = BankingDatasetPreparator()
    
    # 1. Configurer le tokenizer
    if not preparator.setup_tokenizer():
        print("❌ Impossible de configurer le tokenizer")
        print("💡 Vérifiez l'accès à Llama 3.1 sur Hugging Face")
        return
    
    # 2. Charger le dataset
    if not preparator.load_wasifis_dataset():
        print("❌ Impossible de charger le dataset")
        return
    
    # 3. Tokeniser
    if not preparator.tokenize_dataset():
        print("❌ Erreur de tokenisation")
        return
    
    # 4. Analyser
    preparator.create_data_analysis()
    
    # 5. Sauvegarder
    if preparator.save_processed_dataset():
        print("\n✅ Dataset prêt pour le fine-tuning!")
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ:")
    print("✅ Dataset wasifis/bank-assistant-qa chargé")
    print("✅ Format Llama 3.1 chat appliqué")
    print("✅ Tokenisation complète")
    print("✅ Analyse des données effectuée")
    print("✅ Dataset sauvegardé localement")
    
    print("\n🚀 Prêt pour l'étape suivante: Fine-tuning!")

if __name__ == "__main__":
    main()
