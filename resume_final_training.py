"""
Reprendre le training final depuis checkpoint-400
"""

import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import PeftModel, prepare_model_for_kbit_training
from datasets import load_from_disk

print("🔄 Reprise du training final depuis checkpoint-400")
print("=" * 50)

# Vérifier le checkpoint
checkpoint_path = "./llama_banking_final_fidelity/checkpoint-400"
if not Path(checkpoint_path).exists():
    print("❌ Checkpoint-400 non trouvé")
    exit()

print(f"✅ Checkpoint trouvé: {checkpoint_path}")

# Configuration
output_dir = "./llama_banking_final_fidelity"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

torch.cuda.empty_cache()
print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Configuration 4-bit
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

# Tokenizer
model_path = "./models/Llama-3.1-8B-Instruct"
if Path(model_path).exists():
    tokenizer = AutoTokenizer.from_pretrained(model_path)
else:
    tokenizer = AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        token="*************************************"
    )

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Modèle de base
print("🦙 Chargement modèle de base...")
if Path(model_path).exists():
    base_model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
else:
    base_model = AutoModelForCausalLM.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True,
        token="*************************************"
    )

# Charger le modèle depuis le checkpoint
print("🔧 Chargement depuis checkpoint-400...")
model = PeftModel.from_pretrained(base_model, checkpoint_path)
model = prepare_model_for_kbit_training(model)

print("✅ Modèle chargé depuis checkpoint-400")
model.print_trainable_parameters()

# Dataset
dataset = load_from_disk("./processed_banking_dataset_no_instruction")
train_dataset = dataset["train"]
train_size = int(0.85 * len(train_dataset))
train_split = train_dataset.select(range(train_size))
eval_split = train_dataset.select(range(train_size, len(train_dataset)))

print(f"✅ Train: {len(train_split)} exemples")
print(f"✅ Validation: {len(eval_split)} exemples")

# Configuration pour continuer
training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=5,  # Continuer jusqu'à 5 epochs
    per_device_train_batch_size=1,
    gradient_accumulation_steps=32,
    learning_rate=5e-6,
    weight_decay=0.005,
    warmup_ratio=0.02,
    lr_scheduler_type="cosine",
    max_grad_norm=0.3,
    optim="paged_adamw_8bit",
    fp16=True,
    gradient_checkpointing=True,
    dataloader_pin_memory=False,
    dataloader_num_workers=0,
    logging_steps=5,
    eval_steps=200,
    save_steps=400,
    save_total_limit=5,
    report_to=[],
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_split,
    eval_dataset=eval_split,
    data_collator=data_collator,
    processing_class=tokenizer,
)

# Configuration
remaining_steps = 570 - 400
print(f"\n🎯 Reprise du training:")
print(f"  📊 Depuis: checkpoint-400")
print(f"  📈 Steps restants: ~{remaining_steps}")
print(f"  🔄 Epochs restants: ~1.5")
print(f"  ⏱️ Temps estimé: 20-30 minutes")

# Reprendre l'entraînement
print("\n🚀 Reprise du training...")

try:
    # Reprendre depuis le checkpoint
    train_result = trainer.train(resume_from_checkpoint=checkpoint_path)
    
    # Sauvegarder le modèle final
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    # Évaluation finale
    eval_results = trainer.evaluate()
    
    print(f"\n✅ TRAINING FINAL TERMINÉ!")
    print(f"📊 Train loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
    print(f"📊 Eval loss finale: {eval_results.get('eval_loss', 'N/A')}")
    print(f"📁 Modèle final: {output_dir}")
    
    # Sauvegarder métriques
    import json
    final_metrics = {
        'resumed_from': 'checkpoint-400',
        'train_metrics': train_result.metrics,
        'eval_metrics': eval_results,
        'total_steps': 570
    }
    
    with open(Path(output_dir) / "final_metrics.json", 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    print(f"🧪 Testez avec: python test_advanced_fidelity.py")
    
except Exception as e:
    print(f"❌ Erreur: {e}")

print("\n🎉 TRAINING HAUTE FIDÉLITÉ TERMINÉ!")
print("🏦 Chatbot bancaire optimisé pour fidélité maximale!")
