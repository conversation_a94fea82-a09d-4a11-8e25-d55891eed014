"""
Training avec prévention de mise en veille
"""

import subprocess
import threading
import time
import os

def prevent_sleep():
    """Empêche la mise en veille pendant le training"""
    print("🔒 Prévention de la mise en veille activée")
    
    # Utiliser powercfg pour empêcher la veille
    subprocess.run([
        "powercfg", "/requestsoverride", "PROCESS", "python.exe", 
        "DISPLAY", "SYSTEM", "AWAYMODE"
    ], shell=True)
    
    # Thread pour maintenir l'activité
    def keep_awake():
        while True:
            # Simuler une activité système
            subprocess.run(["powercfg", "/requests"], 
                         shell=True, capture_output=True)
            time.sleep(30)  # Toutes les 30 secondes
    
    # Démarrer le thread en arrière-plan
    thread = threading.Thread(target=keep_awake, daemon=True)
    thread.start()

def run_training():
    """Lance le training avec prévention de veille"""
    print("🚀 Lancement du training avec prévention de veille")
    
    # Activer la prévention
    prevent_sleep()
    
    # Lancer le training
    try:
        result = subprocess.run([
            "python", "optimized_high_fidelity.py"
        ], cwd=os.getcwd())
        
        print(f"Training terminé avec code: {result.returncode}")
        
    except Exception as e:
        print(f"Erreur: {e}")
    
    finally:
        # Nettoyer les overrides
        subprocess.run([
            "powercfg", "/requestsoverride", "PROCESS", "python.exe"
        ], shell=True)
        print("🔓 Prévention de veille désactivée")

if __name__ == "__main__":
    run_training()
