"""
Test rapide CUDA après installation PyTorch
"""

def quick_test():
    print("⚡ Test rapide CUDA + PyTorch")
    print("=" * 30)
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA disponible")
            print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
            print(f"💾 Mémoire: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # Test simple
            x = torch.tensor([1.0, 2.0]).cuda()
            y = x * 2
            print(f"✅ Test calcul CUDA: {y.cpu().numpy()}")
            
            print("\n🎉 CUDA + PyTorch prêts!")
            print("🚀 Vous pouvez lancer le fine-tuning!")
            return True
        else:
            print("❌ CUDA non détecté dans PyTorch")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    quick_test()
