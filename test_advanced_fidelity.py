"""
Test du modèle avancé haute fidélité
"""

import torch
import random
import json
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from datasets import load_dataset
from pathlib import Path
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class AdvancedFidelityTester:
    """Testeur pour modèle avancé haute fidélité"""
    
    def __init__(self, model_path="./llama_banking_advanced_fidelity"):
        self.model_path = Path(model_path)
        self.model = None
        self.tokenizer = None
        self.original_dataset = None
        
    def load_components(self):
        """Charge tous les composants"""
        print("=== Chargement des composants ===")
        
        # Dataset original
        print("📊 Chargement dataset original...")
        self.original_dataset = load_dataset("wasifis/bank-assistant-qa")
        print(f"✅ {len(self.original_dataset['train'])} exemples chargés")
        
        # Configuration 4-bit
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        # Tokenizer
        base_model_path = "./models/Llama-3.1-8B-Instruct"
        if Path(base_model_path).exists():
            self.tokenizer = AutoTokenizer.from_pretrained(base_model_path)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                token="*************************************"
            )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Modèle de base
        print("🦙 Chargement modèle de base...")
        if Path(base_model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                base_model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                token="*************************************"
            )
        
        # Modèle fine-tuné avancé
        print("🔧 Chargement modèle avancé...")
        self.model = PeftModel.from_pretrained(base_model, self.model_path)
        print("✅ Modèle avancé haute fidélité chargé")
    
    def generate_response(self, question, max_length=200):
        """Génère une réponse avec le modèle avancé"""
        
        # Format exact du dataset
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are an expert bank customer service bot for NUST Bank. Provide accurate, helpful, and concise answers to banking questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_length,
                temperature=0.05,  # Très déterministe pour fidélité maximale
                do_sample=True,
                top_p=0.95,
                repetition_penalty=1.05,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        return response
    
    def calculate_similarity(self, text1, text2):
        """Calcule la similarité TF-IDF"""
        try:
            vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            return similarity
        except:
            return 0.0
    
    def test_exact_fidelity(self, num_tests=15):
        """Test de fidélité avec questions exactes"""
        print(f"\n🧪 Test Fidélité Avancé - {num_tests} questions exactes")
        print("=" * 60)
        
        # Sélectionner des échantillons aléatoirement
        dataset_samples = random.sample(list(self.original_dataset['train']), num_tests)
        
        results = []
        high_fidelity_count = 0
        medium_fidelity_count = 0
        
        for i, sample in enumerate(dataset_samples, 1):
            print(f"\n--- Test {i}/{num_tests} ---")
            print(f"❓ Question: {sample['input']}")
            print(f"✅ Réponse attendue: {sample['output'][:120]}...")
            
            # Générer la réponse
            generated_response = self.generate_response(sample['input'])
            print(f"🤖 Réponse générée: {generated_response[:120]}...")
            
            # Calculer la similarité
            similarity = self.calculate_similarity(generated_response, sample['output'])
            print(f"📊 Similarité: {similarity:.3f}")
            
            # Évaluer la fidélité
            if similarity > 0.6:
                print("🎉 TRÈS HAUTE FIDÉLITÉ")
                high_fidelity_count += 1
            elif similarity > 0.4:
                print("✅ HAUTE FIDÉLITÉ")
                high_fidelity_count += 1
            elif similarity > 0.25:
                print("⚠️ FIDÉLITÉ MOYENNE")
                medium_fidelity_count += 1
            else:
                print("❌ FAIBLE FIDÉLITÉ")
            
            results.append({
                'question': sample['input'],
                'expected': sample['output'],
                'generated': generated_response,
                'similarity': similarity,
                'high_fidelity': similarity > 0.4
            })
        
        # Statistiques détaillées
        total_high = high_fidelity_count
        total_medium = medium_fidelity_count
        total_low = num_tests - total_high - total_medium
        
        high_rate = (total_high / num_tests) * 100
        medium_rate = (total_medium / num_tests) * 100
        avg_similarity = sum(r['similarity'] for r in results) / len(results)
        
        print(f"\n📊 RÉSULTATS FIDÉLITÉ AVANCÉE:")
        print(f"🎉 Haute fidélité (>0.4): {total_high}/{num_tests} ({high_rate:.1f}%)")
        print(f"⚠️ Fidélité moyenne (0.25-0.4): {total_medium}/{num_tests} ({medium_rate:.1f}%)")
        print(f"❌ Faible fidélité (<0.25): {total_low}/{num_tests}")
        print(f"📈 Similarité moyenne: {avg_similarity:.3f}")
        
        # Évaluation globale
        if high_rate >= 80:
            print("🏆 EXCELLENT: Modèle très fidèle au dataset!")
        elif high_rate >= 60:
            print("🎯 TRÈS BON: Modèle généralement fidèle")
        elif high_rate >= 40:
            print("👍 BON: Fidélité acceptable")
        else:
            print("⚠️ ATTENTION: Fidélité insuffisante")
        
        return results
    
    def test_banking_categories(self):
        """Test par catégories bancaires"""
        print(f"\n🏦 Test par catégories bancaires")
        print("=" * 40)
        
        # Catégories bancaires
        categories = {
            "account": ["account", "savings", "current"],
            "fees": ["fee", "charge", "cost"],
            "loan": ["loan", "credit", "mortgage"],
            "card": ["card", "debit", "credit card"],
            "deposit": ["deposit", "withdrawal"]
        }
        
        category_results = {}
        
        for category, keywords in categories.items():
            print(f"\n📋 Catégorie: {category.upper()}")
            
            # Trouver des questions de cette catégorie
            category_questions = []
            for item in self.original_dataset['train']:
                question_lower = item['input'].lower()
                if any(keyword in question_lower for keyword in keywords):
                    category_questions.append(item)
            
            if category_questions:
                # Tester 3 questions de cette catégorie
                test_samples = random.sample(category_questions, min(3, len(category_questions)))
                
                similarities = []
                for sample in test_samples:
                    response = self.generate_response(sample['input'])
                    similarity = self.calculate_similarity(response, sample['output'])
                    similarities.append(similarity)
                    
                    print(f"  Q: {sample['input'][:60]}...")
                    print(f"  Fidélité: {similarity:.3f}")
                
                avg_similarity = sum(similarities) / len(similarities)
                category_results[category] = avg_similarity
                print(f"  📊 Moyenne catégorie: {avg_similarity:.3f}")
            else:
                print(f"  ❌ Aucune question trouvée")
        
        # Résumé par catégorie
        print(f"\n📊 RÉSUMÉ PAR CATÉGORIE:")
        for category, avg_sim in category_results.items():
            status = "🎉" if avg_sim > 0.4 else "⚠️" if avg_sim > 0.25 else "❌"
            print(f"  {status} {category.capitalize()}: {avg_sim:.3f}")
    
    def interactive_advanced_test(self):
        """Test interactif avancé"""
        print(f"\n💬 Test Interactif Avancé")
        print("=" * 30)
        print("Posez des questions bancaires (tapez 'quit' pour quitter)")
        
        while True:
            user_input = input("\n👤 Question: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                break
            
            if user_input:
                # Générer la réponse
                response = self.generate_response(user_input)
                print(f"🤖 Réponse: {response}")
                
                # Chercher une question similaire dans le dataset
                best_match = None
                best_similarity = 0
                
                for item in random.sample(list(self.original_dataset['train']), 200):
                    q_similarity = self.calculate_similarity(user_input, item['input'])
                    if q_similarity > best_similarity:
                        best_similarity = q_similarity
                        best_match = item
                
                if best_match and best_similarity > 0.3:
                    print(f"\n📋 Question similaire trouvée (sim: {best_similarity:.3f}):")
                    print(f"   Q: {best_match['input']}")
                    print(f"   R: {best_match['output'][:100]}...")
                    
                    r_similarity = self.calculate_similarity(response, best_match['output'])
                    print(f"📊 Fidélité de la réponse: {r_similarity:.3f}")

def main():
    """Fonction principale"""
    print("🎯 Test Modèle Avancé Haute Fidélité")
    print("=" * 40)
    
    # Vérifier si le modèle existe
    model_path = "./llama_banking_advanced_fidelity"
    if not Path(model_path).exists():
        print("❌ Modèle avancé non trouvé")
        print("💡 Lancez d'abord: python advanced_high_fidelity_finetuning.py")
        return
    
    tester = AdvancedFidelityTester(model_path)
    
    try:
        tester.load_components()
        
        print("\nOptions de test avancé:")
        print("1. 🧪 Test fidélité complet (15 questions)")
        print("2. 🏦 Test par catégories bancaires")
        print("3. 💬 Test interactif avancé")
        print("4. 📊 Test complet (toutes les options)")
        
        choice = input("\nVotre choix (1-4): ").strip()
        
        if choice == "1":
            tester.test_exact_fidelity(15)
        
        elif choice == "2":
            tester.test_banking_categories()
        
        elif choice == "3":
            tester.interactive_advanced_test()
        
        elif choice == "4":
            tester.test_exact_fidelity(15)
            tester.test_banking_categories()
            tester.interactive_advanced_test()
        
        else:
            print("❌ Choix invalide")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
