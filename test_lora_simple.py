"""
Test simple de la configuration LoRA
"""

import torch
from peft import LoraConfig, get_peft_model, TaskType
from transformers import AutoModelForCausalLM, AutoTokenizer
import json

def test_simple_lora():
    """Test simple avec GPT-2"""
    print("=== Test simple LoRA avec GPT-2 ===\n")
    
    # 1. Charger un modèle simple
    print("Chargement de GPT-2...")
    model_name = "gpt2"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print(f"Modèle chargé: {model.num_parameters():,} paramètres")
    
    # 2. Créer la configuration LoRA
    print("\nCréation de la configuration LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=8,  # Rank plus petit pour le test
        lora_alpha=16,
        lora_dropout=0.1,
        target_modules=["c_attn", "c_proj"],  # Modules GPT-2
        bias="none",
    )
    
    print("Configuration LoRA:")
    print(f"  - Rank: {lora_config.r}")
    print(f"  - Alpha: {lora_config.lora_alpha}")
    print(f"  - Target modules: {lora_config.target_modules}")
    
    # 3. Appliquer LoRA
    print("\nApplication de LoRA...")
    peft_model = get_peft_model(model, lora_config)
    peft_model.print_trainable_parameters()
    
    # 4. Test de génération
    print("\nTest de génération...")
    test_prompt = "Question: Quels sont les frais bancaires? Réponse:"
    inputs = tokenizer(test_prompt, return_tensors="pt")
    
    with torch.no_grad():
        outputs = peft_model.generate(
            **inputs,
            max_length=inputs['input_ids'].shape[1] + 50,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"Prompt: {test_prompt}")
    print(f"Génération: {generated_text[len(test_prompt):]}")
    
    # 5. Sauvegarder la configuration
    config_dict = {
        "model_name": model_name,
        "lora_config": {
            "r": lora_config.r,
            "lora_alpha": lora_config.lora_alpha,
            "lora_dropout": lora_config.lora_dropout,
            "target_modules": lora_config.target_modules,
            "bias": lora_config.bias,
        },
        "trainable_params": peft_model.get_nb_trainable_parameters(),
        "total_params": peft_model.num_parameters()
    }
    
    with open("lora_test_config.json", 'w') as f:
        json.dump(config_dict, f, indent=2)
    
    print(f"\nConfiguration sauvegardée dans lora_test_config.json")
    print("=== Test LoRA réussi ===")
    
    return peft_model, tokenizer

def test_ollama_connection():
    """Test de connexion à Ollama"""
    print("\n=== Test de connexion Ollama ===")
    
    try:
        import ollama
        client = ollama.Client(host="http://localhost:11434")
        
        # Lister les modèles disponibles
        models = client.list()
        print("Modèles Ollama disponibles:")
        for model in models['models']:
            print(f"  - {model['name']}")
        
        # Tester llama3.1:8b s'il est disponible
        model_names = [m['name'] for m in models['models']]
        if 'llama3.1:8b' in model_names:
            print(f"\nTest avec llama3.1:8b...")
            response = client.chat(
                model='llama3.1:8b',
                messages=[
                    {
                        'role': 'system',
                        'content': 'Vous êtes un assistant bancaire.'
                    },
                    {
                        'role': 'user',
                        'content': 'Bonjour, comment puis-je ouvrir un compte?'
                    }
                ]
            )
            print("Réponse:")
            print(response['message']['content'][:200] + "...")
        else:
            print("llama3.1:8b non trouvé dans les modèles disponibles")
        
        return True
        
    except Exception as e:
        print(f"Erreur de connexion Ollama: {e}")
        print("Assurez-vous qu'Ollama est démarré et accessible")
        return False

if __name__ == "__main__":
    # Test LoRA
    try:
        peft_model, tokenizer = test_simple_lora()
        print("✅ Configuration LoRA validée")
    except Exception as e:
        print(f"❌ Erreur LoRA: {e}")
    
    # Test Ollama
    try:
        ollama_ok = test_ollama_connection()
        if ollama_ok:
            print("✅ Connexion Ollama validée")
        else:
            print("⚠️ Problème de connexion Ollama")
    except Exception as e:
        print(f"❌ Erreur Ollama: {e}")
