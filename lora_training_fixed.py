"""
Fine-tuning LoRA CORRIGÉ pour chatbot bancaire
Ce script crée de vrais adapters LoRA utilisables
"""

import torch
import json
import os
from datasets import load_from_disk, Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
import ollama

class FixedLoRATrainer:
    """Trainer LoRA corrigé"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.ollama_client = ollama.Client()
    
    def test_ollama_baseline(self):
        """Teste le modèle de base via Ollama"""
        print("=== Test du modèle de base (Ollama) ===")
        
        try:
            response = self.ollama_client.chat(
                model='llama3.1:8b',
                messages=[
                    {'role': 'system', 'content': 'Vous êtes un assistant bancaire.'},
                    {'role': 'user', 'content': 'Quels sont les frais de compte?'}
                ]
            )
            
            print("✅ Modèle de base (Ollama):")
            print(response['message']['content'][:200] + "...")
            return True
            
        except Exception as e:
            print(f"❌ Erreur Ollama: {e}")
            return False
    
    def load_model(self):
        """Charge un modèle compatible"""
        print("\n=== Chargement du modèle ===")
        
        try:
            model_name = "gpt2"  # Modèle simple et fiable
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float32
            )
            
            print(f"✅ Modèle chargé: {model_name}")
            print(f"   Paramètres: {self.model.num_parameters():,}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def apply_lora(self):
        """Applique LoRA"""
        print("\n=== Application LoRA ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=8,
            lora_alpha=16,
            lora_dropout=0.1,
            target_modules=["c_attn", "c_proj"],
            bias="none",
        )
        
        self.peft_model = get_peft_model(self.model, lora_config)
        self.peft_model.print_trainable_parameters()
        
        print("✅ LoRA appliqué")
        return True
    
    def prepare_simple_data(self):
        """Prépare des données simples pour l'entraînement"""
        print("\n=== Préparation des données ===")
        
        # Données bancaires simples
        banking_data = [
            "Question: Quels sont les frais de compte? Réponse: Les frais de tenue de compte varient selon le type de compte.",
            "Question: Comment ouvrir un compte? Réponse: Pour ouvrir un compte, vous devez fournir une pièce d'identité et un justificatif de domicile.",
            "Question: Quels sont les horaires? Réponse: La banque est ouverte du lundi au vendredi de 9h à 17h.",
            "Question: Comment activer ma carte? Réponse: Vous pouvez activer votre carte en appelant le numéro au dos de la carte.",
            "Question: Quels documents pour un prêt? Réponse: Pour un prêt, vous devez fournir vos bulletins de salaire et justificatifs de revenus."
        ] * 20  # Répéter pour avoir plus de données
        
        # Créer le dataset
        dataset = Dataset.from_dict({"text": banking_data})
        
        # Tokeniser avec padding correct
        def tokenize_function(examples):
            # Tokeniser avec padding et truncation
            result = self.tokenizer(
                examples["text"],
                truncation=True,
                padding="max_length",
                max_length=128,
                return_tensors=None
            )
            
            # Les labels sont les mêmes que input_ids pour le language modeling
            result["labels"] = result["input_ids"].copy()
            return result
        
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=["text"]
        )
        
        # Diviser en train/validation
        split_dataset = tokenized_dataset.train_test_split(test_size=0.2)
        
        print(f"✅ Données préparées:")
        print(f"   Train: {len(split_dataset['train'])} exemples")
        print(f"   Validation: {len(split_dataset['test'])} exemples")
        
        return split_dataset
    
    def train_lora(self, dataset):
        """Lance l'entraînement LoRA"""
        print("\n=== Entraînement LoRA ===")
        
        # Configuration d'entraînement
        training_args = TrainingArguments(
            output_dir="./banking_lora_adapters",
            num_train_epochs=2,
            per_device_train_batch_size=4,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=2,
            learning_rate=2e-4,
            weight_decay=0.01,
            warmup_steps=10,
            logging_steps=5,
            eval_steps=20,
            save_steps=20,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            report_to=None,
            remove_unused_columns=False,
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["test"],
            data_collator=data_collator,
        )
        
        print("🚀 Démarrage de l'entraînement...")
        
        try:
            trainer.train()
            
            # Sauvegarder les adapters LoRA
            trainer.save_model()
            self.tokenizer.save_pretrained("./banking_lora_adapters")
            
            print("✅ Entraînement terminé!")
            print("✅ Adapters LoRA sauvegardés dans: ./banking_lora_adapters")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_lora_model(self):
        """Teste le modèle avec LoRA"""
        print("\n=== Test du modèle avec LoRA ===")
        
        if self.peft_model is None:
            print("❌ Modèle LoRA non disponible")
            return
        
        test_prompts = [
            "Question: Quels sont les frais de compte? Réponse:",
            "Question: Comment ouvrir un compte? Réponse:",
            "Question: Quels documents pour un prêt? Réponse:"
        ]
        
        for prompt in test_prompts:
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 50,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\n🔹 {prompt.replace('Question: ', '').replace(' Réponse:', '')}")
            print(f"   Réponse LoRA: {response}")
    
    def create_lora_integration_script(self):
        """Crée un script pour utiliser les adapters LoRA"""
        script_content = '''"""
Script pour utiliser le modèle avec adapters LoRA
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import ollama

class BankingChatbotWithLoRA:
    def __init__(self):
        self.base_model = None
        self.lora_model = None
        self.tokenizer = None
        self.ollama_client = ollama.Client()
        
    def load_lora_model(self):
        """Charge le modèle de base + adapters LoRA"""
        print("Chargement du modèle avec adapters LoRA...")
        
        try:
            # Charger le modèle de base
            base_model_name = "gpt2"
            self.tokenizer = AutoTokenizer.from_pretrained(base_model_name)
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.base_model = AutoModelForCausalLM.from_pretrained(base_model_name)
            
            # Charger les adapters LoRA
            self.lora_model = PeftModel.from_pretrained(
                self.base_model, 
                "./banking_lora_adapters"
            )
            
            print("✅ Modèle LoRA chargé avec succès!")
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement LoRA: {e}")
            return False
    
    def chat_with_lora(self, question):
        """Chat avec le modèle LoRA"""
        if self.lora_model is None:
            return "Modèle LoRA non chargé"
        
        prompt = f"Question: {question} Réponse:"
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.lora_model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(prompt):].strip()
        return response
    
    def chat_with_ollama(self, question):
        """Chat avec Ollama (pour comparaison)"""
        try:
            response = self.ollama_client.chat(
                model='llama3.1:8b',
                messages=[
                    {'role': 'system', 'content': 'Vous êtes un assistant bancaire.'},
                    {'role': 'user', 'content': question}
                ]
            )
            return response['message']['content']
        except:
            return "Erreur Ollama"
    
    def compare_responses(self, question):
        """Compare les réponses LoRA vs Ollama"""
        print(f"\\n❓ Question: {question}")
        print("\\n🔵 Réponse Ollama (llama3.1:8b):")
        ollama_response = self.chat_with_ollama(question)
        print(ollama_response[:200] + "...")
        
        print("\\n🟢 Réponse LoRA (fine-tuné):")
        lora_response = self.chat_with_lora(question)
        print(lora_response)
        print("-" * 60)

def main():
    chatbot = BankingChatbotWithLoRA()
    
    if chatbot.load_lora_model():
        print("\\n🎯 Comparaison des modèles:")
        
        questions = [
            "Quels sont les frais de compte?",
            "Comment ouvrir un compte épargne?",
            "Quels documents pour un prêt?"
        ]
        
        for question in questions:
            chatbot.compare_responses(question)
    else:
        print("❌ Impossible de charger le modèle LoRA")

if __name__ == "__main__":
    main()
'''
        
        with open("use_lora_model.py", 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("✅ Script d'utilisation LoRA créé: use_lora_model.py")

def main():
    """Fonction principale"""
    print("🏦 Fine-tuning LoRA CORRIGÉ")
    print("=" * 40)
    
    trainer = FixedLoRATrainer()
    
    # 1. Test Ollama
    trainer.test_ollama_baseline()
    
    # 2. Charger le modèle
    if not trainer.load_model():
        return
    
    # 3. Appliquer LoRA
    trainer.apply_lora()
    
    # 4. Préparer les données
    dataset = trainer.prepare_simple_data()
    
    # 5. Entraîner
    print("\\nVoulez-vous lancer l'entraînement LoRA? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if trainer.train_lora(dataset):
            print("\\n🎉 Fine-tuning LoRA réussi!")
            trainer.test_lora_model()
            trainer.create_lora_integration_script()
            
            print("\\n" + "=" * 50)
            print("📁 FICHIERS CRÉÉS:")
            print("✅ ./banking_lora_adapters/ - Adapters LoRA")
            print("✅ use_lora_model.py - Script d'utilisation")
            print("\\n🚀 POUR UTILISER LE MODÈLE FINE-TUNÉ:")
            print("python use_lora_model.py")
        else:
            print("❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé")

if __name__ == "__main__":
    main()
