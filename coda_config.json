{"model_config": {"base_model": "microsoft/DialoGPT-medium", "model_type": "causal_lm", "torch_dtype": "float16", "device_map": "auto"}, "lora_config": {"task_type": "CAUSAL_LM", "r": 16, "lora_alpha": 32, "lora_dropout": 0.1, "target_modules": ["c_attn", "c_proj"], "bias": "none", "inference_mode": false}, "training_config": {"output_dir": "./coda_banking_model", "num_train_epochs": 3, "per_device_train_batch_size": 4, "per_device_eval_batch_size": 4, "gradient_accumulation_steps": 4, "learning_rate": 0.0002, "weight_decay": 0.01, "warmup_steps": 100, "logging_steps": 10, "eval_steps": 100, "save_steps": 100, "max_length": 512, "fp16": true, "dataloader_pin_memory": false, "remove_unused_columns": false}, "data_config": {"dataset_path": "./processed_data", "max_samples": null, "train_split": 0.8, "val_split": 0.1, "test_split": 0.1}}