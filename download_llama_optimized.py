"""
Téléchargement optimisé de Llama-3.1-8B-Instruct avec hf_xet
"""

import os
import time
from pathlib import Path

def check_existing_download():
    """Vérifie si le modèle existe déjà"""
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    if local_dir.exists():
        files = list(local_dir.glob("*"))
        if files:
            total_size = sum(f.stat().st_size for f in files if f.is_file()) / (1024**3)
            print(f"📁 Modèle existant détecté:")
            print(f"  📍 Emplacement: {local_dir}")
            print(f"  📄 Fichiers: {len(files)}")
            print(f"  💾 Taille actuelle: {total_size:.1f} GB")
            
            # Vérifier si le téléchargement semble complet
            expected_files = [
                "model-00001-of-00004.safetensors",
                "model-00002-of-00004.safetensors", 
                "model-00003-of-00004.safetensors",
                "model-00004-of-00004.safetensors",
                "tokenizer.model",
                "config.json"
            ]
            
            existing_files = [f.name for f in files]
            missing_files = [f for f in expected_files if f not in existing_files]
            
            if not missing_files and total_size > 15:
                print(f"✅ Téléchargement semble complet ({total_size:.1f} GB)")
                return True, total_size
            else:
                print(f"⚠️ Téléchargement incomplet")
                if missing_files:
                    print(f"  📄 Fichiers manquants: {missing_files[:3]}...")
                return False, total_size
    
    return False, 0

def download_llama_optimized():
    """Télécharge Llama avec toutes les optimisations"""
    print("🦙 Téléchargement Optimisé Llama-3.1-8B-Instruct")
    print("=" * 60)
    
    # Configuration
    token = "*************************************"
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    print(f"🎯 Modèle: {model_id}")
    print(f"📁 Destination: {local_dir}")
    print(f"⚡ Optimisations: hf_xet activé")
    
    # Vérifier le téléchargement existant
    is_complete, current_size = check_existing_download()
    
    if is_complete:
        choice = input(f"\nModèle complet détecté. Re-télécharger? (y/n): ").lower()
        if choice != 'y':
            print("✅ Utilisation du modèle existant")
            return True
    elif current_size > 0:
        print(f"\n🔄 Reprise du téléchargement depuis {current_size:.1f} GB")
    
    # Créer le répertoire
    local_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n📊 Informations de téléchargement:")
    print(f"  💾 Taille totale: ~16 GB")
    print(f"  ⏱️ Temps estimé: 30-60 minutes")
    print(f"  🔄 Reprise automatique: Activée")
    print(f"  ⚡ hf_xet: Activé (téléchargement plus rapide)")
    
    confirm = input(f"\nCommencer le téléchargement? (y/n): ").lower()
    if confirm != 'y':
        print("Téléchargement annulé")
        return False
    
    try:
        from huggingface_hub import snapshot_download
        
        print(f"\n🚀 Début du téléchargement optimisé...")
        print(f"💡 Avec hf_xet, le téléchargement devrait être plus rapide")
        print(f"📊 Progression:")
        print("-" * 60)
        
        start_time = time.time()
        
        # Téléchargement avec toutes les optimisations
        snapshot_download(
            repo_id=model_id,
            local_dir=str(local_dir),
            local_dir_use_symlinks=False,
            resume_download=True,
            token=token,
            max_workers=8,  # Plus de parallélisation
        )
        
        end_time = time.time()
        duration_minutes = (end_time - start_time) / 60
        
        print(f"\n" + "=" * 60)
        print(f"🎉 TÉLÉCHARGEMENT TERMINÉ!")
        print(f"=" * 60)
        
        # Vérifier le résultat
        files = list(local_dir.glob("*"))
        total_size = sum(f.stat().st_size for f in files if f.is_file()) / (1024**3)
        
        print(f"✅ Modèle: {model_id}")
        print(f"📁 Emplacement: {local_dir}")
        print(f"💾 Taille finale: {total_size:.1f} GB")
        print(f"📄 Fichiers: {len(files)}")
        print(f"⏱️ Durée: {duration_minutes:.1f} minutes")
        
        # Lister les fichiers principaux
        model_files = list(local_dir.glob("*.safetensors"))
        print(f"\n📄 Fichiers du modèle:")
        for file in model_files:
            size_gb = file.stat().st_size / (1024**3)
            print(f"  ✅ {file.name} ({size_gb:.1f} GB)")
        
        # Vérifier les fichiers essentiels
        essential_files = ["config.json", "tokenizer.model", "tokenizer.json"]
        print(f"\n🔧 Fichiers de configuration:")
        for file_name in essential_files:
            file_path = local_dir / file_name
            if file_path.exists():
                print(f"  ✅ {file_name}")
            else:
                print(f"  ❌ {file_name} manquant")
        
        # Créer un fichier de statut
        import json
        status = {
            "model_id": model_id,
            "local_path": str(local_dir),
            "download_complete": True,
            "total_size_gb": total_size,
            "files_count": len(files),
            "download_duration_minutes": duration_minutes,
            "optimizations_used": ["hf_xet", "resume_download", "max_workers_8"],
            "cuda_ready": True
        }
        
        with open("llama_model_ready.json", 'w') as f:
            json.dump(status, f, indent=2)
        
        print(f"\n✅ Statut sauvegardé: llama_model_ready.json")
        print(f"🎯 Modèle prêt pour CUDA et fine-tuning!")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n⏸️ Téléchargement interrompu")
        print(f"💡 Relancez le script pour reprendre")
        return False
        
    except Exception as e:
        print(f"\n❌ Erreur de téléchargement: {e}")
        print(f"💡 Vérifiez votre connexion et réessayez")
        return False

def verify_cuda_compatibility():
    """Vérifie que le modèle sera compatible avec CUDA"""
    print(f"\n🔍 Vérification compatibilité CUDA...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            print(f"✅ CUDA disponible")
            print(f"🎮 GPU: {gpu_name}")
            print(f"💾 Mémoire GPU: {gpu_memory:.1f} GB")
            
            if gpu_memory >= 16:
                print(f"✅ Mémoire suffisante pour Llama-3.1-8B")
            elif gpu_memory >= 8:
                print(f"⚠️ Mémoire limite - quantification 4-bit recommandée")
            else:
                print(f"❌ Mémoire insuffisante pour Llama-3.1-8B")
            
            return True
        else:
            print(f"❌ CUDA non disponible")
            return False
            
    except ImportError:
        print(f"⚠️ PyTorch non installé")
        return False

def main():
    """Fonction principale"""
    print("🚀 Téléchargement Llama-3.1-8B-Instruct pour CUDA")
    print("=" * 60)
    
    # Vérifier CUDA
    cuda_ok = verify_cuda_compatibility()
    
    if not cuda_ok:
        print(f"\n⚠️ CUDA non détecté")
        choice = input("Continuer quand même? (y/n): ").lower()
        if choice != 'y':
            print("Téléchargement annulé")
            return
    
    # Lancer le téléchargement
    if download_llama_optimized():
        print(f"\n🎉 SUCCÈS!")
        print(f"🚀 Prochaines étapes:")
        print(f"  1. python prepare_banking_dataset.py")
        print(f"  2. python llama_finetuning_cuda.py")
        print(f"  3. python test_finetuned_llama.py")
    else:
        print(f"\n❌ Téléchargement échoué")

if __name__ == "__main__":
    main()
