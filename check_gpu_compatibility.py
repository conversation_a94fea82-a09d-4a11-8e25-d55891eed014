"""
Vérification de la compatibilité GPU pour CUDA
"""

import subprocess
import re
import platform

def check_nvidia_gpu():
    """Vérifie la présence et les spécifications du GPU NVIDIA"""
    print("=== Vérification GPU NVIDIA ===")
    
    try:
        # Exécuter nvidia-smi
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            print("✅ GPU NVIDIA détecté!")
            
            # Extraire les informations du GPU
            gpu_lines = [line for line in output.split('\n') if 'GeForce' in line or 'RTX' in line or 'GTX' in line or 'Quadro' in line or 'Tesla' in line]
            
            for line in gpu_lines:
                print(f"🎮 GPU: {line.strip()}")
            
            # Extraire la version du driver
            driver_match = re.search(r'Driver Version: (\d+\.\d+)', output)
            if driver_match:
                driver_version = driver_match.group(1)
                print(f"🔧 Driver NVIDIA: {driver_version}")
                
                # Recommandations basées sur la version du driver
                driver_float = float(driver_version)
                if driver_float >= 470.0:
                    print("✅ Driver compatible avec CUDA 11.8")
                    return "cuda_11_8"
                elif driver_float >= 450.0:
                    print("✅ Driver compatible avec CUDA 11.0")
                    return "cuda_11_0"
                else:
                    print("⚠️ Driver ancien, mise à jour recommandée")
                    return "update_driver"
            
            # Extraire la mémoire GPU
            memory_lines = [line for line in output.split('\n') if 'MiB' in line and '/' in line]
            for line in memory_lines:
                if 'MiB' in line:
                    memory_match = re.search(r'(\d+)MiB / (\d+)MiB', line)
                    if memory_match:
                        used_memory = int(memory_match.group(1))
                        total_memory = int(memory_match.group(2))
                        total_gb = total_memory / 1024
                        print(f"💾 Mémoire GPU: {total_gb:.1f} GB")
                        
                        if total_gb >= 16:
                            print("✅ Mémoire suffisante pour Llama 3.1 8B")
                        elif total_gb >= 12:
                            print("⚠️ Mémoire limite, quantification 4-bit recommandée")
                        else:
                            print("❌ Mémoire insuffisante pour Llama 3.1 8B")
            
            return "cuda_11_8"
            
        else:
            print("❌ nvidia-smi non trouvé")
            print("💡 Vérifiez que les drivers NVIDIA sont installés")
            return None
            
    except FileNotFoundError:
        print("❌ nvidia-smi non disponible")
        print("💡 Installez les drivers NVIDIA d'abord")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def get_cuda_recommendation():
    """Donne des recommandations CUDA spécifiques"""
    print("\n=== Recommandations CUDA ===")
    
    gpu_status = check_nvidia_gpu()
    
    if gpu_status == "cuda_11_8":
        print("\n🎯 RECOMMANDATION:")
        print("  Operating System: Windows")
        print("  Architecture: x86_64") 
        print("  Version: 11 (CUDA 11.8)")
        print("  Installer Type: exe (network) - plus rapide")
        
        print("\n📥 Lien de téléchargement:")
        print("  https://developer.nvidia.com/cuda-11-8-0-download-archive")
        
        print("\n🔧 Après installation:")
        print("  1. Redémarrer l'ordinateur")
        print("  2. Vérifier: nvcc --version")
        print("  3. Installer PyTorch avec CUDA 11.8")
        
    elif gpu_status == "cuda_11_0":
        print("\n🎯 RECOMMANDATION:")
        print("  Operating System: Windows")
        print("  Architecture: x86_64")
        print("  Version: 11 (CUDA 11.0)")
        
    elif gpu_status == "update_driver":
        print("\n⚠️ MISE À JOUR REQUISE:")
        print("  1. Mettre à jour les drivers NVIDIA")
        print("  2. Puis installer CUDA 11.8")
        
    else:
        print("\n❌ GPU NVIDIA requis pour le fine-tuning")
        print("💡 Alternatives:")
        print("  - Google Colab Pro")
        print("  - Services cloud (AWS, Azure)")
        print("  - Fine-tuning CPU (très lent)")

def check_system_compatibility():
    """Vérifie la compatibilité du système"""
    print("\n=== Compatibilité Système ===")
    
    # Système d'exploitation
    os_name = platform.system()
    print(f"OS: {os_name}")
    
    if os_name == "Windows":
        import platform
        version = platform.version()
        print(f"Version Windows: {version}")
        print("✅ Compatible avec CUDA")
    else:
        print("⚠️ Ce script est optimisé pour Windows")
    
    # Architecture
    arch = platform.machine()
    print(f"Architecture: {arch}")
    
    if arch in ["AMD64", "x86_64"]:
        print("✅ Architecture x86_64 compatible")
    else:
        print("⚠️ Architecture non standard")

def main():
    """Fonction principale"""
    print("🔍 Vérification de compatibilité CUDA")
    print("=" * 50)
    
    check_system_compatibility()
    recommendation = get_cuda_recommendation()
    
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ:")
    print("Pour votre configuration, téléchargez:")
    print("  🖥️ Operating System: Windows")
    print("  🏗️ Architecture: x86_64")
    print("  📦 Version: 11 (recommandé pour ML)")
    print("  💾 Installer Type: exe (network)")
    
    print("\n🔗 Lien direct:")
    print("https://developer.nvidia.com/cuda-downloads")
    
    print("\n⚡ Commandes de vérification post-installation:")
    print("  nvcc --version")
    print("  nvidia-smi")

if __name__ == "__main__":
    main()
