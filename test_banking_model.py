"""
Test du modèle bancaire personnalisé
"""

import ollama

def test_banking_model():
    print("Test du modèle banking-assistant")
    print("=" * 40)
    
    client = ollama.Client()
    
    # Vérifier que le modèle existe
    try:
        models = client.list()
        model_names = [m.get('name', '') for m in models['models']]
        
        if 'banking-assistant:latest' not in model_names:
            print("Erreur: Modèle banking-assistant non trouvé")
            print("Exécutez d'abord: create_banking_model.bat")
            return
        
        print("Modèle banking-assistant trouvé!")
        
    except Exception as e:
        print(f"Erreur de connexion Ollama: {e}")
        return
    
    # Questions de test
    test_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Quelles sont les conditions pour un prêt immobilier?",
        "Comment activer ma carte bancaire?",
        "Que faire en cas de perte de carte?",
        "Quels sont les horaires d'ouverture?",
        "Comment faire un virement international?",
        "Quels documents pour ouvrir un compte?"
    ]
    
    print("\nTest avec questions bancaires:")
    print("-" * 40)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Question: {question}")
        
        try:
            response = client.chat(
                model='banking-assistant',
                messages=[
                    {'role': 'user', 'content': question}
                ]
            )
            
            answer = response['message']['content']
            print(f"   Réponse: {answer[:150]}...")
            
        except Exception as e:
            print(f"   Erreur: {e}")
    
    print("\n" + "=" * 40)
    print("Test terminé!")

def compare_models():
    """Compare le modèle de base et le modèle personnalisé"""
    print("\nComparaison des modèles")
    print("=" * 30)
    
    client = ollama.Client()
    test_question = "Quels sont les frais de compte?"
    
    print(f"Question: {test_question}")
    
    # Modèle de base
    print("\nModèle de base (llama3.1:8b):")
    try:
        response = client.chat(
            model='llama3.1:8b',
            messages=[{'role': 'user', 'content': test_question}]
        )
        print(response['message']['content'][:200] + "...")
    except:
        print("Erreur avec le modèle de base")
    
    # Modèle personnalisé
    print("\nModèle personnalisé (banking-assistant):")
    try:
        response = client.chat(
            model='banking-assistant',
            messages=[{'role': 'user', 'content': test_question}]
        )
        print(response['message']['content'][:200] + "...")
    except:
        print("Erreur avec le modèle personnalisé")

if __name__ == "__main__":
    test_banking_model()
    compare_models()
