# Requirements pour les améliorations du chatbot bancaire

# Base existante (déjà installé)
torch>=2.0.0
transformers>=4.30.0
peft>=0.4.0
datasets>=2.12.0
scikit-learn>=1.3.0
numpy>=1.24.0
pillow>=9.5.0
pytesseract>=0.3.10
opencv-python>=4.7.0

# Nouvelles dépendances pour les améliorations

# === BASE DE DONNÉES ===
# Option 1: SQLite (déjà inclus avec Python)
# Option 2: PostgreSQL
psycopg2-binary>=2.9.7
sqlalchemy>=2.0.0

# Option 3: MongoDB
pymongo>=4.5.0
motor>=3.3.0  # Pour async MongoDB

# === APIs VOCALES ===
# OpenAI (Whisper + TTS)
openai>=1.0.0

# Google Cloud Speech
google-cloud-speech>=2.21.0
google-cloud-texttospeech>=2.14.0

# Azure Speech Services
azure-cognitiveservices-speech>=1.32.0

# === API WEB ===
# FastAPI pour les endpoints
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6

# === UTILITAIRES ===
# Gestion des variables d'environnement
python-dotenv>=1.0.0

# Validation des données
pydantic>=2.4.0

# Gestion des dates
python-dateutil>=2.8.2

# Logging amélioré
loguru>=0.7.0

# === SÉCURITÉ ===
# Authentification JWT
pyjwt>=2.8.0
passlib>=1.7.4
bcrypt>=4.0.1

# === MONITORING ===
# Métriques et monitoring
prometheus-client>=0.17.0

# === DÉVELOPPEMENT ===
# Tests
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Formatage de code
black>=23.0.0
isort>=5.12.0

# Linting
flake8>=6.0.0
mypy>=1.5.0
