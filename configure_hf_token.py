"""
Configuration automatique du token Hugging Face
"""

import os
import subprocess
import sys

def setup_token():
    """Configure le token Hugging Face"""
    print("🔑 Configuration du token Hugging Face...")
    
    # Votre token
    token = "*************************************"
    
    try:
        # Installer huggingface_hub si nécessaire
        try:
            import huggingface_hub
        except ImportError:
            print("📦 Installation de huggingface_hub...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "huggingface_hub"])
        
        # Configurer le token
        os.environ['HUGGINGFACE_HUB_TOKEN'] = token
        
        # Tester la connexion
        from huggingface_hub import HfApi
        api = HfApi(token=token)
        user_info = api.whoami(token=token)
        
        print(f"✅ Connecté en tant que: {user_info['name']}")
        print(f"📧 Email: {user_info.get('email', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_llama_access():
    """Teste l'accès à Llama 3.1"""
    print("\n🦙 Test d'accès à Llama 3.1 8B...")
    
    try:
        from huggingface_hub import HfApi
        api = HfApi(token="*************************************")
        
        model_id = "meta-llama/Llama-3.1-8B-Instruct"
        
        try:
            model_info = api.model_info(model_id)
            print(f"✅ Accès confirmé à {model_id}")
            print(f"📊 Modèle disponible pour téléchargement")
            return True
            
        except Exception as e:
            if "gated" in str(e).lower() or "access" in str(e).lower():
                print(f"🔒 Accès requis au modèle")
                print(f"🌐 Demandez l'accès sur: https://huggingface.co/{model_id}")
                return False
            else:
                print(f"❌ Erreur: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de test: {e}")
        return False

def download_llama():
    """Télécharge Llama 3.1 8B"""
    print("\n📥 Téléchargement de Llama 3.1 8B...")
    
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = "./models/Llama-3.1-8B-Instruct"
    
    print(f"📁 Destination: {local_dir}")
    print(f"⚠️ Taille: ~16GB")
    print(f"⏱️ Temps estimé: 30-60 minutes selon votre connexion")
    
    confirm = input("\nCommencer le téléchargement? (y/n): ").lower()
    if confirm != 'y':
        print("Téléchargement annulé")
        return False
    
    try:
        from huggingface_hub import snapshot_download
        
        print("🔄 Téléchargement en cours...")
        print("💡 Vous pouvez interrompre avec Ctrl+C et reprendre plus tard")
        
        snapshot_download(
            repo_id=model_id,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True,
            token="*************************************"
        )
        
        print(f"\n✅ Modèle téléchargé avec succès!")
        print(f"📁 Emplacement: {local_dir}")
        
        # Créer un fichier de configuration
        import json
        config = {
            "model_name": model_id,
            "local_path": local_dir,
            "download_complete": True,
            "token_configured": True
        }
        
        with open("llama_config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuration sauvegardée: llama_config.json")
        return True
        
    except KeyboardInterrupt:
        print("\n⏸️ Téléchargement interrompu")
        print("💡 Relancez le script pour reprendre")
        return False
    except Exception as e:
        print(f"\n❌ Erreur de téléchargement: {e}")
        return False

def main():
    """Fonction principale"""
    print("🦙 Configuration Llama 3.1 8B")
    print("=" * 40)
    
    # 1. Configurer le token
    if not setup_token():
        print("❌ Échec de configuration du token")
        return
    
    # 2. Tester l'accès
    if not test_llama_access():
        print("\n💡 Actions requises:")
        print("1. Allez sur: https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct")
        print("2. Cliquez sur 'Request access'")
        print("3. Remplissez le formulaire")
        print("4. Attendez l'approbation (généralement rapide)")
        print("5. Relancez ce script")
        return
    
    # 3. Menu de téléchargement
    print("\n" + "=" * 40)
    print("OPTIONS:")
    print("1. 📥 Télécharger le modèle maintenant")
    print("2. ⏭️ Continuer sans télécharger")
    print("3. 🚪 Quitter")
    
    choice = input("Votre choix (1-3): ").strip()
    
    if choice == "1":
        if download_llama():
            print("\n🎉 Prêt pour le fine-tuning!")
            print("🚀 Prochaine étape: python prepare_banking_dataset.py")
    
    elif choice == "2":
        print("✅ Token configuré")
        print("💡 Vous pouvez télécharger plus tard")
    
    elif choice == "3":
        print("👋 Au revoir!")
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
