"""
Vérification des réponses du chatbot contre le dataset original
"""

import json
import torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from pathlib import Path
import difflib
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class ResponseVerifier:
    """Vérificateur de réponses contre le dataset original"""
    
    def __init__(self):
        self.original_dataset = None
        self.model = None
        self.tokenizer = None
        self.load_original_dataset()
        self.load_finetuned_model()
    
    def load_original_dataset(self):
        """Charge le dataset original wasifis/bank-assistant-qa"""
        print("📊 Chargement du dataset original...")
        
        try:
            self.original_dataset = load_dataset("wasifis/bank-assistant-qa")
            print(f"✅ Dataset chargé: {len(self.original_dataset['train'])} exemples")
            
            # Créer un index pour recherche rapide
            self.qa_pairs = []
            for item in self.original_dataset['train']:
                self.qa_pairs.append({
                    'input': item['input'],
                    'output': item['output'],
                    'instruction': item.get('instruction', '')
                })
            
            print(f"✅ Index créé: {len(self.qa_pairs)} paires Q&A")
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
    
    def load_finetuned_model(self):
        """Charge le modèle fine-tuné"""
        print("🦙 Chargement du modèle fine-tuné...")
        
        try:
            # Configuration 4-bit
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Tokenizer
            model_path = "./models/Llama-3.1-8B-Instruct"
            if Path(model_path).exists():
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Modèle de base
            if Path(model_path).exists():
                base_model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            else:
                base_model = AutoModelForCausalLM.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    token="*************************************"
                )
            
            # Modèle fine-tuné
            self.model = PeftModel.from_pretrained(base_model, "./llama_banking_fresh")
            
            print("✅ Modèle fine-tuné chargé")
            
        except Exception as e:
            print(f"❌ Erreur de chargement modèle: {e}")
    
    def find_similar_questions(self, question, top_k=5):
        """Trouve les questions similaires dans le dataset"""
        
        # Extraire toutes les questions du dataset
        dataset_questions = [item['input'] for item in self.qa_pairs]
        
        # Vectorisation TF-IDF
        vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
        
        # Ajouter la question de test
        all_questions = dataset_questions + [question]
        tfidf_matrix = vectorizer.fit_transform(all_questions)
        
        # Calculer la similarité cosinus
        query_vector = tfidf_matrix[-1]  # Dernière question (celle de test)
        similarities = cosine_similarity(query_vector, tfidf_matrix[:-1]).flatten()
        
        # Trouver les top_k plus similaires
        top_indices = similarities.argsort()[-top_k:][::-1]
        
        results = []
        for idx in top_indices:
            results.append({
                'question': dataset_questions[idx],
                'answer': self.qa_pairs[idx]['output'],
                'similarity': similarities[idx],
                'index': idx
            })
        
        return results
    
    def generate_response(self, question):
        """Génère une réponse avec le modèle fine-tuné"""
        
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel.<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 150,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        return response
    
    def calculate_similarity_score(self, text1, text2):
        """Calcule la similarité entre deux textes"""
        
        # Similarité TF-IDF
        vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
        tfidf_matrix = vectorizer.fit_transform([text1, text2])
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        
        return similarity
    
    def verify_response(self, question):
        """Vérifie une réponse contre le dataset"""
        print(f"\n🔍 Vérification de la question: {question}")
        print("=" * 60)
        
        # 1. Trouver les questions similaires dans le dataset
        similar_questions = self.find_similar_questions(question, top_k=3)
        
        print(f"📊 Questions similaires dans le dataset:")
        for i, item in enumerate(similar_questions, 1):
            print(f"\n{i}. Similarité: {item['similarity']:.3f}")
            print(f"   Q: {item['question'][:100]}...")
            print(f"   R: {item['answer'][:100]}...")
        
        # 2. Générer la réponse du modèle fine-tuné
        print(f"\n🤖 Génération de la réponse...")
        generated_response = self.generate_response(question)
        print(f"Réponse générée: {generated_response}")
        
        # 3. Comparer avec les réponses du dataset
        print(f"\n📈 Analyse de similarité:")
        
        best_match = None
        best_similarity = 0
        
        for item in similar_questions:
            similarity = self.calculate_similarity_score(generated_response, item['answer'])
            print(f"Similarité avec réponse {item['index']}: {similarity:.3f}")
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = item
        
        # 4. Verdict
        print(f"\n🎯 VERDICT:")
        if best_similarity > 0.5:
            print(f"✅ RÉPONSE COHÉRENTE avec le dataset (similarité: {best_similarity:.3f})")
            print(f"📋 Meilleure correspondance:")
            print(f"   Q: {best_match['question']}")
            print(f"   R: {best_match['answer'][:200]}...")
        elif best_similarity > 0.3:
            print(f"⚠️ RÉPONSE PARTIELLEMENT COHÉRENTE (similarité: {best_similarity:.3f})")
        else:
            print(f"❌ RÉPONSE PEU COHÉRENTE avec le dataset (similarité: {best_similarity:.3f})")
            print(f"💡 Le modèle pourrait halluciner ou généraliser")
        
        return {
            'question': question,
            'generated_response': generated_response,
            'best_match': best_match,
            'similarity_score': best_similarity,
            'is_coherent': best_similarity > 0.5
        }
    
    def batch_verification(self, test_questions):
        """Vérifie plusieurs questions"""
        print(f"\n🧪 Vérification par lot de {len(test_questions)} questions")
        print("=" * 60)
        
        results = []
        coherent_count = 0
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n--- Test {i}/{len(test_questions)} ---")
            result = self.verify_response(question)
            results.append(result)
            
            if result['is_coherent']:
                coherent_count += 1
        
        # Statistiques finales
        coherence_rate = (coherent_count / len(test_questions)) * 100
        
        print(f"\n📊 STATISTIQUES FINALES:")
        print(f"✅ Réponses cohérentes: {coherent_count}/{len(test_questions)} ({coherence_rate:.1f}%)")
        print(f"⚠️ Réponses incohérentes: {len(test_questions) - coherent_count}")
        
        if coherence_rate >= 80:
            print(f"🎉 EXCELLENT: Le modèle est bien aligné avec votre dataset!")
        elif coherence_rate >= 60:
            print(f"👍 BON: Le modèle suit généralement votre dataset")
        else:
            print(f"⚠️ ATTENTION: Le modèle s'écarte souvent du dataset")
        
        return results

def main():
    """Fonction principale"""
    print("🔍 Vérificateur de Réponses - Dataset vs Modèle Fine-tuné")
    print("=" * 60)
    
    verifier = ResponseVerifier()
    
    if verifier.original_dataset is None or verifier.model is None:
        print("❌ Impossible de charger les composants nécessaires")
        return
    
    # Questions de test
    test_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Que faire en cas de perte de carte?",
        "Quelles sont les conditions pour un prêt?",
        "Comment activer ma carte bancaire?"
    ]
    
    print("Options:")
    print("1. 🔍 Vérification d'une question spécifique")
    print("2. 🧪 Vérification par lot (5 questions)")
    print("3. 💬 Vérification interactive")
    
    choice = input("\nVotre choix (1-3): ").strip()
    
    if choice == "1":
        question = input("Entrez votre question: ").strip()
        if question:
            verifier.verify_response(question)
    
    elif choice == "2":
        verifier.batch_verification(test_questions)
    
    elif choice == "3":
        print("\n💬 Mode interactif (tapez 'quit' pour quitter)")
        while True:
            question = input("\nQuestion à vérifier: ").strip()
            if question.lower() in ['quit', 'exit']:
                break
            if question:
                verifier.verify_response(question)
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
