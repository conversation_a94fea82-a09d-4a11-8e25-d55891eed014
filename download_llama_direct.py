"""
Téléchargement direct de Llama 3.1 8B
"""

import os
import sys
import subprocess
from pathlib import Path

def install_requirements():
    """Installe les dépendances nécessaires"""
    print("📦 Installation des dépendances...")
    
    packages = [
        "huggingface_hub",
        "transformers",
        "torch",
        "accelerate"
    ]
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} déjà installé")
        except ImportError:
            print(f"🔄 Installation de {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def download_llama():
    """Télécharge Llama 3.1 8B directement"""
    print("🦙 Téléchargement de Llama 3.1 8B")
    print("=" * 50)
    
    # Configuration
    token = "*************************************"
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    print(f"📥 Modèle: {model_id}")
    print(f"📁 Destination: {local_dir}")
    print(f"💾 Taille estimée: ~16 GB")
    print(f"⏱️ Temps estimé: 30-60 minutes")
    
    # Créer le répertoire
    local_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        from huggingface_hub import snapshot_download
        
        print(f"\n🚀 Début du téléchargement...")
        print(f"💡 Vous pouvez interrompre avec Ctrl+C et reprendre plus tard")
        print(f"📊 Progression affichée ci-dessous:")
        print("-" * 50)
        
        # Télécharger le modèle
        snapshot_download(
            repo_id=model_id,
            local_dir=str(local_dir),
            local_dir_use_symlinks=False,
            resume_download=True,
            token=token
        )
        
        print("\n" + "=" * 50)
        print("🎉 TÉLÉCHARGEMENT TERMINÉ!")
        print("=" * 50)
        print(f"✅ Modèle sauvegardé dans: {local_dir}")
        
        # Vérifier les fichiers téléchargés
        files = list(local_dir.glob("*"))
        print(f"📁 Fichiers téléchargés: {len(files)}")
        
        for file in files[:10]:  # Afficher les 10 premiers fichiers
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"  📄 {file.name} ({size_mb:.1f} MB)")
        
        if len(files) > 10:
            print(f"  ... et {len(files) - 10} autres fichiers")
        
        # Créer un fichier de configuration
        import json
        config = {
            "model_id": model_id,
            "local_path": str(local_dir),
            "download_complete": True,
            "files_count": len(files),
            "total_size_gb": sum(f.stat().st_size for f in files) / (1024**3)
        }
        
        with open("llama_download_info.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n✅ Configuration sauvegardée: llama_download_info.json")
        print(f"🚀 Prêt pour le fine-tuning!")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n⏸️ Téléchargement interrompu par l'utilisateur")
        print(f"💡 Relancez le script pour reprendre le téléchargement")
        return False
        
    except Exception as e:
        print(f"\n❌ Erreur de téléchargement: {e}")
        print(f"💡 Vérifiez votre connexion internet et réessayez")
        return False

def check_existing_model():
    """Vérifie si le modèle existe déjà"""
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    if local_dir.exists() and any(local_dir.iterdir()):
        files = list(local_dir.glob("*"))
        total_size = sum(f.stat().st_size for f in files) / (1024**3)
        
        print(f"📁 Modèle existant détecté:")
        print(f"  📍 Emplacement: {local_dir}")
        print(f"  📄 Fichiers: {len(files)}")
        print(f"  💾 Taille: {total_size:.1f} GB")
        
        if total_size > 10:  # Si plus de 10GB, probablement complet
            print(f"✅ Modèle semble complet")
            return True
        else:
            print(f"⚠️ Modèle incomplet, téléchargement recommandé")
            return False
    
    return False

def main():
    """Fonction principale"""
    print("🦙 Téléchargeur Llama 3.1 8B")
    print("=" * 40)
    
    # Vérifier si le modèle existe déjà
    if check_existing_model():
        choice = input("\nModèle existant trouvé. Re-télécharger? (y/n): ").lower()
        if choice != 'y':
            print("✅ Utilisation du modèle existant")
            return
    
    # Installer les dépendances
    install_requirements()
    
    # Confirmer le téléchargement
    print(f"\n⚠️ ATTENTION:")
    print(f"  📥 Téléchargement de ~16 GB")
    print(f"  ⏱️ Peut prendre 30-60 minutes")
    print(f"  💾 Nécessite ~20 GB d'espace libre")
    
    confirm = input(f"\nCommencer le téléchargement? (y/n): ").lower()
    
    if confirm == 'y':
        if download_llama():
            print(f"\n🎉 SUCCÈS!")
            print(f"🚀 Prochaines étapes:")
            print(f"  1. python prepare_banking_dataset.py")
            print(f"  2. python llama_finetuning_cuda.py")
        else:
            print(f"\n❌ Téléchargement échoué")
    else:
        print(f"Téléchargement annulé")

if __name__ == "__main__":
    main()
