"""
Fine-tuning Coda avec modèle local uniquement (sans Hugging Face)
Utilise le modèle Llama téléchargé via llama-stack
"""

import os
import json
import torch
from pathlib import Path
from datasets import Dataset
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodaLocalTrainer:
    """Trainer Coda utilisant uniquement des ressources locales"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.config = self.create_local_config()
    
    def create_local_config(self):
        """Configuration pour utilisation locale uniquement"""
        config = {
            "approach": "local_only",
            "model_source": "llama_stack_local",
            "training_method": "lora_from_scratch",
            "data_source": "wasifis_local",
            "output_dir": "./coda_local_model"
        }
        
        with open("coda_local_config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        return config
    
    def find_llama_stack_model(self):
        """Trouve le modèle Llama dans llama-stack"""
        logger.info("Recherche du modèle Llama local...")
        
        # Chemins possibles pour llama-stack
        possible_paths = [
            Path.home() / ".llama" / "checkpoints" / "Llama3.1-8B",
            Path.home() / ".cache" / "llama-stack" / "models" / "Llama3.1-8B",
            Path("./models/Llama3.1-8B"),
            Path("./llama_models/Llama3.1-8B")
        ]
        
        for path in possible_paths:
            if path.exists():
                logger.info(f"✅ Modèle trouvé: {path}")
                return str(path)
        
        logger.warning("❌ Modèle Llama local non trouvé")
        return None
    
    def use_alternative_approach(self):
        """Approche alternative sans téléchargement"""
        logger.info("=== Approche Alternative: Fine-tuning sans modèle externe ===")
        
        print("""
🎯 APPROCHES POSSIBLES SANS HUGGING FACE:

1. 📦 UTILISER OLLAMA DIRECTEMENT
   - Votre llama3.1:8b est déjà fonctionnel
   - Créer des prompts optimisés
   - Utiliser le RAG que nous avons créé
   
2. 🔧 FINE-TUNING AVEC OLLAMA MODELFILE
   - Créer un Modelfile personnalisé
   - Utiliser vos données wasifis/bank-assistant-qa
   - Commande: ollama create banking-assistant -f Modelfile
   
3. 🧠 ENTRAÎNEMENT DE PROMPTS (PROMPT ENGINEERING)
   - Optimiser les prompts système
   - Utiliser few-shot learning
   - Créer des templates spécialisés
   
4. 💾 FINE-TUNING LOCAL AVEC PYTORCH
   - Utiliser PyTorch directement
   - Créer un modèle simple depuis zéro
   - Entraîner sur vos données bancaires
        """)
        
        return self.choose_approach()
    
    def choose_approach(self):
        """Permet de choisir l'approche"""
        print("\nQuelle approche préférez-vous?")
        print("1. Ollama Modelfile (recommandé)")
        print("2. Prompt Engineering avancé")
        print("3. PyTorch from scratch")
        print("4. Voir toutes les options")
        
        choice = input("Choix (1-4): ").strip()
        
        if choice == "1":
            return self.create_ollama_modelfile()
        elif choice == "2":
            return self.create_prompt_engineering()
        elif choice == "3":
            return self.create_pytorch_training()
        elif choice == "4":
            return self.show_all_options()
        else:
            print("Choix invalide")
            return False
    
    def create_ollama_modelfile(self):
        """Crée un Modelfile Ollama personnalisé"""
        logger.info("Création d'un Modelfile Ollama personnalisé...")
        
        # Charger quelques exemples de vos données
        examples = self.load_sample_data()
        
        # Créer le Modelfile
        modelfile_content = f"""FROM llama3.1:8b

# Paramètres optimisés pour le banking
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1

# Prompt système spécialisé banking
SYSTEM \"\"\"Vous êtes un assistant bancaire expert de NUST Bank. 
Vous aidez les clients avec leurs questions bancaires de manière précise et professionnelle.

Votre expertise inclut:
- Comptes bancaires et frais
- Cartes de crédit et débit
- Prêts et crédits
- Services bancaires en ligne
- Procédures et réglementations

Répondez toujours de manière courtoise et professionnelle.\"\"\"

# Template de conversation
TEMPLATE \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{{{{ .System }}}}<|eot_id|><|start_header_id|>user<|end_header_id|>

{{{{ .Prompt }}}}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

\"\"\"

# Exemples d'entraînement intégrés
"""

        # Ajouter quelques exemples dans le Modelfile
        for i, example in enumerate(examples[:5]):
            modelfile_content += f'''
# Exemple {i+1}
TEMPLATE \"\"\"<|begin_of_text|><|start_header_id|>user<|end_header_id|>
{example['input']}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
{example['output']}<|eot_id|>\"\"\"
'''
        
        # Sauvegarder le Modelfile
        with open("Modelfile.banking", 'w', encoding='utf-8') as f:
            f.write(modelfile_content)
        
        # Créer le script de création
        script_content = """@echo off
echo 🏦 Création du modèle bancaire personnalisé...
echo.

ollama create banking-assistant -f Modelfile.banking

if %errorlevel% equ 0 (
    echo.
    echo ✅ Modèle 'banking-assistant' créé avec succès!
    echo.
    echo 🧪 Test du modèle:
    ollama run banking-assistant "Quels sont les frais de compte?"
) else (
    echo ❌ Erreur lors de la création du modèle
)
"""
        
        with open("create_banking_model.bat", 'w') as f:
            f.write(script_content)
        
        print("✅ Fichiers créés:")
        print("  - Modelfile.banking")
        print("  - create_banking_model.bat")
        print("\n🚀 Pour créer le modèle: create_banking_model.bat")
        
        return True
    
    def create_prompt_engineering(self):
        """Crée un système de prompt engineering avancé"""
        logger.info("Création du système de prompt engineering...")
        
        examples = self.load_sample_data()
        
        prompt_system = f"""
# Système de Prompt Engineering Bancaire

## Prompt Système Optimisé
```
Vous êtes un assistant bancaire expert de NUST Bank avec une connaissance approfondie des services bancaires.

EXPERTISE:
- Comptes: courants, épargne, joints
- Cartes: débit, crédit, prépayées  
- Prêts: personnels, immobiliers, auto
- Services: virements, prélèvements, assurances
- Réglementations bancaires

STYLE DE RÉPONSE:
- Professionnel et courtois
- Précis et factuel
- Structuré avec des points clés
- Inclure les frais quand pertinent

EXEMPLES DE RÉPONSES:
"""

        # Ajouter des exemples
        for example in examples[:3]:
            prompt_system += f"""
Q: {example['input']}
R: {example['output']}
---
"""
        
        prompt_system += """
```

## Templates de Prompts Spécialisés

### Pour les frais
"En tant qu'expert bancaire, expliquez les frais de [SERVICE] en détail, incluant les conditions et exceptions."

### Pour les procédures  
"Guidez le client étape par étape pour [PROCÉDURE], en mentionnant tous les documents requis."

### Pour les produits
"Présentez les caractéristiques principales de [PRODUIT BANCAIRE], ses avantages et conditions d'éligibilité."
"""
        
        with open("prompt_engineering_guide.md", 'w', encoding='utf-8') as f:
            f.write(prompt_system)
        
        # Créer un script de test
        test_script = '''"""
Test du système de prompt engineering
"""

import ollama

def test_optimized_prompts():
    client = ollama.Client()
    
    # Prompt système optimisé
    system_prompt = """Vous êtes un assistant bancaire expert de NUST Bank avec une connaissance approfondie des services bancaires.

EXPERTISE: Comptes, cartes, prêts, services, réglementations
STYLE: Professionnel, précis, structuré
FORMAT: Points clés avec frais quand pertinent"""
    
    test_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Conditions pour un prêt auto?"
    ]
    
    for question in test_questions:
        print(f"\\n❓ {question}")
        
        response = client.chat(
            model='llama3.1:8b',
            messages=[
                {'role': 'system', 'content': system_prompt},
                {'role': 'user', 'content': question}
            ]
        )
        
        print(f"🤖 {response['message']['content'][:200]}...")

if __name__ == "__main__":
    test_optimized_prompts()
'''
        
        with open("test_prompts.py", 'w') as f:
            f.write(test_script)
        
        print("✅ Fichiers créés:")
        print("  - prompt_engineering_guide.md")
        print("  - test_prompts.py")
        
        return True
    
    def load_sample_data(self):
        """Charge quelques exemples de données"""
        try:
            with open('processed_data/train.json', 'r', encoding='utf-8') as f:
                data = [json.loads(line) for line in f]
            return data[:10]  # Prendre 10 exemples
        except:
            # Données de fallback
            return [
                {
                    "input": "Quels sont les frais de tenue de compte?",
                    "output": "Les frais de tenue de compte varient selon le type de compte. Pour un compte courant standard, les frais sont de 5€ par mois."
                },
                {
                    "input": "Comment ouvrir un compte épargne?",
                    "output": "Pour ouvrir un compte épargne, vous devez fournir une pièce d'identité, un justificatif de domicile et effectuer un dépôt minimum de 10€."
                }
            ]
    
    def show_all_options(self):
        """Affiche toutes les options disponibles"""
        print("""
🎯 TOUTES LES OPTIONS SANS HUGGING FACE:

1. 🔧 OLLAMA MODELFILE (RECOMMANDÉ)
   ✅ Utilise votre llama3.1:8b existant
   ✅ Intègre vos données wasifis directement
   ✅ Création simple avec 'ollama create'
   
2. 🧠 PROMPT ENGINEERING AVANCÉ
   ✅ Optimise les prompts système
   ✅ Templates spécialisés banking
   ✅ Few-shot learning avec exemples
   
3. 💾 RAG AVEC OLLAMA (DÉJÀ CRÉÉ)
   ✅ Base vectorielle ChromaDB
   ✅ 4,272 exemples indexés
   ✅ Recherche sémantique + génération
   
4. 🔄 FINE-TUNING PYTORCH SIMPLE
   ✅ Modèle léger depuis zéro
   ✅ Entraînement sur vos données
   ✅ Export vers ONNX pour Ollama
   
5. 📝 SYSTÈME DE TEMPLATES
   ✅ Templates de réponses prédéfinis
   ✅ Logique de correspondance
   ✅ Fallback vers Ollama

RECOMMANDATION: Commencez par l'option 1 (Ollama Modelfile)
C'est le plus simple et efficace avec vos ressources actuelles.
        """)

def main():
    """Fonction principale"""
    print("🏦 Coda Local - Sans Hugging Face")
    print("=" * 40)
    
    trainer = CodaLocalTrainer()
    
    # Vérifier si un modèle local existe
    local_model = trainer.find_llama_stack_model()
    
    if local_model:
        print(f"✅ Modèle local trouvé: {local_model}")
        print("Vous pouvez utiliser ce modèle directement")
    else:
        print("⚠️ Modèle local non trouvé")
        print("Utilisation d'approches alternatives...")
    
    # Proposer les approches alternatives
    trainer.use_alternative_approach()

if __name__ == "__main__":
    main()
