"""
Test simple de l'accès Hugging Face pour Llama 3.1
"""

def test_huggingface_login():
    """Teste si vous êtes connecté à Hugging Face"""
    print("=== Test de connexion Hugging Face ===")
    
    try:
        from huggingface_hub import whoami
        user_info = whoami()
        print(f"✅ Connecté en tant que: {user_info['name']}")
        print(f"   Email: {user_info.get('email', 'Non disponible')}")
        return True
    except Exception as e:
        print(f"❌ Non connecté: {e}")
        print("\nPour vous connecter:")
        print("1. pip install huggingface_hub")
        print("2. huggingface-cli login")
        print("3. Coller votre token")
        return False

def test_llama_access():
    """Teste l'accès au modèle Llama 3.1"""
    print("\n=== Test d'accès à Llama 3.1 ===")
    
    try:
        from transformers import AutoConfig
        
        print("Test d'accès au modèle...")
        config = AutoConfig.from_pretrained("meta-llama/Meta-Llama-3.1-8B")
        
        print("✅ Accès au modèle Llama 3.1 confirmé!")
        print(f"   Vocabulaire: {config.vocab_size:,}")
        print(f"   Couches: {config.num_hidden_layers}")
        print(f"   Dimension: {config.hidden_size}")
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Pas d'accès au modèle: {error_msg}")
        
        if "401" in error_msg or "gated" in error_msg.lower():
            print("\n🔒 Le modèle est protégé. Vous devez:")
            print("1. Demander l'accès sur: https://huggingface.co/meta-llama/Meta-Llama-3.1-8B")
            print("2. Attendre l'approbation de Meta")
            print("3. Vous connecter avec: huggingface-cli login")
        elif "authentication" in error_msg.lower():
            print("\n🔑 Problème d'authentification:")
            print("1. Créer un token sur: https://huggingface.co/settings/tokens")
            print("2. Se connecter avec: huggingface-cli login")
        
        return False

def check_alternatives():
    """Vérifie les alternatives disponibles"""
    print("\n=== Alternatives disponibles ===")
    
    # Modèles libres similaires
    alternatives = [
        {
            "name": "microsoft/DialoGPT-large",
            "description": "Modèle de dialogue, libre d'accès",
            "size": "774M paramètres"
        },
        {
            "name": "microsoft/DialoGPT-medium", 
            "description": "Version moyenne, plus rapide",
            "size": "355M paramètres"
        },
        {
            "name": "gpt2-large",
            "description": "GPT-2 large, libre d'accès",
            "size": "774M paramètres"
        }
    ]
    
    print("Modèles libres que vous pouvez utiliser immédiatement:")
    for alt in alternatives:
        print(f"\n📦 {alt['name']}")
        print(f"   {alt['description']}")
        print(f"   Taille: {alt['size']}")
    
    print(f"\n💡 Vous pouvez commencer avec ces modèles pendant que")
    print(f"   vous attendez l'accès à Llama 3.1")

def create_alternative_config():
    """Crée une configuration pour utiliser un modèle alternatif"""
    print("\n=== Configuration alternative ===")
    
    config_content = '''"""
Configuration pour utiliser un modèle alternatif en attendant Llama 3.1
"""

# Modèle alternatif (libre d'accès)
ALTERNATIVE_MODEL = "microsoft/DialoGPT-medium"

# Configuration LoRA adaptée
LORA_CONFIG = {
    "r": 8,
    "lora_alpha": 16,
    "lora_dropout": 0.1,
    "target_modules": ["c_attn", "c_proj"]  # Pour DialoGPT
}

print("Utilisation du modèle alternatif:", ALTERNATIVE_MODEL)
'''
    
    with open("alternative_config.py", 'w') as f:
        f.write(config_content)
    
    print("✅ Configuration alternative créée: alternative_config.py")

def main():
    """Fonction principale"""
    print("🤗 Test d'accès Hugging Face pour Llama 3.1\n")
    
    # 1. Test de connexion HF
    hf_connected = test_huggingface_login()
    
    # 2. Test d'accès Llama
    if hf_connected:
        llama_access = test_llama_access()
        
        if llama_access:
            print("\n🎉 SUCCÈS! Vous avez accès à Llama 3.1")
            print("Vous pouvez maintenant lancer:")
            print("python llama_lora_training.py")
        else:
            print("\n⏳ Pas encore d'accès à Llama 3.1")
            check_alternatives()
            create_alternative_config()
    else:
        print("\n🔑 Vous devez d'abord vous connecter à Hugging Face")
        print("\nÉtapes à suivre:")
        print("1. Créer un compte sur https://huggingface.co")
        print("2. Demander l'accès à Llama 3.1")
        print("3. Créer un token et se connecter")
    
    print(f"\n{'='*50}")
    print("RÉSUMÉ DES ÉTAPES:")
    print("1. 🌐 Compte HF: https://huggingface.co")
    print("2. 🔒 Accès Llama: https://huggingface.co/meta-llama/Meta-Llama-3.1-8B")
    print("3. 🔑 Token: https://huggingface.co/settings/tokens")
    print("4. 💻 Login: huggingface-cli login")
    print("5. ✅ Test: python test_hf_access.py")

if __name__ == "__main__":
    main()
