"""
Utilisation du modèle Llama 3.1 fine-tuné avec Coda
"""

import torch
import json
import ollama
from transformers import LlamaTokenizer, LlamaForCausalLM, AutoTokenizer
from peft import PeftModel
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Llama31FinetunedChatbot:
    """Chatbot utilisant Llama 3.1 fine-tuné"""
    
    def __init__(self, model_path="./coda_llama31_model"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.ollama_client = ollama.Client()
        self.load_model()
    
    def load_model(self):
        """Charge le modèle Llama 3.1 fine-tuné"""
        try:
            logger.info("Chargement du modèle Llama 3.1 fine-tuné...")
            
            # Charger la configuration
            with open("coda_llama31_config.json", 'r') as f:
                config = json.load(f)
            
            base_model_path = config["model_config"]["model_path"]
            
            # Charger le tokenizer
            try:
                self.tokenizer = LlamaTokenizer.from_pretrained(base_model_path)
            except:
                self.tokenizer = AutoTokenizer.from_pretrained(base_model_path)
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            base_model = LlamaForCausalLM.from_pretrained(
                base_model_path,
                torch_dtype=torch.float16,
                device_map="auto" if torch.cuda.is_available() else None,
                load_in_8bit=True
            )
            
            # Charger les adapters LoRA
            self.model = PeftModel.from_pretrained(base_model, self.model_path)
            
            logger.info("✅ Modèle Llama 3.1 fine-tuné chargé avec succès")
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            logger.info("Le modèle fine-tuné n'est pas disponible")
            self.model = None
    
    def chat_with_finetuned(self, question):
        """Chat avec le modèle fine-tuné"""
        if self.model is None:
            return "Modèle fine-tuné non disponible"
        
        # Format de prompt Llama 3.1
        prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nVous êtes un assistant bancaire expert et professionnel.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(prompt):].strip()
        return response
    
    def chat_with_ollama_base(self, question):
        """Chat avec le modèle de base via Ollama"""
        try:
            response = self.ollama_client.chat(
                model='llama3.1:8b',
                messages=[
                    {'role': 'system', 'content': 'Vous êtes un assistant bancaire expert.'},
                    {'role': 'user', 'content': question}
                ]
            )
            return response['message']['content']
        except Exception as e:
            return f"Erreur Ollama: {e}"
    
    def compare_models(self, question):
        """Compare le modèle de base et le modèle fine-tuné"""
        print(f"\n❓ Question: {question}")
        print("=" * 60)
        
        # Modèle de base (Ollama)
        print("🔵 Modèle de base (Ollama llama3.1:8b):")
        base_response = self.chat_with_ollama_base(question)
        print(base_response[:300] + "..." if len(base_response) > 300 else base_response)
        
        print("\n" + "-" * 60)
        
        # Modèle fine-tuné
        print("🟢 Modèle fine-tuné (Coda + LoRA):")
        if self.model:
            finetuned_response = self.chat_with_finetuned(question)
            print(finetuned_response[:300] + "..." if len(finetuned_response) > 300 else finetuned_response)
        else:
            print("❌ Modèle fine-tuné non disponible")
        
        print("=" * 60)
    
    def interactive_comparison(self):
        """Chat interactif avec comparaison"""
        print("\n🦙 Comparaison Llama 3.1: Base vs Fine-tuné")
        print("=" * 50)
        print("💬 Posez vos questions bancaires (tapez 'quit' pour quitter)")
        print("🔍 Comparaison automatique entre modèle de base et fine-tuné")
        print("-" * 50)
        
        while True:
            user_input = input("\n👤 Vous: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                print("👋 Au revoir!")
                break
            
            if user_input:
                self.compare_models(user_input)
    
    def test_banking_scenarios(self):
        """Teste avec des scénarios bancaires spécifiques"""
        print("\n🧪 Test des scénarios bancaires")
        print("=" * 40)
        
        scenarios = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt immobilier?",
            "Comment activer ma carte bancaire?",
            "Que faire en cas de perte de carte?",
            "Quels sont les horaires d'ouverture de la banque?",
            "Comment faire un virement international?",
            "Quels documents pour ouvrir un compte?"
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 Scénario {i}:")
            self.compare_models(scenario)
            
            if i < len(scenarios):
                input("\nAppuyez sur Entrée pour continuer...")
    
    def benchmark_performance(self):
        """Benchmark de performance"""
        print("\n📊 Benchmark de performance")
        print("=" * 30)
        
        test_question = "Quels sont les frais de compte?"
        
        # Test Ollama
        import time
        start_time = time.time()
        ollama_response = self.chat_with_ollama_base(test_question)
        ollama_time = time.time() - start_time
        
        # Test fine-tuné
        if self.model:
            start_time = time.time()
            finetuned_response = self.chat_with_finetuned(test_question)
            finetuned_time = time.time() - start_time
        else:
            finetuned_time = 0
            finetuned_response = "Non disponible"
        
        print(f"⏱️ Temps de réponse:")
        print(f"  Ollama (base): {ollama_time:.2f}s")
        print(f"  Fine-tuné: {finetuned_time:.2f}s")
        
        print(f"\n📝 Longueur des réponses:")
        print(f"  Ollama: {len(ollama_response)} caractères")
        print(f"  Fine-tuné: {len(finetuned_response)} caractères")

def main():
    """Fonction principale"""
    print("🦙 Chatbot Llama 3.1 Fine-tuné avec Coda")
    print("=" * 50)
    
    chatbot = Llama31FinetunedChatbot()
    
    while True:
        print("\nOptions:")
        print("1. 💬 Comparaison interactive")
        print("2. 🧪 Test des scénarios bancaires")
        print("3. 📊 Benchmark de performance")
        print("4. ℹ️ Informations sur les modèles")
        print("5. 🚪 Quitter")
        
        choice = input("\nChoix (1-5): ").strip()
        
        if choice == "1":
            chatbot.interactive_comparison()
        elif choice == "2":
            chatbot.test_banking_scenarios()
        elif choice == "3":
            chatbot.benchmark_performance()
        elif choice == "4":
            print(f"""
🦙 Informations sur les modèles

Modèle de base (Ollama):
- Nom: llama3.1:8b
- Source: Ollama
- Statut: {'✅ Disponible' if chatbot.ollama_client else '❌ Non disponible'}

Modèle fine-tuné (Coda):
- Technique: LoRA (Low-Rank Adaptation)
- Base: Llama 3.1-8B local
- Données: wasifis/bank-assistant-qa
- Statut: {'✅ Disponible' if chatbot.model else '❌ Non disponible'}
- Chemin: {chatbot.model_path}

Configuration LoRA:
- Rank: 16
- Alpha: 32
- Target modules: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj
- Optimisations: 8-bit quantization, gradient checkpointing
            """)
        elif choice == "5":
            print("👋 Au revoir!")
            break
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
