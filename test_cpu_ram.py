"""
Test utilisant CPU + RAM (16GB) au lieu du GPU
Solution pour éviter les conflits GPU/CPU
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import time
import psutil
import os

print("🏦 TEST CPU + RAM - CHATBOT BANCAIRE")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Forcer l'utilisation CPU
os.environ["CUDA_VISIBLE_DEVICES"] = ""
torch.set_num_threads(8)  # Utiliser 8 threads CPU

print(f"💾 RAM totale: {psutil.virtual_memory().total / 1024**3:.1f} GB")
print(f"💾 RAM libre: {psutil.virtual_memory().available / 1024**3:.1f} GB")
print(f"🔧 Mode: CPU seulement (utilise votre RAM)")

def load_cpu_model():
    """Charge le modèle sur CPU avec votre RAM"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle sur CPU...")
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="cpu",  # Forcer CPU
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            torch_dtype=torch.float16,
            device_map="cpu",  # Forcer CPU
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            token="*************************************"
        )
    
    print(f"✅ Modèle de base chargé sur CPU")
    print(f"💾 RAM utilisée: {psutil.virtual_memory().used / 1024**3:.1f} GB")
    
    print("🔧 Chargement adaptateur LoRA...")
    try:
        finetuned_model = PeftModel.from_pretrained(
            base_model, 
            adapter_path,
            torch_dtype=torch.float16
        )
        print("✅ Modèle fine-tuné chargé avec succès!")
        return tokenizer, finetuned_model
    except Exception as e:
        print(f"⚠️ Erreur adaptateur principal: {e}")
        try:
            checkpoint_path = f"{adapter_path}/checkpoint-570"
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                checkpoint_path,
                torch_dtype=torch.float16
            )
            print("✅ Modèle fine-tuné chargé (checkpoint)!")
            return tokenizer, finetuned_model
        except Exception as e2:
            print(f"❌ Impossible de charger le fine-tuné: {e2}")
            print("💡 Utilisation du modèle de base seulement")
            return tokenizer, base_model

# Chargement du modèle
try:
    tokenizer, model = load_cpu_model()
    
    print(f"\n💾 RAM finale utilisée: {psutil.virtual_memory().used / 1024**3:.1f} GB")
    print(f"💾 RAM libre restante: {psutil.virtual_memory().available / 1024**3:.1f} GB")
    
    def generate_response(question, max_length=150):
        """Génère une réponse sur CPU"""
        
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.1,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        generation_time = time.time() - start_time
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        if "<|start_header_id|>assistant<|end_header_id|>" in response:
            response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        
        return response, generation_time

    # Tests bancaires
    print("\n🧪 TESTS BANCAIRES (CPU + RAM):")
    
    test_questions = [
        "What are the charges for account maintenance?",
        "How to open a savings account?", 
        "What is the minimum balance required?"
    ]
    
    for i, question in enumerate(test_questions):
        print(f"\n📋 Test {i+1}/{len(test_questions)}: {question}")
        
        response, response_time = generate_response(question)
        
        banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                           if word in response.lower())
        
        print(f"⏱️ Temps: {response_time:.2f}s (CPU)")
        print(f"🎯 Termes bancaires: {banking_words}")
        print(f"💬 Réponse: {response[:150]}...")
        print(f"💾 RAM: {psutil.virtual_memory().used / 1024**3:.1f} GB utilisés")

    print(f"\n✅ SUCCÈS! Modèle fonctionne avec votre RAM!")
    print(f"💾 Votre RAM de 16GB est bien utilisée")
    print(f"🔧 Pas de conflit GPU/CPU")
    
    # Test interactif
    print(f"\n💬 Test interactif? (y/n)")
    choice = input().strip().lower()
    if choice == 'y':
        print("Posez vos questions bancaires (tapez 'quit' pour arrêter)")
        while True:
            user_question = input("\n❓ Question: ").strip()
            if user_question.lower() in ['quit', 'q', 'exit']:
                break
            if user_question:
                print("🤖 Génération...")
                response, time_taken = generate_response(user_question)
                banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                                   if word in response.lower())
                print(f"⏱️ {time_taken:.2f}s | 🎯 {banking_words} termes | 💬 {response}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print(f"\n💡 Votre RAM de 16GB est détectée mais:")
    print(f"1. Le modèle est peut-être trop gros même pour 16GB")
    print(f"2. Essayez de fermer d'autres applications")
    print(f"3. Redémarrez Windows pour libérer la RAM")

print(f"\n🎯 TEST TERMINÉ!")
print(f"💾 Votre RAM de 16GB est bien reconnue et utilisable")
