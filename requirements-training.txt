# Training Spécifique - Fine-tuning LoRA
# Dépendances pour entraînement et fine-tuning

# Core ML Framework
torch==2.1.0+cu118
transformers==4.35.0

# Fine-tuning & Optimization
peft==0.6.0
accelerate==0.24.0
bitsandbytes==0.41.0

# Dataset Management
datasets==2.14.1
huggingface-hub==0.17.3

# Training Utilities
trl==0.7.4
evaluate==0.4.1

# Monitoring & Logging
wandb==0.15.12
tensorboard==2.14.1

# Data Processing
numpy==1.24.3
pandas==2.0.3

# Utilities
tqdm==4.66.1
safetensors==0.4.0
