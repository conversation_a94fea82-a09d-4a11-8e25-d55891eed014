"""
Configuration et utilitaires pour le fine-tuning LoRA
"""

import torch
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer, 
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from config import model_config, training_config
import os

class LoRATrainingSetup:
    """Classe pour configurer et gérer le fine-tuning LoRA"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.lora_config = None
        
    def create_lora_config(self):
        """Crée la configuration LoRA"""
        print("Création de la configuration LoRA...")
        
        self.lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,  # Pour le language modeling
            inference_mode=False,  # Mode entraînement
            r=model_config.lora_r,  # Rank
            lora_alpha=model_config.lora_alpha,  # Alpha
            lora_dropout=model_config.lora_dropout,  # Dropout
            target_modules=model_config.lora_target_modules,  # Modules cibles
            bias="none",  # Pas de bias LoRA
            use_rslora=False,  # RSLoRA désactivé pour la compatibilité
        )
        
        print(f"Configuration LoRA créée:")
        print(f"  - Rank (r): {self.lora_config.r}")
        print(f"  - Alpha: {self.lora_config.lora_alpha}")
        print(f"  - Dropout: {self.lora_config.lora_dropout}")
        print(f"  - Target modules: {self.lora_config.target_modules}")
        
        return self.lora_config
    
    def load_base_model(self, model_name="microsoft/DialoGPT-medium"):
        """Charge le modèle de base (utilise un modèle plus petit pour les tests)"""
        print(f"Chargement du modèle de base: {model_name}")

        try:
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)

            # Ajouter un token de padding si nécessaire
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Charger le modèle
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )

            # Adapter les modules cibles selon l'architecture du modèle
            self._adapt_target_modules_for_model()

            print(f"Modèle chargé avec succès!")
            print(f"  - Paramètres: {self.model.num_parameters():,}")
            print(f"  - Device: {next(self.model.parameters()).device}")

            return self.model, self.tokenizer

        except Exception as e:
            print(f"Erreur lors du chargement du modèle: {e}")
            print("Utilisation d'un modèle de test plus petit...")

            # Fallback vers un modèle plus petit
            model_name = "gpt2"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            self._adapt_target_modules_for_model()

            print(f"Modèle de fallback chargé: {model_name}")
            return self.model, self.tokenizer

    def _adapt_target_modules_for_model(self):
        """Adapte les modules cibles selon l'architecture du modèle"""
        if self.model is None:
            return

        # Obtenir tous les noms de modules
        module_names = []
        for name, module in self.model.named_modules():
            if hasattr(module, 'weight') and len(module.weight.shape) == 2:
                module_names.append(name.split('.')[-1])

        # Modules communs pour différentes architectures
        common_targets = {
            'gpt2': ['c_attn', 'c_proj', 'c_fc'],
            'dialogpt': ['c_attn', 'c_proj', 'c_fc'],
            'llama': ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj'],
            'mistral': ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
        }

        # Détecter l'architecture
        model_type = self.model.config.model_type.lower()

        if model_type in common_targets:
            target_modules = common_targets[model_type]
        else:
            # Fallback: utiliser les modules linéaires trouvés
            target_modules = list(set(module_names))[:4]  # Prendre les 4 premiers

        # Mettre à jour la configuration
        if hasattr(model_config, 'lora_target_modules'):
            model_config.lora_target_modules = target_modules

        print(f"Modules cibles adaptés pour {model_type}: {target_modules}")

        # Recréer la config LoRA si elle existe déjà
        if self.lora_config is not None:
            self.lora_config.target_modules = target_modules
    
    def apply_lora(self):
        """Applique LoRA au modèle"""
        print("Application de LoRA au modèle...")
        
        if self.model is None:
            raise ValueError("Le modèle de base doit être chargé avant d'appliquer LoRA")
        
        if self.lora_config is None:
            self.create_lora_config()
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, self.lora_config)
        
        # Afficher les statistiques
        self.peft_model.print_trainable_parameters()
        
        print("LoRA appliqué avec succès!")
        return self.peft_model
    
    def create_training_arguments(self):
        """Crée les arguments d'entraînement"""
        print("Création des arguments d'entraînement...")
        
        training_args = TrainingArguments(
            output_dir=training_config.output_dir,
            num_train_epochs=training_config.num_train_epochs,
            per_device_train_batch_size=training_config.per_device_train_batch_size,
            per_device_eval_batch_size=training_config.per_device_eval_batch_size,
            gradient_accumulation_steps=training_config.gradient_accumulation_steps,
            learning_rate=training_config.learning_rate,
            weight_decay=training_config.weight_decay,
            warmup_steps=training_config.warmup_steps,
            logging_steps=training_config.logging_steps,
            eval_steps=training_config.eval_steps,
            save_steps=training_config.save_steps,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,  # Désactiver wandb pour l'instant
            remove_unused_columns=False,
            dataloader_pin_memory=False,
        )
        
        print("Arguments d'entraînement créés:")
        print(f"  - Epochs: {training_args.num_train_epochs}")
        print(f"  - Batch size: {training_args.per_device_train_batch_size}")
        print(f"  - Learning rate: {training_args.learning_rate}")
        print(f"  - Output dir: {training_args.output_dir}")
        
        return training_args
    
    def create_data_collator(self):
        """Crée le data collator pour l'entraînement"""
        if self.tokenizer is None:
            raise ValueError("Le tokenizer doit être chargé avant de créer le data collator")
        
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,  # Pas de masked language modeling
        )
        
        return data_collator
    
    def save_lora_config(self, output_path="./lora_config.json"):
        """Sauvegarde la configuration LoRA"""
        if self.lora_config is None:
            print("Aucune configuration LoRA à sauvegarder")
            return
        
        import json
        
        config_dict = {
            "task_type": str(self.lora_config.task_type),
            "r": self.lora_config.r,
            "lora_alpha": self.lora_config.lora_alpha,
            "lora_dropout": self.lora_config.lora_dropout,
            "target_modules": self.lora_config.target_modules,
            "bias": self.lora_config.bias,
        }
        
        with open(output_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        print(f"Configuration LoRA sauvegardée: {output_path}")

def test_lora_setup():
    """Teste la configuration LoRA"""
    print("=== Test de la configuration LoRA ===\n")
    
    # Créer l'instance de setup
    setup = LoRATrainingSetup()
    
    # 1. Créer la configuration LoRA
    lora_config = setup.create_lora_config()
    
    # 2. Charger le modèle de base
    model, tokenizer = setup.load_base_model()
    
    # 3. Appliquer LoRA
    peft_model = setup.apply_lora()
    
    # 4. Créer les arguments d'entraînement
    training_args = setup.create_training_arguments()
    
    # 5. Créer le data collator
    data_collator = setup.create_data_collator()
    
    # 6. Sauvegarder la configuration
    setup.save_lora_config()
    
    print("\n=== Test terminé avec succès ===")
    print("Configuration LoRA prête pour l'entraînement!")
    
    return setup

def create_ollama_integration_script():
    """Crée un script pour intégrer avec Ollama"""
    script_content = '''"""
Script pour intégrer le modèle fine-tuné avec Ollama
"""

import ollama
import json
from config import model_config, chatbot_config

class OllamaIntegration:
    def __init__(self):
        self.client = ollama.Client(host=model_config.ollama_host)
        
    def test_base_model(self):
        """Teste le modèle de base Ollama"""
        try:
            response = self.client.chat(
                model=model_config.base_model_name,
                messages=[
                    {
                        'role': 'system',
                        'content': chatbot_config.system_prompt
                    },
                    {
                        'role': 'user',
                        'content': 'Bonjour, pouvez-vous m\\'aider avec mes questions bancaires?'
                    }
                ]
            )
            print("Réponse du modèle de base:")
            print(response['message']['content'])
            return True
        except Exception as e:
            print(f"Erreur avec Ollama: {e}")
            return False
    
    def load_fine_tuned_model(self, adapter_path):
        """Charge le modèle fine-tuné (à implémenter)"""
        # Cette fonction sera implémentée après l'entraînement
        pass

if __name__ == "__main__":
    integration = OllamaIntegration()
    integration.test_base_model()
'''
    
    with open("ollama_integration.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("Script d'intégration Ollama créé: ollama_integration.py")

if __name__ == "__main__":
    # Tester la configuration
    setup = test_lora_setup()
    
    # Créer le script d'intégration Ollama
    create_ollama_integration_script()
