#!/usr/bin/env python3
"""
Test du modèle Ollama fine-tuné
"""

import ollama
import json

def test_banking_model():
    print("🧪 Test du modèle banking-assistant")
    print("=" * 40)
    
    client = ollama.Client()
    
    # Questions de test
    test_questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Quelles sont les conditions pour un prêt immobilier?",
        "Comment activer ma carte bancaire?",
        "Que faire en cas de perte de carte?",
        "Quels sont les horaires d'ouverture?",
        "Comment faire un virement international?",
        "Quels documents pour ouvrir un compte?"
    ]
    
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Question: {question}")
        
        try:
            response = client.chat(
                model='banking-assistant',
                messages=[
                    {'role': 'user', 'content': question}
                ]
            )
            
            answer = response['message']['content']
            print(f"   Réponse: {answer[:150]}...")
            
            results.append({
                "question": question,
                "answer": answer,
                "status": "success"
            })
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            results.append({
                "question": question,
                "error": str(e),
                "status": "error"
            })
    
    # Sauvegarder les résultats
    with open("test_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Test terminé. Résultats sauvegardés dans test_results.json")
    
    # Statistiques
    success_count = sum(1 for r in results if r['status'] == 'success')
    print(f"📊 Réussites: {success_count}/{len(test_questions)}")

if __name__ == "__main__":
    test_banking_model()
