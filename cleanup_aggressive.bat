@echo off
echo 🧹 NETTOYAGE AGRESSIF - Suppression de tous les fichiers inutiles...
echo.

REM ===== SUPPRESSION DES GROS DOSSIERS =====
echo 🗂️ Suppression des gros dossiers...

if exist "models" (
    echo Suppression du dossier models/ (~15GB)...
    rmdir /s /q "models"
)

if exist "llama_banking_final_fidelity" (
    echo Suppression du dossier llama_banking_final_fidelity/ (~5GB)...
    rmdir /s /q "llama_banking_final_fidelity"
)

if exist "offload_temp" (
    echo Suppression du dossier offload_temp/ (~10GB)...
    rmdir /s /q "offload_temp"
)

if exist "chroma_db_wasifis" (
    echo Suppression du dossier chroma_db_wasifis/ (~1GB)...
    rmdir /s /q "chroma_db_wasifis"
)

if exist "formatted_data" (
    echo Suppression du dossier formatted_data/...
    rmdir /s /q "formatted_data"
)

if exist "processed_data" (
    echo Suppression du dossier processed_data/...
    rmdir /s /q "processed_data"
)

if exist "processed_banking_dataset_no_instruction" (
    echo Suppression du dossier processed_banking_dataset_no_instruction/...
    rmdir /s /q "processed_banking_dataset_no_instruction"
)

if exist "banking_lora_results" (
    echo Suppression du dossier banking_lora_results/...
    rmdir /s /q "banking_lora_results"
)

if exist "git" (
    echo Suppression du dossier git/...
    rmdir /s /q "git"
)

REM ===== SUPPRESSION DES FICHIERS DE TEST =====
echo 🧪 Suppression des fichiers de test...

del /q test_*.py 2>nul
del /q check_*.py 2>nul
del /q quick_*.py 2>nul
del /q verify_*.py 2>nul
del /q compare_*.py 2>nul

REM ===== SUPPRESSION DES FICHIERS DE TRAINING =====
echo 🎯 Suppression des fichiers de training/fine-tuning...

del /q *training*.py 2>nul
del /q *finetuning*.py 2>nul
del /q *lora*.py 2>nul
del /q *fidelity*.py 2>nul
del /q *coda*.py 2>nul
del /q download_*.py 2>nul
del /q setup_*.py 2>nul
del /q prepare_*.py 2>nul
del /q resume_*.py 2>nul
del /q continue_*.py 2>nul
del /q improve_*.py 2>nul
del /q start_*.py 2>nul
del /q deploy_*.py 2>nul
del /q use_*.py 2>nul

REM ===== SUPPRESSION DES FICHIERS DE CONFIGURATION MULTIPLES =====
echo ⚙️ Suppression des configs multiples...

del /q *config*.json 2>nul
del /q *config*.py 2>nul
del /q requirements-*.txt 2>nul
del /q requirements_*.txt 2>nul
del /q package-*.json 2>nul

REM ===== SUPPRESSION DES SCRIPTS BATCH/SHELL =====
echo 📜 Suppression des scripts batch/shell...

del /q *.bat 2>nul
del /q *.sh 2>nul

REM ===== SUPPRESSION DES FICHIERS DE RÉSULTATS =====
echo 📊 Suppression des fichiers de résultats...

del /q *results*.json 2>nul
del /q comparison_*.json 2>nul
del /q fidelity_*.json 2>nul
del /q sample_*.json 2>nul

REM ===== SUPPRESSION DES FICHIERS DE DOCUMENTATION MULTIPLES =====
echo 📚 Suppression des docs multiples...

del /q AMELIORATIONS_*.md 2>nul
del /q GUIDE_*.md 2>nul
del /q documentation-*.md 2>nul
del /q dependencies-*.md 2>nul
del /q rapport.md 2>nul

REM ===== SUPPRESSION DES FICHIERS DIVERS =====
echo 🗑️ Suppression des fichiers divers...

del /q Modelfile* 2>nul
del /q *.jsonl 2>nul
del /q cleaned_*.json 2>nul

REM ===== SUPPRESSION DES ARCHIVES =====
echo 📦 Suppression des archives...

del /q *.zip 2>nul

REM ===== NETTOYAGE STANDARD =====
echo 🧽 Nettoyage standard...

if exist "chat-bank-nexus-main(frontend v0)\node_modules" (
    rmdir /s /q "chat-bank-nexus-main(frontend v0)\node_modules"
)

for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul
del /s /q *.log 2>nul
del /s /q conversation_*.json 2>nul

echo.
echo 🎉 NETTOYAGE AGRESSIF TERMINÉ !
echo.
echo ✅ FICHIERS CONSERVÉS (essentiels uniquement) :
echo   - backend_rag_api.py
echo   - backend_chat_history.py  
echo   - banking_chatbot_final.py
echo   - rag_chat_simple.py
echo   - chatbot_rag_complete.py
echo   - banking_knowledge_base.py
echo   - requirements.txt
echo   - chat-bank-nexus-main(frontend v0)/src/
echo   - banking_documents/
echo   - README.md
echo.
echo 📊 Réduction estimée : 32GB → 20-50MB (99%+ de réduction)
echo 🚀 Prêt pour Swiss Transfer !
pause
