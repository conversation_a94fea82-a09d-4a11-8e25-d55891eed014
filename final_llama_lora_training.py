"""
Fine-tuning LoRA final avec Llama 3.1-8B via Llama Stack
"""

import os
import torch
import json
from datasets import load_from_disk
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import ollama

class FinalLlamaLoRATrainer:
    """Trainer final pour Llama 3.1 avec LoRA"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.ollama_client = ollama.Client()
        
        # Charger la configuration
        with open("llama_stack_config.json", 'r') as f:
            self.config = json.load(f)
    
    def test_ollama_baseline(self):
        """Teste le modèle de base via Ollama"""
        print("=== Test du modèle de base (Ollama) ===")
        
        try:
            response = self.ollama_client.chat(
                model='llama3.1:8b',
                messages=[
                    {
                        'role': 'system',
                        'content': 'Vous êtes un assistant bancaire professionnel.'
                    },
                    {
                        'role': 'user',
                        'content': 'Quels sont les frais de tenue de compte?'
                    }
                ]
            )
            
            print("Réponse du modèle de base:")
            print(response['message']['content'])
            print("-" * 50)
            return True
            
        except Exception as e:
            print(f"❌ Erreur Ollama: {e}")
            return False
    
    def load_llama_model(self):
        """Charge le modèle Llama 3.1 depuis Llama Stack"""
        print("\n=== Chargement de Llama 3.1-8B ===")
        
        try:
            # Utiliser le modèle depuis Llama Stack
            model_path = "meta-llama/Llama-3.1-8B"
            
            print("Chargement du tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            print("✅ Tokenizer chargé")
            
            print("Chargement du modèle (cela peut prendre du temps)...")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            print(f"✅ Modèle chargé: {self.model.num_parameters():,} paramètres")
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            print("Le modèle sera chargé depuis le cache de Llama Stack...")
            return False
    
    def apply_lora(self):
        """Applique LoRA au modèle"""
        print("\n=== Application LoRA ===")
        
        if self.model is None:
            print("❌ Modèle non chargé")
            return False
        
        # Configuration LoRA
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            **self.config["lora_config"]
        )
        
        print(f"Configuration LoRA:")
        print(f"  - Rank: {lora_config.r}")
        print(f"  - Alpha: {lora_config.lora_alpha}")
        print(f"  - Target modules: {lora_config.target_modules}")
        
        try:
            self.peft_model = get_peft_model(self.model, lora_config)
            self.peft_model.print_trainable_parameters()
            print("✅ LoRA appliqué avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur LoRA: {e}")
            return False
    
    def prepare_data(self):
        """Prépare les données d'entraînement"""
        print("\n=== Préparation des données ===")
        
        try:
            # Charger les données formatées
            dataset = load_from_disk("./formatted_data")
            print(f"Dataset chargé: {len(dataset['train'])} exemples")
            
            # Tokeniser
            def tokenize_function(examples):
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,
                    max_length=self.config["training"]["max_length"],
                    return_tensors=None
                )
                tokenized["labels"] = tokenized["input_ids"].copy()
                return tokenized
            
            print("Tokenisation...")
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=dataset["train"].column_names,
                desc="Tokenisation"
            )
            
            print("✅ Données préparées")
            return tokenized_dataset
            
        except Exception as e:
            print(f"❌ Erreur de préparation: {e}")
            return None
    
    def train_model(self, dataset):
        """Lance l'entraînement LoRA"""
        print("\n=== Entraînement LoRA ===")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(
            output_dir="./final_llama_lora_results",
            num_train_epochs=self.config["training"]["num_epochs"],
            per_device_train_batch_size=self.config["training"]["batch_size"],
            per_device_eval_batch_size=self.config["training"]["batch_size"],
            gradient_accumulation_steps=self.config["training"]["gradient_accumulation_steps"],
            learning_rate=self.config["training"]["learning_rate"],
            weight_decay=0.01,
            warmup_steps=100,
            logging_steps=10,
            eval_steps=100,
            save_steps=100,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,
            remove_unused_columns=False,
            fp16=torch.cuda.is_available(),
            dataloader_pin_memory=False,
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["validation"],
            data_collator=data_collator,
        )
        
        print("Démarrage de l'entraînement...")
        try:
            trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained("./final_llama_lora_results")
            
            print("✅ Entraînement terminé et modèle sauvegardé")
            return True
            
        except Exception as e:
            print(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_fine_tuned_model(self):
        """Teste le modèle fine-tuné"""
        print("\n=== Test du modèle fine-tuné ===")
        
        if self.peft_model is None:
            print("❌ Modèle fine-tuné non disponible")
            return
        
        test_questions = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt immobilier?",
            "Comment activer ma carte bancaire?",
            "Quels documents fournir pour ouvrir un compte?"
        ]
        
        print("Questions de test:")
        for i, question in enumerate(test_questions, 1):
            prompt = f"Question: {question}\nRéponse:"
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 150,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\n{i}. Q: {question}")
            print(f"   R: {response}")
    
    def compare_models(self):
        """Compare le modèle de base et le modèle fine-tuné"""
        print("\n=== Comparaison des modèles ===")
        
        test_question = "Quels sont les frais de tenue de compte?"
        
        # Test avec Ollama (modèle de base)
        print("🔵 Modèle de base (Ollama):")
        try:
            response = self.ollama_client.chat(
                model='llama3.1:8b',
                messages=[
                    {'role': 'system', 'content': 'Vous êtes un assistant bancaire.'},
                    {'role': 'user', 'content': test_question}
                ]
            )
            print(response['message']['content'][:200] + "...")
        except:
            print("Erreur avec Ollama")
        
        # Test avec le modèle fine-tuné
        print("\n🟢 Modèle fine-tuné (LoRA):")
        if self.peft_model:
            prompt = f"Question: {test_question}\nRéponse:"
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            print(response[:200] + "...")
        else:
            print("Modèle fine-tuné non disponible")

def main():
    """Fonction principale"""
    print("=== Fine-tuning LoRA Final - Llama 3.1-8B ===\n")
    
    trainer = FinalLlamaLoRATrainer()
    
    # 1. Test du modèle de base
    print("1. Test du modèle de base via Ollama...")
    trainer.test_ollama_baseline()
    
    # 2. Charger le modèle Llama
    print("\n2. Chargement du modèle Llama 3.1...")
    if not trainer.load_llama_model():
        print("❌ Impossible de charger le modèle. Vérifiez Llama Stack.")
        return
    
    # 3. Appliquer LoRA
    print("\n3. Application de LoRA...")
    if not trainer.apply_lora():
        return
    
    # 4. Préparer les données
    print("\n4. Préparation des données...")
    dataset = trainer.prepare_data()
    if dataset is None:
        return
    
    # 5. Entraînement
    print("\n5. Voulez-vous lancer l'entraînement LoRA? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        print("Lancement de l'entraînement...")
        if trainer.train_model(dataset):
            print("\n✅ Fine-tuning LoRA terminé avec succès!")
            
            # 6. Tests
            trainer.test_fine_tuned_model()
            trainer.compare_models()
        else:
            print("\n❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé.")
        print("Configuration LoRA prête. Vous pouvez relancer plus tard.")
    
    print("\n" + "="*60)
    print("RÉSUMÉ:")
    print("✅ Llama Stack configuré")
    print("✅ Modèle Llama 3.1-8B accessible")
    print("✅ Configuration LoRA prête")
    print("✅ Données bancaires préparées (4272 exemples)")
    print("🎯 Prêt pour le fine-tuning LoRA!")

if __name__ == "__main__":
    main()
