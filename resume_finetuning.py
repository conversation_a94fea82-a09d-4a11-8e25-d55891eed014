"""
Reprendre le fine-tuning depuis le checkpoint-100
"""

import os
import torch
from pathlib import Path
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import PeftModel, LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk

def resume_training():
    print("🔄 Reprise du fine-tuning depuis checkpoint-100")
    print("=" * 50)
    
    # Vérifier le checkpoint
    checkpoint_path = "./llama_banking_finetuned/checkpoint-100"
    if not Path(checkpoint_path).exists():
        print("❌ Checkpoint non trouvé")
        return False
    
    print(f"✅ Checkpoint trouvé: {checkpoint_path}")
    
    try:
        # Configuration 4-bit
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        # Charger le modèle de base
        print("🦙 Chargement du modèle de base...")
        model_path = "./models/Llama-3.1-8B-Instruct"
        
        if Path(model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
            tokenizer = AutoTokenizer.from_pretrained(model_path)
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                token="*************************************"
            )
            tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                token="*************************************"
            )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            tokenizer.pad_token_id = tokenizer.eos_token_id
        
        # Préparer pour l'entraînement
        base_model = prepare_model_for_kbit_training(base_model)
        
        # Charger les adapters LoRA depuis le checkpoint
        print("🔧 Chargement des adapters LoRA...")
        model = PeftModel.from_pretrained(base_model, checkpoint_path)
        
        # Dataset
        print("📊 Chargement du dataset...")
        dataset = load_from_disk("./processed_banking_dataset_no_instruction")
        
        # Configuration d'entraînement (reprise)
        training_args = TrainingArguments(
            output_dir="./llama_banking_finetuned",
            num_train_epochs=2,
            per_device_train_batch_size=1,
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=16,
            learning_rate=1e-4,
            weight_decay=0.01,
            warmup_ratio=0.1,
            lr_scheduler_type="cosine",
            max_grad_norm=1.0,
            optim="paged_adamw_8bit",
            fp16=True,
            gradient_checkpointing=True,
            dataloader_pin_memory=True,
            dataloader_num_workers=2,
            logging_steps=5,
            eval_steps=50,
            save_steps=100,
            save_total_limit=3,
            max_steps=500,  # Continuer jusqu'à 500
            resume_from_checkpoint=checkpoint_path,  # REPRISE
            report_to=[],
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset.get("test", dataset.get("validation")),
            data_collator=data_collator,
            tokenizer=tokenizer,
        )
        
        print(f"\n🚀 Reprise de l'entraînement depuis step 100...")
        print(f"🎯 Objectif: 500 steps (400 steps restants)")
        
        # Reprendre l'entraînement
        trainer.train(resume_from_checkpoint=checkpoint_path)
        
        # Sauvegarder le modèle final
        trainer.save_model()
        tokenizer.save_pretrained("./llama_banking_finetuned")
        
        print(f"\n✅ Fine-tuning terminé!")
        print(f"📁 Modèle final: ./llama_banking_finetuned")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🔄 Reprise du Fine-tuning Llama 3.1 8B")
    print("=" * 40)
    
    if not torch.cuda.is_available():
        print("❌ CUDA requis")
        return
    
    confirm = input("Reprendre le fine-tuning depuis checkpoint-100? (y/n): ").lower()
    if confirm != 'y':
        print("Reprise annulée")
        return
    
    if resume_training():
        print("\n🎉 Fine-tuning complet terminé!")
        print("🚀 Testez avec: python test_finetuned_llama.py")
    else:
        print("\n❌ Échec de la reprise")

if __name__ == "__main__":
    main()
