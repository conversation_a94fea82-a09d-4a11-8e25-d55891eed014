"""
Configuration et utilisation de Llama Stack pour le fine-tuning LoRA
"""

import subprocess
import os
import json
from pathlib import Path

class LlamaStackSetup:
    """Classe pour configurer Llama Stack"""
    
    def __init__(self):
        self.llama_stack_installed = False
        self.models_available = []
    
    def install_llama_stack(self):
        """Installe ou met à jour Llama Stack"""
        print("=== Installation/Mise à jour de Llama Stack ===")
        
        try:
            # Installer/mettre à jour llama-stack
            print("Installation de llama-stack...")
            result = subprocess.run([
                "pip", "install", "llama-stack", "-U"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Llama Stack installé/mis à jour avec succès")
                self.llama_stack_installed = True
                return True
            else:
                print(f"❌ Erreur d'installation: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def list_available_models(self):
        """Liste les modèles disponibles"""
        print("\n=== Modèles Llama disponibles ===")
        
        try:
            # Lister tous les modèles
            result = subprocess.run([
                "llama", "model", "list", "--show-all"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Modèles disponibles:")
                print(result.stdout)
                return True
            else:
                print(f"❌ Erreur: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def download_llama_model(self, model_id="Meta-Llama-3.1-8B"):
        """Télécharge un modèle Llama spécifique"""
        print(f"\n=== Téléchargement de {model_id} ===")
        
        try:
            # Télécharger le modèle depuis Meta
            print(f"Téléchargement de {model_id} depuis Meta...")
            print("Cela peut prendre du temps selon votre connexion...")
            
            result = subprocess.run([
                "llama", "model", "download", 
                "--source", "meta", 
                "--model-id", model_id
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {model_id} téléchargé avec succès")
                print(result.stdout)
                return True
            else:
                print(f"❌ Erreur de téléchargement: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def check_local_models(self):
        """Vérifie les modèles installés localement"""
        print("\n=== Modèles installés localement ===")
        
        try:
            result = subprocess.run([
                "llama", "model", "list"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Modèles locaux:")
                print(result.stdout)
                return True
            else:
                print(f"❌ Erreur: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def create_llama_stack_config(self):
        """Crée une configuration pour Llama Stack"""
        print("\n=== Configuration Llama Stack ===")
        
        config = {
            "llama_stack": {
                "model_id": "Meta-Llama-3.1-8B",
                "source": "meta",
                "local_path": None  # Sera rempli après téléchargement
            },
            "lora_config": {
                "r": 16,
                "lora_alpha": 32,
                "lora_dropout": 0.1,
                "target_modules": [
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ]
            },
            "training": {
                "batch_size": 1,
                "gradient_accumulation_steps": 8,
                "learning_rate": 2e-4,
                "num_epochs": 3,
                "max_length": 512
            }
        }
        
        with open("llama_stack_config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuration sauvegardée: llama_stack_config.json")
        return config

def create_llama_stack_training_script():
    """Crée un script de fine-tuning avec Llama Stack"""
    script_content = '''"""
Fine-tuning LoRA avec Llama Stack
"""

import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from datasets import load_from_disk

def load_llama_stack_model():
    """Charge le modèle depuis Llama Stack"""
    print("=== Chargement du modèle Llama Stack ===")
    
    # Charger la configuration
    with open("llama_stack_config.json", 'r') as f:
        config = json.load(f)
    
    model_id = config["llama_stack"]["model_id"]
    
    try:
        # Le modèle devrait être disponible localement après téléchargement
        # Adapter le chemin selon l'installation de Llama Stack
        
        print(f"Chargement de {model_id}...")
        
        # Essayer de charger depuis le cache local de Llama Stack
        tokenizer = AutoTokenizer.from_pretrained(
            f"meta-llama/{model_id}",
            trust_remote_code=True
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            f"meta-llama/{model_id}",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            trust_remote_code=True
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"✅ Modèle chargé: {model.num_parameters():,} paramètres")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Erreur de chargement: {e}")
        print("Vérifiez que le modèle a été téléchargé avec Llama Stack")
        return None, None

def apply_lora_to_llama_stack():
    """Applique LoRA au modèle Llama Stack"""
    print("\\n=== Application LoRA ===")
    
    # Charger le modèle
    model, tokenizer = load_llama_stack_model()
    if model is None:
        return None, None
    
    # Charger la config LoRA
    with open("llama_stack_config.json", 'r') as f:
        config = json.load(f)
    
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        **config["lora_config"]
    )
    
    # Appliquer LoRA
    peft_model = get_peft_model(model, lora_config)
    peft_model.print_trainable_parameters()
    
    print("✅ LoRA appliqué avec succès")
    return peft_model, tokenizer

def train_with_llama_stack():
    """Lance l'entraînement avec le modèle Llama Stack"""
    print("\\n=== Entraînement avec Llama Stack ===")
    
    # Appliquer LoRA
    peft_model, tokenizer = apply_lora_to_llama_stack()
    if peft_model is None:
        return False
    
    # Charger les données
    try:
        dataset = load_from_disk("./formatted_data")
        print(f"Dataset chargé: {len(dataset['train'])} exemples")
    except:
        print("❌ Données non trouvées. Exécutez d'abord prepare_training_data.py")
        return False
    
    # Configuration d'entraînement
    from transformers import TrainingArguments, Trainer, DataCollatorForLanguageModeling
    
    training_args = TrainingArguments(
        output_dir="./llama_stack_results",
        num_train_epochs=3,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,
        learning_rate=2e-4,
        weight_decay=0.01,
        warmup_steps=100,
        logging_steps=10,
        eval_steps=100,
        save_steps=100,
        eval_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        report_to=None,
        remove_unused_columns=False,
        fp16=torch.cuda.is_available(),
    )
    
    # Tokeniser les données
    def tokenize_function(examples):
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=512,
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized
    
    tokenized_dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=dataset["train"].column_names
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=peft_model,
        args=training_args,
        train_dataset=tokenized_dataset["train"],
        eval_dataset=tokenized_dataset["validation"],
        data_collator=data_collator,
    )
    
    # Entraînement
    print("Démarrage de l'entraînement...")
    try:
        trainer.train()
        trainer.save_model()
        tokenizer.save_pretrained("./llama_stack_results")
        print("✅ Entraînement terminé et modèle sauvegardé")
        return True
    except Exception as e:
        print(f"❌ Erreur d'entraînement: {e}")
        return False

if __name__ == "__main__":
    train_with_llama_stack()
'''
    
    with open("llama_stack_training.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Script de fine-tuning créé: llama_stack_training.py")

def main():
    """Fonction principale"""
    print("=== Configuration Llama Stack pour Fine-tuning LoRA ===\n")
    
    setup = LlamaStackSetup()
    
    # 1. Installer/mettre à jour Llama Stack
    if not setup.install_llama_stack():
        print("❌ Impossible d'installer Llama Stack")
        return
    
    # 2. Lister les modèles disponibles
    setup.list_available_models()
    
    # 3. Vérifier les modèles locaux
    setup.check_local_models()
    
    # 4. Proposer de télécharger Llama 3.1-8B
    print("\nVoulez-vous télécharger Meta-Llama-3.1-8B? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if setup.download_llama_model("Meta-Llama-3.1-8B"):
            print("✅ Modèle téléchargé avec succès")
        else:
            print("❌ Échec du téléchargement")
    
    # 5. Créer la configuration
    setup.create_llama_stack_config()
    
    # 6. Créer le script de fine-tuning
    create_llama_stack_training_script()
    
    print("\n" + "="*50)
    print("PROCHAINES ÉTAPES:")
    print("1. ✅ Llama Stack configuré")
    print("2. 📥 Télécharger le modèle si pas encore fait")
    print("3. 🚀 Lancer: python llama_stack_training.py")
    print("4. 🎯 Intégrer avec Ollama")

if __name__ == "__main__":
    main()
