"""
Fine-tuning Llama 3.1 8B - HAUTE FIDÉLITÉ au dataset
Configuration optimisée pour mémorisation maximale du dataset wasifis/bank-assistant-qa
"""

import os
import json
import torch
from datetime import datetime
from pathlib import Path

from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    prepare_model_for_kbit_training
)
from datasets import load_from_disk
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HighFidelityFineTuner:
    """Fine-tuner optimisé pour fidélité maximale au dataset"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.dataset = None
        
        # Configuration HAUTE FIDÉLITÉ
        self.config = {
            "model_settings": {
                "model_path": "./models/Llama-3.1-8B-Instruct",
                "load_in_4bit": True,
                "bnb_4bit_compute_dtype": "float16",
                "bnb_4bit_use_double_quant": True,
                "bnb_4bit_quant_type": "nf4"
            },
            "lora_settings": {
                "r": 32,  # Rank plus élevé pour plus de capacité
                "lora_alpha": 64,  # Alpha plus élevé
                "lora_dropout": 0.05,  # Dropout plus faible
                "target_modules": [
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"  # Tous les modules
                ],
                "bias": "none"
            },
            "training_settings": {
                "output_dir": "./llama_banking_high_fidelity",
                "num_train_epochs": 3,  # Plus d'epochs
                "per_device_train_batch_size": 1,
                "per_device_eval_batch_size": 1,
                "gradient_accumulation_steps": 16,  # Plus d'accumulation
                "learning_rate": 5e-5,  # Learning rate plus bas
                "weight_decay": 0.01,
                "warmup_ratio": 0.05,  # Moins de warmup
                "lr_scheduler_type": "cosine",
                "max_grad_norm": 0.5,  # Gradient clipping plus strict
                "optim": "paged_adamw_8bit",
                "fp16": True,
                "gradient_checkpointing": True,
                "dataloader_pin_memory": False,
                "dataloader_num_workers": 0,
                "logging_steps": 5,
                "eval_steps": 50,
                "save_steps": 100,
                "save_total_limit": 5,  # Plus de checkpoints
                "max_steps": -1,  # Pas de limite, utiliser epochs
                "report_to": [],
                "remove_unused_columns": False,
                "dataloader_drop_last": False,
                "load_best_model_at_end": True,
                "metric_for_best_model": "eval_loss",
                "greater_is_better": False,
            }
        }
    
    def setup_environment(self):
        """Configure l'environnement pour fidélité maximale"""
        logger.info("=== Configuration Haute Fidélité ===")
        
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA requis")
        
        # Optimisations pour stabilité
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        # Variables d'environnement
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        os.environ["CUDA_LAUNCH_BLOCKING"] = "0"
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
        
        # Vider le cache
        torch.cuda.empty_cache()
        
        # Informations GPU
        device = torch.cuda.current_device()
        gpu_props = torch.cuda.get_device_properties(device)
        logger.info(f"✅ GPU: {gpu_props.name}")
        logger.info(f"✅ Mémoire: {gpu_props.total_memory / 1024**3:.1f} GB")
    
    def load_model_and_tokenizer(self):
        """Charge le modèle avec configuration optimisée"""
        logger.info("=== Chargement Modèle (Haute Fidélité) ===")
        
        model_path = self.config["model_settings"]["model_path"]
        
        try:
            # Configuration 4-bit optimisée
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Tokenizer
            logger.info("📝 Chargement tokenizer...")
            if Path(model_path).exists():
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # Modèle
            logger.info("🦙 Chargement modèle...")
            if Path(model_path).exists():
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    token="*************************************"
                )
            
            # Préparer pour l'entraînement
            self.model = prepare_model_for_kbit_training(self.model)
            
            logger.info(f"✅ Modèle chargé: {self.model.num_parameters():,} paramètres")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur: {e}")
            return False
    
    def setup_lora_high_capacity(self):
        """Configure LoRA avec capacité élevée"""
        logger.info("=== Configuration LoRA Haute Capacité ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            **self.config["lora_settings"]
        )
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, lora_config)
        
        # Afficher les paramètres
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA haute capacité configuré")
        return True
    
    def load_dataset(self):
        """Charge le dataset préparé"""
        logger.info("=== Chargement Dataset ===")
        
        dataset_path = "./processed_banking_dataset_no_instruction"
        
        try:
            self.dataset = load_from_disk(dataset_path)
            
            # Créer un split de validation (10% des données)
            train_dataset = self.dataset["train"]
            
            # Split train/eval
            train_size = int(0.9 * len(train_dataset))
            eval_size = len(train_dataset) - train_size
            
            train_split = train_dataset.select(range(train_size))
            eval_split = train_dataset.select(range(train_size, train_size + eval_size))
            
            self.dataset = {
                "train": train_split,
                "eval": eval_split
            }
            
            logger.info(f"✅ Dataset chargé:")
            logger.info(f"  - Train: {len(self.dataset['train'])} exemples")
            logger.info(f"  - Eval: {len(self.dataset['eval'])} exemples")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur dataset: {e}")
            return False
    
    def train_high_fidelity(self):
        """Lance l'entraînement haute fidélité"""
        logger.info("=== Fine-tuning Haute Fidélité ===")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_settings"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer avec validation
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=self.dataset["train"],
            eval_dataset=self.dataset["eval"],
            data_collator=data_collator,
            processing_class=self.tokenizer,
        )
        
        try:
            # Entraînement
            logger.info("🚀 Début entraînement haute fidélité...")
            logger.info(f"📊 Configuration:")
            logger.info(f"  - Epochs: {self.config['training_settings']['num_train_epochs']}")
            logger.info(f"  - Learning rate: {self.config['training_settings']['learning_rate']}")
            logger.info(f"  - LoRA rank: {self.config['lora_settings']['r']}")
            logger.info(f"  - Batch size effectif: {self.config['training_settings']['gradient_accumulation_steps']}")
            
            train_result = trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_settings"]["output_dir"])
            
            # Métriques
            metrics = train_result.metrics
            with open(Path(self.config["training_settings"]["output_dir"]) / "training_metrics.json", 'w') as f:
                json.dump(metrics, f, indent=2)
            
            # Évaluation finale
            eval_results = trainer.evaluate()
            logger.info(f"📊 Résultats finaux:")
            logger.info(f"  - Train loss: {metrics.get('train_loss', 'N/A')}")
            logger.info(f"  - Eval loss: {eval_results.get('eval_loss', 'N/A')}")
            
            logger.info("✅ Fine-tuning haute fidélité terminé!")
            logger.info(f"✅ Modèle sauvegardé: {self.config['training_settings']['output_dir']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False

def main():
    """Fonction principale"""
    print("🎯 Fine-tuning Llama 3.1 8B - HAUTE FIDÉLITÉ")
    print("🏦 Dataset: wasifis/bank-assistant-qa")
    print("🎮 RTX 4060 8GB + CUDA 12.9")
    print("=" * 60)
    
    fine_tuner = HighFidelityFineTuner()
    
    # 1. Configuration
    fine_tuner.setup_environment()
    
    # 2. Modèle
    print("\n1. Chargement du modèle...")
    if not fine_tuner.load_model_and_tokenizer():
        print("❌ Échec du chargement")
        return
    
    # 3. LoRA haute capacité
    print("\n2. Configuration LoRA haute capacité...")
    fine_tuner.setup_lora_high_capacity()
    
    # 4. Dataset avec validation
    print("\n3. Chargement dataset avec validation...")
    if not fine_tuner.load_dataset():
        print("❌ Dataset non trouvé")
        return
    
    # 5. Confirmer
    print("\n4. Configuration HAUTE FIDÉLITÉ:")
    print("  🎯 3 epochs complets")
    print("  📊 LoRA rank 32 (haute capacité)")
    print("  🧠 Learning rate 5e-5 (mémorisation)")
    print("  📈 Validation continue")
    print("  💾 Sauvegarde: ./llama_banking_high_fidelity")
    print("  ⏱️ Temps estimé: 45-60 minutes")
    
    confirm = input("\nLancer le fine-tuning haute fidélité? (y/n): ").lower()
    if confirm != 'y':
        print("Fine-tuning annulé")
        return
    
    # 6. Entraîner
    print("\n5. Lancement du fine-tuning haute fidélité...")
    if fine_tuner.train_high_fidelity():
        print("\n🎉 FINE-TUNING HAUTE FIDÉLITÉ TERMINÉ!")
        print("🏦 Chatbot bancaire optimisé pour fidélité maximale!")
        print("📁 Modèle: ./llama_banking_high_fidelity")
        print("🧪 Testez avec: python test_high_fidelity_model.py")
    else:
        print("\n❌ Échec du fine-tuning")

if __name__ == "__main__":
    main()
