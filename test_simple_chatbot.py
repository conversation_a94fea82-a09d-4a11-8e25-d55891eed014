"""
Test simple du chatbot bancaire fine-tuné
Version optimisée pour mémoire limitée
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import time
import json

print("🏦 TEST SIMPLE - CHATBOT BANCAIRE")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Vérification GPU
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Vider le cache GPU
torch.cuda.empty_cache()

def load_finetuned_model_only():
    """Charge seulement le modèle fine-tuné pour économiser la mémoire"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle de base (optimisé mémoire)...")
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            offload_folder="./offload_temp"  # Utiliser le disque si nécessaire
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            offload_folder="./offload_temp",
            token="*************************************"
        )
    
    print("✅ Modèle de base chargé")
    
    print("🔧 Chargement adaptateur LoRA...")
    try:
        finetuned_model = PeftModel.from_pretrained(
            base_model, 
            adapter_path,
            torch_dtype=torch.float16,
            offload_folder="./offload_temp"
        )
        print("✅ Modèle fine-tuné chargé avec succès!")
        return tokenizer, finetuned_model
    except Exception as e:
        print(f"⚠️ Erreur adaptateur principal: {e}")
        try:
            checkpoint_path = f"{adapter_path}/checkpoint-570"
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                checkpoint_path,
                torch_dtype=torch.float16,
                offload_folder="./offload_temp"
            )
            print("✅ Modèle fine-tuné chargé (checkpoint)!")
            return tokenizer, finetuned_model
        except Exception as e2:
            print(f"❌ Impossible de charger le fine-tuné: {e2}")
            print("💡 Utilisation du modèle de base seulement")
            return tokenizer, base_model

# Chargement du modèle
tokenizer, model = load_finetuned_model_only()

def generate_response(question, max_length=150):
    """Génère une réponse rapidement"""
    
    prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
    
    # Déplacer vers GPU si possible
    try:
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
    except:
        pass
    
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            temperature=0.1,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            repetition_penalty=1.1
        )
    
    generation_time = time.time() - start_time
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    if "<|start_header_id|>assistant<|end_header_id|>" in response:
        response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
    
    return response, generation_time

# Tests bancaires
print("\n" + "="*50)
print("🧪 TESTS BANCAIRES")
print("="*50)

test_questions = [
    "What are the charges for account maintenance?",
    "How to open a savings account?", 
    "What is the minimum balance required?",
    "How to transfer money between accounts?",
    "What are the interest rates for loans?"
]

results = []

for i, question in enumerate(test_questions):
    print(f"\n📋 Test {i+1}/{len(test_questions)}: {question}")
    
    try:
        response, response_time = generate_response(question)
        
        # Évaluation bancaire
        banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee', 'credit', 'debit'] 
                           if word in response.lower())
        
        print(f"⏱️ Temps: {response_time:.2f}s")
        print(f"🎯 Termes bancaires: {banking_words}")
        print(f"💬 Réponse: {response[:150]}...")
        
        results.append({
            "question": question,
            "response": response,
            "time": response_time,
            "banking_words": banking_words
        })
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        results.append({
            "question": question,
            "response": "Erreur",
            "time": 0,
            "banking_words": 0
        })

# Test interactif
print("\n" + "="*50)
print("💬 TEST INTERACTIF")
print("="*50)

print("Posez vos questions bancaires (tapez 'quit' pour arrêter)")
print("Exemples: 'How to close account?', 'What are overdraft fees?'")

while True:
    try:
        user_question = input("\n❓ Votre question: ").strip()
        
        if user_question.lower() in ['quit', 'exit', 'stop', 'q']:
            break
            
        if not user_question:
            continue
            
        print("🤖 Réponse...")
        response, response_time = generate_response(user_question)
        
        banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                           if word in response.lower())
        
        print(f"⏱️ {response_time:.2f}s | 🎯 {banking_words} termes bancaires")
        print(f"💬 {response}")
        
    except KeyboardInterrupt:
        break
    except Exception as e:
        print(f"❌ Erreur: {e}")

# Résumé
print("\n" + "="*50)
print("📊 RÉSUMÉ")
print("="*50)

if results:
    avg_time = sum(r["time"] for r in results if r["time"] > 0) / len([r for r in results if r["time"] > 0])
    avg_banking = sum(r["banking_words"] for r in results) / len(results)
    
    print(f"⏱️ Temps moyen: {avg_time:.2f}s")
    print(f"🎯 Termes bancaires moyens: {avg_banking:.1f}")
    
    if avg_banking >= 3:
        print("🏆 EXCELLENT: Très bonne spécialisation bancaire!")
    elif avg_banking >= 2:
        print("✅ BON: Bonne spécialisation bancaire")
    else:
        print("⚠️ MOYEN: Spécialisation bancaire modérée")

print(f"\n✅ TESTS TERMINÉS:")
print(f"  ✅ Modèle fine-tuné testé")
print(f"  ✅ {len(test_questions)} questions bancaires")
print(f"  ✅ Test interactif validé")

print(f"\n🏦 VOTRE CHATBOT BANCAIRE:")
print(f"📁 Modèle: {adapter_path}")
print(f"🎯 Spécialisé dans les questions bancaires")
print(f"⚡ Prêt pour utilisation!")

# Sauvegarde
with open("simple_test_results.json", "w") as f:
    json.dump({
        "avg_time": avg_time if results else 0,
        "avg_banking_words": avg_banking if results else 0,
        "results": results
    }, f, indent=2)

print(f"💾 Résultats: simple_test_results.json")
