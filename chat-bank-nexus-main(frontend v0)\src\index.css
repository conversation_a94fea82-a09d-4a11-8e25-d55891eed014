@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animations ultra-modernes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-ring {
  0% { transform: scale(0.8); opacity: 1; }
  100% { transform: scale(2.4); opacity: 0; }
}

@keyframes slide-up {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Classes utilitaires modernes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-ring {
  animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out;
}

/* Effets de verre moderne */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradients modernes */
.gradient-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-chat {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Ombres modernes */
.shadow-modern {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-glow-primary {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-success {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

/* Transitions fluides */
.transition-modern {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Mode Clair par défaut */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 221 83% 63%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 263 70% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 221 83% 53%;

    --gradient-primary: linear-gradient(135deg, hsl(221, 83%, 53%), hsl(263, 70%, 50%));
    --gradient-surface: linear-gradient(145deg, hsl(0, 0%, 100%), hsl(240, 4.8%, 95.9%));
    --gradient-accent: linear-gradient(90deg, hsl(221, 83%, 53%), hsl(221, 83%, 63%));

    --shadow-elegant: 0 25px 50px -12px hsl(221 83% 53% / 0.15);
    --shadow-glow: 0 0 50px hsl(221 83% 53% / 0.1);

    --animation-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --animation-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Mode Sombre amélioré */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 221 83% 63%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 263 70% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 221 83% 53%;

    --gradient-primary: linear-gradient(135deg, hsl(221, 83%, 53%), hsl(263, 70%, 50%));
    --gradient-surface: linear-gradient(145deg, hsl(240, 10%, 3.9%), hsl(240, 3.7%, 15.9%));
    --gradient-accent: linear-gradient(90deg, hsl(221, 83%, 53%), hsl(221, 83%, 63%));

    --shadow-elegant: 0 25px 50px -12px hsl(221 83% 53% / 0.25);
    --shadow-glow: 0 0 50px hsl(221 83% 53% / 0.15);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}