"""
Script de démarrage complet pour votre chatbot
Lance automatiquement Backend RAG + Frontend React
"""

import subprocess
import time
import sys
import os
import threading
import requests
from pathlib import Path

def check_requirements():
    """Vérifie les prérequis"""
    print("🔍 Vérification des prérequis...")
    
    # Vérifier Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ requis")
        return False
    
    # Vérifier les dossiers
    if not Path("chat-bank-nexus-main(frontend v0)").exists():
        print("❌ Dossier frontend non trouvé")
        return False
    
    if not Path("llama_banking_final_fidelity").exists():
        print("❌ Modèle fine-tuné non trouvé")
        return False
    
    print("✅ Prérequis validés")
    return True

def install_backend_dependencies():
    """Installe les dépendances backend"""
    print("📦 Installation des dépendances backend...")
    
    dependencies = [
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "pydantic"
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✅ {dep} installé")
        except subprocess.CalledProcessError:
            print(f"⚠️ Erreur installation {dep}")

def install_frontend_dependencies():
    """Installe les dépendances frontend"""
    print("📦 Installation des dépendances frontend...")

    frontend_dir = Path("chat-bank-nexus-main(frontend v0)")

    # Essayer différentes commandes npm
    npm_commands = ["npm", "npm.cmd", "npm.exe"]

    for npm_cmd in npm_commands:
        try:
            # Tester si npm fonctionne
            result = subprocess.run([npm_cmd, "--version"],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ npm trouvé: {npm_cmd} (version {result.stdout.strip()})")

                # Vérifier si node_modules existe
                if not (frontend_dir / "node_modules").exists():
                    print("📥 Installation des packages npm...")
                    subprocess.run([npm_cmd, "install"],
                                 cwd=frontend_dir,
                                 check=True)
                    print("✅ Packages npm installés")
                else:
                    print("✅ Packages npm déjà installés")
                return True

        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            continue

    print("⚠️ npm non trouvé automatiquement")
    print("💡 Lancez manuellement: cd \"chat-bank-nexus-main(frontend v0)\" && npm install")
    return False

def start_backend():
    """Lance le backend RAG"""
    print("🚀 Démarrage du backend RAG...")
    
    try:
        # Lancer l'API FastAPI
        process = subprocess.Popen([
            sys.executable, "backend_rag_api.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre que l'API soit prête
        print("⏳ Attente du démarrage de l'API...")
        for i in range(30):  # 30 secondes max
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Backend RAG démarré!")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"⏳ Tentative {i+1}/30...")
        
        print("⚠️ Backend prend du temps à démarrer (normal pour le premier lancement)")
        return process
        
    except Exception as e:
        print(f"❌ Erreur démarrage backend: {e}")
        return None

def start_frontend():
    """Lance le frontend React"""
    print("🚀 Démarrage du frontend React...")

    frontend_dir = Path("chat-bank-nexus-main(frontend v0)")

    # Essayer différentes commandes npm
    npm_commands = ["npm", "npm.cmd", "npm.exe"]

    for npm_cmd in npm_commands:
        try:
            # Tester si npm fonctionne
            result = subprocess.run([npm_cmd, "--version"],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Utilisation de {npm_cmd}")

                # Lancer Vite dev server
                process = subprocess.Popen([
                    npm_cmd, "run", "dev"
                ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Attendre que le frontend soit prêt
                print("⏳ Attente du démarrage du frontend...")
                for i in range(20):  # 20 secondes max
                    try:
                        # Essayer les deux ports possibles
                        for port in [8080, 5173]:
                            try:
                                response = requests.get(f"http://localhost:{port}", timeout=2)
                                if response.status_code == 200:
                                    print(f"✅ Frontend React démarré sur port {port}!")
                                    return process
                            except:
                                continue
                    except:
                        pass
                    time.sleep(1)

                print("✅ Frontend en cours de démarrage...")
                return process

        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            continue

    print("❌ Impossible de démarrer le frontend")
    print("💡 Lancez manuellement: cd \"chat-bank-nexus-main(frontend v0)\" && npm run dev")
    return None

def monitor_processes(backend_process, frontend_process):
    """Surveille les processus"""
    print("\n" + "="*60)
    print("🎉 CHATBOT BANCAIRE DÉMARRÉ!")
    print("="*60)
    print("🔗 Frontend: http://localhost:8080")
    print("📡 API Backend: http://localhost:8000")
    print("📚 Documentation API: http://localhost:8000/docs")
    print("="*60)
    print("💡 Votre chatbot utilise:")
    print("   🦙 Votre modèle fine-tuné (llama_banking_final_fidelity)")
    print("   📊 RAG avec base wasifis/bank-assistant-qa")
    print("   ⚛️ Frontend React + TypeScript")
    print("   🚀 API FastAPI")
    print("="*60)
    print("⌨️ Appuyez sur Ctrl+C pour arrêter")
    
    try:
        while True:
            # Vérifier si les processus sont toujours actifs
            if backend_process and backend_process.poll() is not None:
                print("❌ Backend arrêté")
                break
            if frontend_process and frontend_process.poll() is not None:
                print("❌ Frontend arrêté")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé...")
        
        if backend_process:
            backend_process.terminate()
            print("✅ Backend arrêté")
            
        if frontend_process:
            frontend_process.terminate()
            print("✅ Frontend arrêté")
        
        print("🎉 Chatbot arrêté proprement!")

def main():
    """Fonction principale"""
    print("🎯 DÉMARRAGE CHATBOT BANCAIRE COMPLET")
    print("=" * 60)
    
    # Vérifications
    if not check_requirements():
        print("❌ Prérequis non satisfaits")
        return
    
    # Installation des dépendances
    install_backend_dependencies()
    install_frontend_dependencies()
    
    # Démarrage des services
    print("\n🚀 DÉMARRAGE DES SERVICES...")
    print("=" * 40)
    
    # Backend en premier
    backend_process = start_backend()
    if not backend_process:
        print("❌ Impossible de démarrer le backend")
        return
    
    # Attendre un peu avant le frontend
    time.sleep(3)
    
    # Frontend
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Impossible de démarrer le frontend")
        if backend_process:
            backend_process.terminate()
        return
    
    # Surveillance
    monitor_processes(backend_process, frontend_process)

if __name__ == "__main__":
    main()
