#!/usr/bin/env python3
"""
Script de démarrage professionnel pour le chatbot bancaire
Démarre tous les services nécessaires de façon coordonnée
"""

import os
import sys
import time
import subprocess
import threading
import signal
import requests
from pathlib import Path

class ChatbotLauncher:
    def __init__(self):
        self.processes = []
        self.services = {
            'rag': {'port': 8000, 'script': 'backend_rag_api.py', 'status': False},
            'speech': {'port': 8004, 'script': 'compatible_speech_service.py', 'status': False},
            'frontend': {'port': 8080, 'script': None, 'status': False}
        }
        
    def check_dependencies(self):
        """Vérifie que toutes les dépendances sont installées"""
        print("🔍 Vérification des dépendances...")
        
        required_packages = [
            'torch', 'transformers', 'peft', 'whisper', 'gtts',
            'fastapi', 'uvicorn', 'scikit-learn', 'numpy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                # Gestion spéciale pour sklearn
                if package == 'scikit-learn':
                    import sklearn
                else:
                    __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package}")
        
        if missing_packages:
            print(f"\n❌ Packages manquants: {', '.join(missing_packages)}")
            print("Installez-les avec:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ Toutes les dépendances sont installées")
        return True
    
    def check_models(self):
        """Vérifie que les modèles nécessaires sont disponibles"""
        print("\n🤖 Vérification des modèles...")
        
        # Vérifier le modèle fine-tuné
        adapter_path = "./llama_banking_final_fidelity"
        if os.path.exists(adapter_path):
            print("  ✅ Modèle fine-tuné trouvé")
        else:
            print("  ⚠️ Modèle fine-tuné non trouvé")
            print(f"     Recherché dans: {adapter_path}")
        
        # Vérifier la base de données
        db_file = "cleaned_banking_qa.json"
        if os.path.exists(db_file):
            print("  ✅ Base de données trouvée")
        else:
            print("  ⚠️ Base de données non trouvée")
            print(f"     Recherché: {db_file}")
        
        return True
    
    def start_service(self, service_name, script_path, port):
        """Démarre un service en arrière-plan"""
        print(f"🚀 Démarrage {service_name} sur le port {port}...")
        
        try:
            if script_path:
                process = subprocess.Popen([
                    sys.executable, script_path
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.processes.append(process)
                
                # Attendre que le service soit prêt
                self.wait_for_service(port, service_name)
                self.services[service_name]['status'] = True
                print(f"  ✅ {service_name} démarré avec succès")
            
        except Exception as e:
            print(f"  ❌ Erreur démarrage {service_name}: {e}")
            return False
        
        return True
    
    def wait_for_service(self, port, service_name, timeout=60):
        """Attend qu'un service soit prêt"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=1)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        
        print(f"  ⚠️ Timeout pour {service_name}")
        return False
    
    def start_frontend(self):
        """Démarre le frontend React"""
        frontend_dir = "chat-bank-nexus-main(frontend v0)"
        
        if not os.path.exists(frontend_dir):
            print(f"❌ Dossier frontend non trouvé: {frontend_dir}")
            return False
        
        print("🚀 Démarrage du frontend React...")
        
        try:
            # Vérifier si node_modules existe
            node_modules = os.path.join(frontend_dir, "node_modules")
            if not os.path.exists(node_modules):
                print("📦 Installation des dépendances npm...")
                subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
            
            # Démarrer le serveur de développement
            process = subprocess.Popen([
                "npm", "run", "dev"
            ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(process)
            
            # Attendre que le frontend soit prêt
            time.sleep(5)  # Le frontend prend plus de temps
            self.services['frontend']['status'] = True
            print("  ✅ Frontend démarré avec succès")
            
        except Exception as e:
            print(f"  ❌ Erreur démarrage frontend: {e}")
            return False
        
        return True
    
    def display_status(self):
        """Affiche le statut des services"""
        print("\n" + "="*60)
        print("📊 STATUT DES SERVICES")
        print("="*60)
        
        for name, info in self.services.items():
            status = "🟢 ACTIF" if info['status'] else "🔴 INACTIF"
            port = info['port']
            print(f"{name.upper():12} | Port {port:4} | {status}")
        
        print("\n🌐 URLS D'ACCÈS:")
        if self.services['rag']['status']:
            print(f"  • RAG API:     http://localhost:8000/docs")
        if self.services['speech']['status']:
            print(f"  • Speech API:  http://localhost:8004/docs")
        if self.services['frontend']['status']:
            print(f"  • Frontend:    http://localhost:5173")
        
        print("\n💡 COMMANDES UTILES:")
        print("  • Ctrl+C pour arrêter tous les services")
        print("  • Vérifiez les logs en cas de problème")
    
    def cleanup(self):
        """Nettoie tous les processus"""
        print("\n🧹 Arrêt des services...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print("  ✅ Service arrêté")
            except:
                try:
                    process.kill()
                    print("  ⚠️ Service forcé à s'arrêter")
                except:
                    print("  ❌ Impossible d'arrêter le service")
    
    def run(self):
        """Lance tous les services"""
        print("🎯 DÉMARRAGE CHATBOT BANCAIRE PROFESSIONNEL")
        print("="*60)
        
        # Vérifications préliminaires
        if not self.check_dependencies():
            return False
        
        self.check_models()
        
        # Gestionnaire de signal pour nettoyage
        def signal_handler(sig, frame):
            self.cleanup()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        try:
            # Démarrer les services backend
            self.start_service('speech', 'professional_speech_service.py', 8004)
            time.sleep(2)  # Laisser le temps au service Speech de se charger
            
            self.start_service('rag', 'rag_developed_responses.py', 8000)
            time.sleep(2)
            
            # Démarrer le frontend
            self.start_frontend()
            
            # Afficher le statut
            self.display_status()
            
            # Garder le script actif
            print("\n⏳ Services en cours d'exécution... (Ctrl+C pour arrêter)")
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.cleanup()

if __name__ == "__main__":
    launcher = ChatbotLauncher()
    launcher.run()
