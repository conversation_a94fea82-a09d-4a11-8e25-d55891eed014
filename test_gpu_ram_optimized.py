"""
Test optimisé GPU + RAM avec gestion intelligente de la mémoire
Utilise votre GPU 8GB + RAM 16GB efficacement
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import time
import psutil

print("🏦 TEST OPTIMISÉ GPU + RAM - CHATBOT BANCAIRE")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Vérifications
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")
print(f"💾 GPU VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"💾 RAM totale: {psutil.virtual_memory().total / 1024**3:.1f} GB")
print(f"💾 RAM libre: {psutil.virtual_memory().available / 1024**3:.1f} GB")

# Vider le cache GPU
torch.cuda.empty_cache()

def load_optimized_model():
    """Charge le modèle avec répartition intelligente GPU/RAM"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle optimisé...")
    
    # Configuration device_map personnalisée
    device_map = {
        "model.embed_tokens": "cuda:0",
        "model.norm": "cuda:0",
        "lm_head": "cuda:0",
    }
    
    # Répartir les couches entre GPU et CPU
    for i in range(32):  # Llama 3.1 8B a 32 couches
        if i < 20:  # Premières 20 couches sur GPU
            device_map[f"model.layers.{i}"] = "cuda:0"
        else:  # Dernières 12 couches sur CPU (RAM)
            device_map[f"model.layers.{i}"] = "cpu"
    
    if Path(model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map=device_map,
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            offload_buffers=True  # Important pour éviter les conflits
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            torch_dtype=torch.float16,
            device_map=device_map,
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            offload_buffers=True,
            token="*************************************"
        )
    
    print(f"✅ Modèle de base chargé (GPU + RAM)")
    print(f"💾 GPU utilisé: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    print(f"💾 RAM utilisée: {psutil.virtual_memory().used / 1024**3:.1f} GB")
    
    print("🔧 Chargement adaptateur LoRA...")
    try:
        finetuned_model = PeftModel.from_pretrained(
            base_model, 
            adapter_path,
            torch_dtype=torch.float16
        )
        print("✅ Modèle fine-tuné chargé avec succès!")
        return tokenizer, finetuned_model
    except Exception as e:
        print(f"⚠️ Erreur adaptateur principal: {e}")
        try:
            checkpoint_path = f"{adapter_path}/checkpoint-570"
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                checkpoint_path,
                torch_dtype=torch.float16
            )
            print("✅ Modèle fine-tuné chargé (checkpoint)!")
            return tokenizer, finetuned_model
        except Exception as e2:
            print(f"❌ Impossible de charger le fine-tuné: {e2}")
            print("💡 Utilisation du modèle de base optimisé")
            return tokenizer, base_model

# Chargement du modèle
try:
    tokenizer, model = load_optimized_model()
    
    print(f"\n💾 État final:")
    print(f"  GPU: {torch.cuda.memory_allocated() / 1024**3:.2f} GB utilisés")
    print(f"  RAM: {psutil.virtual_memory().used / 1024**3:.1f} GB utilisés")
    print(f"  RAM libre: {psutil.virtual_memory().available / 1024**3:.1f} GB")
    
    def generate_response(question, max_length=150):
        """Génère une réponse avec modèle hybride GPU/RAM"""
        
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        # Les inputs vont automatiquement sur le bon device
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.1,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        generation_time = time.time() - start_time
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        if "<|start_header_id|>assistant<|end_header_id|>" in response:
            response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        
        return response, generation_time

    # Test simple
    print("\n🧪 TEST OPTIMISÉ:")
    question = "What are the charges for account maintenance?"
    print(f"❓ Question: {question}")
    
    response, response_time = generate_response(question)
    
    banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                       if word in response.lower())
    
    print(f"⏱️ Temps: {response_time:.2f}s")
    print(f"🎯 Termes bancaires: {banking_words}")
    print(f"💬 Réponse: {response}")
    print(f"💾 GPU: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    print(f"💾 RAM: {psutil.virtual_memory().used / 1024**3:.1f} GB")
    
    print(f"\n✅ SUCCÈS! Modèle hybride GPU+RAM fonctionne!")
    print(f"🎯 Utilise efficacement vos 8GB GPU + 16GB RAM")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print(f"\n💡 SOLUTIONS:")
    print(f"1. Essayez le mode CPU pur: python test_cpu_ram.py")
    print(f"2. Fermez d'autres applications pour libérer la RAM")
    print(f"3. Redémarrez pour nettoyer la mémoire")

print(f"\n🎯 DIAGNOSTIC TERMINÉ!")
print(f"💾 Votre configuration: GPU 8GB + RAM 16GB est excellente!")
