"""
Test et évaluation du modèle Llama 3.1 8B fine-tuné
"""

import torch
import json
import time
from pathlib import Path
from typing import List, Dict, Any
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import pandas as pd

class LlamaFineTunedTester:
    """Testeur pour le modèle Llama 3.1 fine-tuné"""
    
    def __init__(self, model_path: str = "./llama_banking_finetuned"):
        self.model_path = Path(model_path)
        self.base_model = None
        self.finetuned_model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def load_models(self):
        """Charge le modèle de base et le modèle fine-tuné"""
        print("=== Chargement des modèles ===")
        
        try:
            # Charger le tokenizer
            print("Chargement du tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                trust_remote_code=True
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            print("Chargement du modèle de base...")
            self.base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            
            # Charger le modèle fine-tuné
            print("Chargement du modèle fine-tuné...")
            self.finetuned_model = PeftModel.from_pretrained(
                self.base_model,
                self.model_path
            )
            
            print("✅ Modèles chargés avec succès")
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            return False
    
    def generate_response(self, model, prompt: str, max_length: int = 512) -> str:
        """Génère une réponse avec le modèle"""
        
        # Format de chat Llama 3.1
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel.<|eot_id|><|start_header_id|>user<|end_header_id|>

{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_length,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        return response
    
    def benchmark_performance(self, questions: List[str]) -> Dict[str, Any]:
        """Benchmark de performance entre modèles"""
        print("\n=== Benchmark de Performance ===")
        
        results = {
            "base_model": {"responses": [], "times": []},
            "finetuned_model": {"responses": [], "times": []}
        }
        
        for i, question in enumerate(questions):
            print(f"\nQuestion {i+1}/{len(questions)}: {question}")
            
            # Test modèle de base
            start_time = time.time()
            base_response = self.generate_response(self.base_model, question)
            base_time = time.time() - start_time
            
            results["base_model"]["responses"].append(base_response)
            results["base_model"]["times"].append(base_time)
            
            # Test modèle fine-tuné
            start_time = time.time()
            finetuned_response = self.generate_response(self.finetuned_model, question)
            finetuned_time = time.time() - start_time
            
            results["finetuned_model"]["responses"].append(finetuned_response)
            results["finetuned_model"]["times"].append(finetuned_time)
            
            print(f"  Base ({base_time:.2f}s): {base_response[:100]}...")
            print(f"  Fine-tuned ({finetuned_time:.2f}s): {finetuned_response[:100]}...")
        
        # Statistiques
        base_avg_time = sum(results["base_model"]["times"]) / len(results["base_model"]["times"])
        ft_avg_time = sum(results["finetuned_model"]["times"]) / len(results["finetuned_model"]["times"])
        
        print(f"\n📊 Statistiques de performance:")
        print(f"  Temps moyen base: {base_avg_time:.2f}s")
        print(f"  Temps moyen fine-tuné: {ft_avg_time:.2f}s")
        
        return results
    
    def evaluate_banking_knowledge(self) -> Dict[str, Any]:
        """Évalue les connaissances bancaires spécifiques"""
        print("\n=== Évaluation Connaissances Bancaires ===")
        
        banking_questions = [
            "Quels sont les frais de tenue de compte pour un compte courant?",
            "Comment ouvrir un compte épargne et quels documents sont nécessaires?",
            "Quelles sont les conditions d'éligibilité pour un prêt immobilier?",
            "Comment activer une nouvelle carte bancaire?",
            "Que faire en cas de perte ou vol de carte bancaire?",
            "Quels sont les différents types de comptes bancaires disponibles?",
            "Comment effectuer un virement international?",
            "Quelles sont les garanties offertes par l'assurance emprunteur?",
            "Comment contester un prélèvement non autorisé?",
            "Quels sont les horaires d'ouverture des agences bancaires?"
        ]
        
        results = self.benchmark_performance(banking_questions)
        
        # Analyse qualitative
        print(f"\n📋 Analyse qualitative:")
        for i, question in enumerate(banking_questions):
            print(f"\n{i+1}. {question}")
            print(f"Base: {results['base_model']['responses'][i][:150]}...")
            print(f"Fine-tuned: {results['finetuned_model']['responses'][i][:150]}...")
            print("-" * 80)
        
        return results
    
    def save_evaluation_report(self, results: Dict[str, Any]):
        """Sauvegarde le rapport d'évaluation"""
        print("\n=== Sauvegarde du rapport ===")
        
        # Créer le rapport
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model_path": str(self.model_path),
            "device": str(self.device),
            "performance_stats": {
                "base_model_avg_time": sum(results["base_model"]["times"]) / len(results["base_model"]["times"]),
                "finetuned_model_avg_time": sum(results["finetuned_model"]["times"]) / len(results["finetuned_model"]["times"]),
                "total_questions": len(results["base_model"]["responses"])
            },
            "detailed_results": results
        }
        
        # Sauvegarder en JSON
        report_file = f"evaluation_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Créer un CSV pour analyse
        csv_data = []
        for i in range(len(results["base_model"]["responses"])):
            csv_data.append({
                "question_id": i + 1,
                "base_response": results["base_model"]["responses"][i],
                "base_time": results["base_model"]["times"][i],
                "finetuned_response": results["finetuned_model"]["responses"][i],
                "finetuned_time": results["finetuned_model"]["times"][i]
            })
        
        df = pd.DataFrame(csv_data)
        csv_file = f"evaluation_results_{time.strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"✅ Rapport sauvegardé: {report_file}")
        print(f"✅ Données CSV: {csv_file}")
    
    def interactive_test(self):
        """Test interactif du modèle fine-tuné"""
        print("\n=== Test Interactif ===")
        print("💬 Posez vos questions bancaires (tapez 'quit' pour quitter)")
        print("🔄 Tapez 'compare' pour comparer avec le modèle de base")
        print("-" * 60)
        
        while True:
            user_input = input("\n👤 Vous: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                print("👋 Au revoir!")
                break
            
            if user_input.lower() == 'compare':
                question = input("Question à comparer: ").strip()
                if question:
                    print(f"\n🔵 Modèle de base:")
                    base_resp = self.generate_response(self.base_model, question)
                    print(base_resp)
                    
                    print(f"\n🟢 Modèle fine-tuné:")
                    ft_resp = self.generate_response(self.finetuned_model, question)
                    print(ft_resp)
                continue
            
            if user_input:
                print(f"\n🤖 Assistant bancaire:")
                response = self.generate_response(self.finetuned_model, user_input)
                print(response)

def main():
    """Fonction principale"""
    print("🧪 Test du modèle Llama 3.1 8B fine-tuné")
    print("=" * 50)
    
    # Vérifier CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA disponible: {torch.cuda.get_device_name()}")
    else:
        print("⚠️ CUDA non disponible, utilisation du CPU")
    
    # Initialiser le testeur
    tester = LlamaFineTunedTester()
    
    # Charger les modèles
    if not tester.load_models():
        print("❌ Impossible de charger les modèles")
        return
    
    # Menu principal
    while True:
        print("\n" + "=" * 50)
        print("OPTIONS DE TEST:")
        print("1. 🧪 Évaluation complète des connaissances bancaires")
        print("2. 💬 Test interactif")
        print("3. 📊 Benchmark de performance")
        print("4. 📋 Rapport d'évaluation")
        print("5. 🚪 Quitter")
        print("=" * 50)
        
        choice = input("Votre choix (1-5): ").strip()
        
        if choice == "1":
            results = tester.evaluate_banking_knowledge()
            tester.save_evaluation_report(results)
        
        elif choice == "2":
            tester.interactive_test()
        
        elif choice == "3":
            questions = [
                "Quels sont les frais de compte?",
                "Comment ouvrir un compte?",
                "Conditions pour un prêt?"
            ]
            tester.benchmark_performance(questions)
        
        elif choice == "4":
            # Évaluation rapide pour le rapport
            questions = [
                "Quels sont les frais de tenue de compte?",
                "Comment ouvrir un compte épargne?"
            ]
            results = tester.benchmark_performance(questions)
            tester.save_evaluation_report(results)
        
        elif choice == "5":
            print("👋 Au revoir!")
            break
        
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
