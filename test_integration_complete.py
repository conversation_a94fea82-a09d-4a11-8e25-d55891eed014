#!/usr/bin/env python3
"""
Script de test complet pour l'intégration du chatbot bancaire
Teste tous les services et fonctionnalités
"""

import requests
import json
import time
import sys
from pathlib import Path

class ChatbotIntegrationTester:
    def __init__(self):
        self.services = {
            'rag': 'http://localhost:8000',
            'speech': 'http://localhost:8004',
            'frontend': 'http://localhost:5173'
        }
        self.test_results = {}
    
    def print_header(self, title):
        """Affiche un en-tête de section"""
        print("\n" + "="*60)
        print(f"🧪 {title}")
        print("="*60)
    
    def print_test(self, test_name, success, details=""):
        """Affiche le résultat d'un test"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results[test_name] = {
            'success': success,
            'details': details
        }
    
    def test_service_health(self, service_name, url):
        """Teste la santé d'un service"""
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return True, data
            else:
                return False, f"HTTP {response.status_code}"
        except Exception as e:
            return False, str(e)
    
    def test_rag_service(self):
        """Teste le service RAG"""
        self.print_header("TEST SERVICE RAG")
        
        # Test de santé
        success, details = self.test_service_health('rag', self.services['rag'])
        self.print_test("RAG Health Check", success, str(details))
        
        if not success:
            return False
        
        # Test de question bancaire
        try:
            test_question = "Quels sont les frais de virement NUST ?"
            response = requests.post(
                f"{self.services['rag']}/chat",
                json={"message": test_question},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('response', '')
                self.print_test(
                    "RAG Question Test", 
                    len(answer) > 10,
                    f"Réponse: {answer[:100]}..."
                )
                return True
            else:
                self.print_test("RAG Question Test", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.print_test("RAG Question Test", False, str(e))
            return False
    
    def test_speech_service(self):
        """Teste le service Speech"""
        self.print_header("TEST SERVICE SPEECH")
        
        # Test de santé
        success, details = self.test_service_health('speech', self.services['speech'])
        self.print_test("Speech Health Check", success, str(details))
        
        if not success:
            return False
        
        # Test des endpoints
        try:
            # Test endpoint de test
            response = requests.get(f"{self.services['speech']}/speech/test", timeout=5)
            self.print_test(
                "Speech Test Endpoint", 
                response.status_code == 200,
                response.json().get('message', '') if response.status_code == 200 else f"HTTP {response.status_code}"
            )
            
            # Test TTS
            tts_data = {
                "text": "Bonjour, ceci est un test de synthèse vocale.",
                "voice_speed": 1.0
            }
            
            response = requests.post(
                f"{self.services['speech']}/speech/synthesize",
                json=tts_data,
                timeout=15
            )
            
            self.print_test(
                "Speech TTS Test",
                response.status_code == 200,
                f"Content-Type: {response.headers.get('content-type', 'unknown')}"
            )
            
            return True
            
        except Exception as e:
            self.print_test("Speech Service Test", False, str(e))
            return False
    
    def test_frontend_availability(self):
        """Teste la disponibilité du frontend"""
        self.print_header("TEST FRONTEND")
        
        try:
            response = requests.get(self.services['frontend'], timeout=5)
            success = response.status_code == 200
            self.print_test(
                "Frontend Availability",
                success,
                f"HTTP {response.status_code}" if not success else "Page accessible"
            )
            return success
            
        except Exception as e:
            self.print_test("Frontend Availability", False, str(e))
            return False
    
    def test_file_structure(self):
        """Teste la structure des fichiers"""
        self.print_header("TEST STRUCTURE FICHIERS")
        
        required_files = [
            'compatible_speech_service.py',
            'backend_rag_api.py',
            'start_professional_chatbot.py',
            'chat-bank-nexus-main(frontend v0)/src/hooks/use-speech.ts',
            'chat-bank-nexus-main(frontend v0)/src/components/VoiceControls.tsx',
            'chat-bank-nexus-main(frontend v0)/src/components/ChatHistory.tsx',
            'chat-bank-nexus-main(frontend v0)/src/components/QuickActions.tsx'
        ]
        
        for file_path in required_files:
            exists = Path(file_path).exists()
            self.print_test(f"Fichier: {file_path}", exists)
    
    def test_integration_flow(self):
        """Teste le flux d'intégration complet"""
        self.print_header("TEST FLUX INTÉGRATION")
        
        # Simuler un flux utilisateur complet
        try:
            # 1. Question au RAG
            question = "Quels sont les avantages du compte NUST Maximiser ?"
            rag_response = requests.post(
                f"{self.services['rag']}/chat",
                json={"message": question},
                timeout=30
            )
            
            rag_success = rag_response.status_code == 200
            self.print_test("Étape 1: Question RAG", rag_success)
            
            if rag_success:
                answer = rag_response.json().get('response', '')
                
                # 2. Synthèse de la réponse
                tts_response = requests.post(
                    f"{self.services['speech']}/speech/synthesize",
                    json={"text": answer[:200], "voice_speed": 1.0},
                    timeout=15
                )
                
                tts_success = tts_response.status_code == 200
                self.print_test("Étape 2: Synthèse TTS", tts_success)
                
                # 3. Test complet
                integration_success = rag_success and tts_success
                self.print_test(
                    "Flux Intégration Complet",
                    integration_success,
                    "RAG → TTS pipeline fonctionnel"
                )
                
                return integration_success
            
        except Exception as e:
            self.print_test("Flux Intégration", False, str(e))
            return False
    
    def generate_report(self):
        """Génère un rapport de test"""
        self.print_header("RAPPORT DE TEST")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 RÉSULTATS:")
        print(f"   Total: {total_tests} tests")
        print(f"   ✅ Réussis: {passed_tests}")
        print(f"   ❌ Échoués: {failed_tests}")
        print(f"   📈 Taux de réussite: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ TESTS ÉCHOUÉS:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"   • {test_name}: {result['details']}")
        
        print(f"\n🎯 STATUT GLOBAL: {'✅ TOUS LES SERVICES OPÉRATIONNELS' if failed_tests == 0 else '⚠️ PROBLÈMES DÉTECTÉS'}")
        
        return failed_tests == 0
    
    def run_all_tests(self):
        """Lance tous les tests"""
        print("🚀 DÉMARRAGE DES TESTS D'INTÉGRATION")
        print("🕐 Cela peut prendre quelques minutes...")
        
        # Tests de structure
        self.test_file_structure()
        
        # Tests de services
        rag_ok = self.test_rag_service()
        speech_ok = self.test_speech_service()
        frontend_ok = self.test_frontend_availability()
        
        # Test d'intégration si les services sont OK
        if rag_ok and speech_ok:
            self.test_integration_flow()
        else:
            self.print_test(
                "Flux Intégration", 
                False, 
                "Services requis non disponibles"
            )
        
        # Rapport final
        return self.generate_report()

def main():
    """Fonction principale"""
    print("🧪 TESTEUR D'INTÉGRATION CHATBOT BANCAIRE")
    print("=" * 60)
    
    tester = ChatbotIntegrationTester()
    
    # Vérifier que les services sont démarrés
    print("⚠️  IMPORTANT: Assurez-vous que tous les services sont démarrés:")
    print("   1. python compatible_speech_service.py")
    print("   2. python rag_developed_responses.py")
    print("   3. npm run dev (dans le dossier frontend)")
    print("\n⏳ Démarrage des tests dans 3 secondes...")
    
    time.sleep(3)
    
    # Lancer les tests
    success = tester.run_all_tests()
    
    # Code de sortie
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
