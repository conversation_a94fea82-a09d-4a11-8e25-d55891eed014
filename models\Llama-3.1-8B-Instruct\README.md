---
language:
- en
- de
- fr
- it
- pt
- hi
- es
- th
license: llama3.1
base_model: meta-llama/Meta-Llama-3.1-8B
pipeline_tag: text-generation
tags:
- facebook
- meta
- pytorch
- llama
- llama-3
extra_gated_prompt: "### LLAMA 3.1 COMMUNITY LICENSE AGREEMENT\nLlama 3.1 Version\
  \ Release Date: July 23, 2024\n\"Agreement\" means the terms and conditions for\
  \ use, reproduction, distribution and modification of the  Llama Materials set forth\
  \ herein.\n\"Documentation\" means the specifications, manuals and documentation\
  \ accompanying Llama 3.1 distributed by Meta at https://llama.meta.com/doc/overview.\n\
  \"Licensee\" or \"you\" means you, or your employer or any other person or entity\
  \ (if you are entering into this Agreement on such person or entity’s behalf), of\
  \ the age required under applicable laws, rules or regulations to provide legal\
  \ consent and that has legal authority to bind your employer or such other person\
  \ or entity if you are entering in this Agreement on their behalf.\n\"Llama 3.1\"\
  \ means the foundational large language models and software and algorithms, including\
  \ machine-learning model code, trained model weights, inference-enabling code, training-enabling\
  \ code, fine-tuning enabling code and other elements of the foregoing distributed\
  \ by Meta at https://llama.meta.com/llama-downloads.\n\"Llama Materials\" means,\
  \ collectively, Meta’s proprietary Llama 3.1 and Documentation (and any portion\
  \ thereof) made available under this Agreement.\n\"Meta\" or \"we\" means Meta Platforms\
  \ Ireland Limited (if you are located in or, if you are an entity, your principal\
  \ place of business is in the EEA or Switzerland) and Meta Platforms, Inc. (if you\
  \ are located outside of the EEA or Switzerland).\n   \n1. License Rights and Redistribution.\n\
  a. Grant of Rights. You are granted a non-exclusive, worldwide, non-transferable\
  \ and royalty-free limited license under Meta’s intellectual property or other rights\
  \ owned by Meta embodied in the Llama Materials to use, reproduce, distribute, copy,\
  \ create derivative works of, and make modifications to the Llama Materials.\nb.\
  \ Redistribution and Use.\ni. If you distribute or make available the Llama Materials\
  \ (or any derivative works thereof), or a product or service (including another\
  \ AI model) that contains any of them, you shall (A) provide a copy of this Agreement\
  \ with any such Llama Materials; and (B) prominently display “Built with Llama”\
  \ on a related website, user interface, blogpost, about page, or product documentation.\
  \ If you use the Llama Materials or any outputs or results of the Llama Materials\
  \ to create, train, fine tune, or otherwise improve an AI model, which is distributed\
  \ or made available, you shall also include “Llama” at the beginning of any such\
  \ AI model name.\nii. If you receive Llama Materials, or any derivative works thereof,\
  \ from a Licensee as part  of an integrated end user product, then Section 2 of\
  \ this Agreement will not apply to you.\niii. You must retain in all copies of the\
  \ Llama Materials that you distribute the following attribution notice within a\
  \ “Notice” text file distributed as a part of such copies: “Llama 3.1 is licensed\
  \ under the Llama 3.1 Community License, Copyright © Meta Platforms, Inc. All Rights\
  \ Reserved.”\niv. Your use of the Llama Materials must comply with applicable laws\
  \ and regulations (including trade compliance laws and regulations) and adhere to\
  \ the Acceptable Use Policy for the Llama Materials (available at https://llama.meta.com/llama3_1/use-policy),\
  \ which is hereby incorporated by reference into this Agreement.\n2. Additional\
  \ Commercial Terms. If, on the Llama 3.1 version release date, the monthly active\
  \ users of the products or services made available by or for Licensee, or Licensee’s\
  \ affiliates, is greater than 700 million monthly active users in the preceding\
  \ calendar month, you must request a license from Meta, which Meta may grant to\
  \ you in its sole discretion, and you are not authorized to exercise any of the\
  \ rights under this Agreement unless or until Meta otherwise expressly grants you\
  \ such rights.\n3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE LAW, THE\
  \ LLAMA MATERIALS AND ANY OUTPUT AND RESULTS THEREFROM ARE PROVIDED ON AN “AS IS”\
  \ BASIS, WITHOUT WARRANTIES OF ANY KIND, AND META DISCLAIMS ALL WARRANTIES OF ANY\
  \ KIND, BOTH EXPRESS AND IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES\
  \ OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE.\
  \ YOU ARE SOLELY RESPONSIBLE FOR DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING\
  \ THE LLAMA MATERIALS AND ASSUME ANY RISKS ASSOCIATED WITH YOUR USE OF THE LLAMA\
  \ MATERIALS AND ANY OUTPUT AND RESULTS.\n4. Limitation of Liability. IN NO EVENT\
  \ WILL META OR ITS AFFILIATES BE LIABLE UNDER ANY THEORY OF LIABILITY, WHETHER IN\
  \ CONTRACT, TORT, NEGLIGENCE, PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS\
  \ AGREEMENT, FOR ANY LOST PROFITS OR ANY INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL,\
  \ EXEMPLARY OR PUNITIVE DAMAGES, EVEN IF META OR ITS AFFILIATES HAVE BEEN ADVISED\
  \ OF THE POSSIBILITY OF ANY OF THE FOREGOING.\n5. Intellectual Property.\na. No\
  \ trademark licenses are granted under this Agreement, and in connection with the\
  \ Llama Materials, neither Meta nor Licensee may use any name or mark owned by or\
  \ associated with the other or any of its affiliates, except as required for reasonable\
  \ and customary use in describing and redistributing the Llama Materials or as set\
  \ forth in this Section 5(a). Meta hereby grants you a license to use “Llama” (the\
  \ “Mark”) solely as required to comply with the last sentence of Section 1.b.i.\
  \ You will comply with Meta’s brand guidelines (currently accessible at https://about.meta.com/brand/resources/meta/company-brand/\
  \ ). All goodwill arising out of your use of the Mark will inure to the benefit\
  \ of Meta.\nb. Subject to Meta’s ownership of Llama Materials and derivatives made\
  \ by or for Meta, with respect to any derivative works and modifications of the\
  \ Llama Materials that are made by you, as between you and Meta, you are and will\
  \ be the owner of such derivative works and modifications.\nc. If you institute\
  \ litigation or other proceedings against Meta or any entity (including a cross-claim\
  \ or counterclaim in a lawsuit) alleging that the Llama Materials or Llama 3.1 outputs\
  \ or results, or any portion of any of the foregoing, constitutes infringement of\
  \ intellectual property or other rights owned or licensable by you, then any licenses\
  \ granted to you under this Agreement shall terminate as of the date such litigation\
  \ or claim is filed or instituted. You will indemnify and hold harmless Meta from\
  \ and against any claim by any third party arising out of or related to your use\
  \ or distribution of the Llama Materials.\n6. Term and Termination. The term of\
  \ this Agreement will commence upon your acceptance of this Agreement or access\
  \ to the Llama Materials and will continue in full force and effect until terminated\
  \ in accordance with the terms and conditions herein. Meta may terminate this Agreement\
  \ if you are in breach of any term or condition of this Agreement. Upon termination\
  \ of this Agreement, you shall delete and cease use of the Llama Materials. Sections\
  \ 3, 4 and 7 shall survive the termination of this Agreement.\n7. Governing Law\
  \ and Jurisdiction. This Agreement will be governed and construed under the laws\
  \ of the State of California without regard to choice of law principles, and the\
  \ UN Convention on Contracts for the International Sale of Goods does not apply\
  \ to this Agreement. The courts of California shall have exclusive jurisdiction\
  \ of any dispute arising out of this Agreement.\n### Llama 3.1 Acceptable Use Policy\n\
  Meta is committed to promoting safe and fair use of its tools and features, including\
  \ Llama 3.1. If you access or use Llama 3.1, you agree to this Acceptable Use Policy\
  \ (“Policy”). The most recent copy of this policy can be found at [https://llama.meta.com/llama3_1/use-policy](https://llama.meta.com/llama3_1/use-policy)\n\
  #### Prohibited Uses\nWe want everyone to use Llama 3.1 safely and responsibly.\
  \ You agree you will not use, or allow others to use, Llama 3.1 to:\n 1. Violate\
  \ the law or others’ rights, including to:\n    1. Engage in, promote, generate,\
  \ contribute to, encourage, plan, incite, or further illegal or unlawful activity\
  \ or content, such as:\n        1. Violence or terrorism\n        2. Exploitation\
  \ or harm to children, including the solicitation, creation, acquisition, or dissemination\
  \ of child exploitative content or failure to report Child Sexual Abuse Material\n\
  \        3. Human trafficking, exploitation, and sexual violence\n        4. The\
  \ illegal distribution of information or materials to minors, including obscene\
  \ materials, or failure to employ legally required age-gating in connection with\
  \ such information or materials.\n        5. Sexual solicitation\n        6. Any\
  \ other criminal activity\n    3. Engage in, promote, incite, or facilitate the\
  \ harassment, abuse, threatening, or bullying of individuals or groups of individuals\n\
  \    4. Engage in, promote, incite, or facilitate discrimination or other unlawful\
  \ or harmful conduct in the provision of employment, employment benefits, credit,\
  \ housing, other economic benefits, or other essential goods and services\n    5.\
  \ Engage in the unauthorized or unlicensed practice of any profession including,\
  \ but not limited to, financial, legal, medical/health, or related professional\
  \ practices\n    6. Collect, process, disclose, generate, or infer health, demographic,\
  \ or other sensitive personal or private information about individuals without rights\
  \ and consents required by applicable laws\n    7. Engage in or facilitate any action\
  \ or generate any content that infringes, misappropriates, or otherwise violates\
  \ any third-party rights, including the outputs or results of any products or services\
  \ using the Llama Materials\n    8. Create, generate, or facilitate the creation\
  \ of malicious code, malware, computer viruses or do anything else that could disable,\
  \ overburden, interfere with or impair the proper working, integrity, operation\
  \ or appearance of a website or computer system\n2. Engage in, promote, incite,\
  \ facilitate, or assist in the planning or development of activities that present\
  \ a risk of death or bodily harm to individuals, including use of Llama 3.1 related\
  \ to the following:\n    1. Military, warfare, nuclear industries or applications,\
  \ espionage, use for materials or activities that are subject to the International\
  \ Traffic Arms Regulations (ITAR) maintained by the United States Department of\
  \ State\n    2. Guns and illegal weapons (including weapon development)\n    3.\
  \ Illegal drugs and regulated/controlled substances\n    4. Operation of critical\
  \ infrastructure, transportation technologies, or heavy machinery\n    5. Self-harm\
  \ or harm to others, including suicide, cutting, and eating disorders\n    6. Any\
  \ content intended to incite or promote violence, abuse, or any infliction of bodily\
  \ harm to an individual\n3. Intentionally deceive or mislead others, including use\
  \ of Llama 3.1 related to the following:\n    1. Generating, promoting, or furthering\
  \ fraud or the creation or promotion of disinformation\n    2. Generating, promoting,\
  \ or furthering defamatory content, including the creation of defamatory statements,\
  \ images, or other content\n    3. Generating, promoting, or further distributing\
  \ spam\n    4. Impersonating another individual without consent, authorization,\
  \ or legal right\n    5. Representing that the use of Llama 3.1 or outputs are human-generated\n\
  \    6. Generating or facilitating false online engagement, including fake reviews\
  \ and other means of fake online engagement\n4. Fail to appropriately disclose to\
  \ end users any known dangers of your AI system\nPlease report any violation of\
  \ this Policy, software “bug,” or other problems that could lead to a violation\
  \ of this Policy through one of the following means:\n    * Reporting issues with\
  \ the model: [https://github.com/meta-llama/llama-models/issues](https://github.com/meta-llama/llama-models/issues)\n\
  \    * Reporting risky content generated by the model:\n    developers.facebook.com/llama_output_feedback\n\
  \    * Reporting bugs and security concerns: facebook.com/whitehat/info\n    * Reporting\
  \ violations of the Acceptable Use Policy or unlicensed uses of Meta Llama 3: <EMAIL>"
extra_gated_fields:
  First Name: text
  Last Name: text
  Date of birth: date_picker
  Country: country
  Affiliation: text
  Job title:
    type: select
    options:
    - Student
    - Research Graduate
    - AI researcher
    - AI developer/engineer
    - Reporter
    - Other
  geo: ip_location
  ? By clicking Submit below I accept the terms of the license and acknowledge that
    the information I provide will be collected stored processed and shared in accordance
    with the Meta Privacy Policy
  : checkbox
extra_gated_description: The information you provide will be collected, stored, processed
  and shared in accordance with the [Meta Privacy Policy](https://www.facebook.com/privacy/policy/).
extra_gated_button_content: Submit
---

## Model Information

The Meta Llama 3.1 collection of multilingual large language models (LLMs) is a collection of pretrained and instruction tuned generative models in 8B, 70B and 405B sizes (text in/text out). The Llama 3.1 instruction tuned text only models (8B, 70B, 405B) are optimized for multilingual dialogue use cases and outperform many of the available open source and closed chat models on common industry benchmarks.

**Model developer**: Meta

**Model Architecture:** Llama 3.1 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety. 


<table>
  <tr>
   <td>
   </td>
   <td><strong>Training Data</strong>
   </td>
   <td><strong>Params</strong>
   </td>
   <td><strong>Input modalities</strong>
   </td>
   <td><strong>Output modalities</strong>
   </td>
   <td><strong>Context length</strong>
   </td>
   <td><strong>GQA</strong>
   </td>
   <td><strong>Token count</strong>
   </td>
   <td><strong>Knowledge cutoff</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="3" >Llama 3.1 (text only)
   </td>
   <td rowspan="3" >A new mix of publicly available online data.
   </td>
   <td>8B
   </td>
   <td>Multilingual Text
   </td>
   <td>Multilingual Text and code
   </td>
   <td>128k
   </td>
   <td>Yes
   </td>
   <td rowspan="3" >15T+
   </td>
   <td rowspan="3" >December 2023
   </td>
  </tr>
  <tr>
   <td>70B
   </td>
   <td>Multilingual Text
   </td>
   <td>Multilingual Text and code
   </td>
   <td>128k
   </td>
   <td>Yes
   </td>
  </tr>
  <tr>
   <td>405B
   </td>
   <td>Multilingual Text
   </td>
   <td>Multilingual Text and code
   </td>
   <td>128k
   </td>
   <td>Yes
   </td>
  </tr>
</table>


**Supported languages:** English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.

**Llama 3.1 family of models**. Token counts refer to pretraining data only. All model versions use Grouped-Query Attention (GQA) for improved inference scalability.

**Model Release Date:** July 23, 2024.

**Status:** This is a static model trained on an offline dataset. Future versions of the tuned models will be released as we improve model safety with community feedback.

**License:** A custom commercial license, the Llama 3.1 Community License, is available at: [https://github.com/meta-llama/llama-models/blob/main/models/llama3_1/LICENSE](https://github.com/meta-llama/llama-models/blob/main/models/llama3_1/LICENSE)

Where to send questions or comments about the model Instructions on how to provide feedback or comments on the model can be found in the model [README](https://github.com/meta-llama/llama3). For more technical information about generation parameters and recipes for how to use Llama 3.1 in applications, please go [here](https://github.com/meta-llama/llama-recipes). 


## Intended Use

**Intended Use Cases** Llama 3.1 is intended for commercial and research use in multiple languages. Instruction tuned text only models are intended for assistant-like chat, whereas pretrained models can be adapted for a variety of natural language generation tasks. The Llama 3.1 model collection also supports the ability to leverage the outputs of its models to improve other models including synthetic data generation and distillation. The Llama 3.1 Community License allows for these use cases. 

**Out-of-scope** Use in any manner that violates applicable laws or regulations (including trade compliance laws). Use in any other way that is prohibited by the Acceptable Use Policy and Llama 3.1 Community License. Use in languages beyond those explicitly referenced as supported in this model card**.

**<span style="text-decoration:underline;">Note</span>: Llama 3.1 has been trained on a broader collection of languages than the 8 supported languages. Developers may fine-tune Llama 3.1 models for languages beyond the 8 supported languages provided they comply with the Llama 3.1 Community License and the Acceptable Use Policy and in such cases are responsible for ensuring that any uses of Llama 3.1 in additional languages is done in a safe and responsible manner.

## How to use

This repository contains two versions of Meta-Llama-3.1-8B-Instruct, for use with transformers and with the original `llama` codebase.

### Use with transformers

Starting with `transformers >= 4.43.0` onward, you can run conversational inference using the Transformers `pipeline` abstraction or by leveraging the Auto classes with the `generate()` function.

Make sure to update your transformers installation via `pip install --upgrade transformers`.

```python
import transformers
import torch

model_id = "meta-llama/Meta-Llama-3.1-8B-Instruct"

pipeline = transformers.pipeline(
    "text-generation",
    model=model_id,
    model_kwargs={"torch_dtype": torch.bfloat16},
    device_map="auto",
)

messages = [
    {"role": "system", "content": "You are a pirate chatbot who always responds in pirate speak!"},
    {"role": "user", "content": "Who are you?"},
]

outputs = pipeline(
    messages,
    max_new_tokens=256,
)
print(outputs[0]["generated_text"][-1])
```

Note: You can also find detailed recipes on how to use the model locally, with `torch.compile()`, assisted generations, quantised and more at [`huggingface-llama-recipes`](https://github.com/huggingface/huggingface-llama-recipes)

### Tool use with transformers

LLaMA-3.1 supports multiple tool use formats. You can see a full guide to prompt formatting [here](https://llama.meta.com/docs/model-cards-and-prompt-formats/llama3_1/).

Tool use is also supported through [chat templates](https://huggingface.co/docs/transformers/main/chat_templating#advanced-tool-use--function-calling) in Transformers. 
Here is a quick example showing a single simple tool:

```python
# First, define a tool
def get_current_temperature(location: str) -> float:
    """
    Get the current temperature at a location.
    
    Args:
        location: The location to get the temperature for, in the format "City, Country"
    Returns:
        The current temperature at the specified location in the specified units, as a float.
    """
    return 22.  # A real function should probably actually get the temperature!

# Next, create a chat and apply the chat template
messages = [
  {"role": "system", "content": "You are a bot that responds to weather queries."},
  {"role": "user", "content": "Hey, what's the temperature in Paris right now?"}
]

inputs = tokenizer.apply_chat_template(messages, tools=[get_current_temperature], add_generation_prompt=True)
```

You can then generate text from this input as normal. If the model generates a tool call, you should add it to the chat like so:

```python
tool_call = {"name": "get_current_temperature", "arguments": {"location": "Paris, France"}}
messages.append({"role": "assistant", "tool_calls": [{"type": "function", "function": tool_call}]})
```

and then call the tool and append the result, with the `tool` role, like so:

```python
messages.append({"role": "tool", "name": "get_current_temperature", "content": "22.0"})
```

After that, you can `generate()` again to let the model use the tool result in the chat. Note that this was a very brief introduction to tool calling - for more information,
see the [LLaMA prompt format docs](https://llama.meta.com/docs/model-cards-and-prompt-formats/llama3_1/) and the Transformers [tool use documentation](https://huggingface.co/docs/transformers/main/chat_templating#advanced-tool-use--function-calling).


### Use with `llama`

Please, follow the instructions in the [repository](https://github.com/meta-llama/llama)

To download Original checkpoints, see the example command below leveraging `huggingface-cli`:

```
huggingface-cli download meta-llama/Meta-Llama-3.1-8B-Instruct --include "original/*" --local-dir Meta-Llama-3.1-8B-Instruct
```

## Hardware and Software

**Training Factors** We used custom training libraries, Meta's custom built GPU cluster, and production infrastructure for pretraining. Fine-tuning, annotation, and evaluation were also performed on production infrastructure.

**Training utilized a cumulative of** 39.3M GPU hours of computation on H100-80GB (TDP of 700W) type hardware, per the table below. Training time is the total GPU time required for training each model and power consumption is the peak power capacity per GPU device used, adjusted for power usage efficiency. 


**Training Greenhouse Gas Emissions** Estimated total location-based greenhouse gas emissions were **11,390** tons CO2eq for training. Since 2020, Meta has maintained net zero greenhouse gas emissions in its global operations and matched 100% of its electricity use with renewable energy, therefore the total market-based greenhouse gas emissions for training were 0 tons CO2eq.


<table>
  <tr>
   <td>
   </td>
   <td><strong>Training Time (GPU hours)</strong>
   </td>
   <td><strong>Training Power Consumption (W)</strong>
   </td>
   <td><strong>Training Location-Based Greenhouse Gas Emissions</strong>
<p>
<strong>(tons CO2eq)</strong>
   </td>
   <td><strong>Training Market-Based Greenhouse Gas Emissions</strong>
<p>
<strong>(tons CO2eq)</strong>
   </td>
  </tr>
  <tr>
   <td>Llama 3.1 8B
   </td>
   <td>1.46M
   </td>
   <td>700
   </td>
   <td>420
   </td>
   <td>0
   </td>
  </tr>
  <tr>
   <td>Llama 3.1 70B
   </td>
   <td>7.0M
   </td>
   <td>700
   </td>
   <td>2,040
   </td>
   <td>0
   </td>
  </tr>
  <tr>
   <td>Llama 3.1 405B
   </td>
   <td>30.84M
   </td>
   <td>700
   </td>
   <td>8,930
   </td>
   <td>0
   </td>
  </tr>
  <tr>
   <td>Total
   </td>
   <td>39.3M
   <td>
<ul>

</ul>
   </td>
   <td>11,390
   </td>
   <td>0
   </td>
  </tr>
</table>



The methodology used to determine training energy use and greenhouse gas emissions can be found [here](https://arxiv.org/pdf/2204.05149).  Since Meta is openly releasing these models, the training energy use and greenhouse gas emissions  will not be incurred by others.


## Training Data

**Overview:** Llama 3.1 was pretrained on ~15 trillion tokens of data from publicly available sources. The fine-tuning data includes publicly available instruction datasets, as well as over 25M synthetically generated examples. 

**Data Freshness:** The pretraining data has a cutoff of December 2023.


## Benchmark scores

In this section, we report the results for Llama 3.1 models on standard automatic benchmarks. For all the evaluations, we use our internal evaluations library. 

### Base pretrained models


<table>
  <tr>
   <td><strong>Category</strong>
   </td>
   <td><strong>Benchmark</strong>
   </td>
   <td><strong># Shots</strong>
   </td>
   <td><strong>Metric</strong>
   </td>
   <td><strong>Llama 3 8B</strong>
   </td>
   <td><strong>Llama 3.1 8B</strong>
   </td>
   <td><strong>Llama 3 70B</strong>
   </td>
   <td><strong>Llama 3.1 70B</strong>
   </td>
   <td><strong>Llama 3.1 405B</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="7" >General
   </td>
   <td>MMLU
   </td>
   <td>5
   </td>
   <td>macro_avg/acc_char
   </td>
   <td>66.7
   </td>
   <td>66.7
   </td>
   <td>79.5
   </td>
   <td>79.3
   </td>
   <td>85.2
   </td>
  </tr>
  <tr>
   <td>MMLU-Pro (CoT)
   </td>
   <td>5
   </td>
   <td>macro_avg/acc_char
   </td>
   <td>36.2
   </td>
   <td>37.1
   </td>
   <td>55.0
   </td>
   <td>53.8
   </td>
   <td>61.6
   </td>
  </tr>
  <tr>
   <td>AGIEval English
   </td>
   <td>3-5
   </td>
   <td>average/acc_char
   </td>
   <td>47.1
   </td>
   <td>47.8
   </td>
   <td>63.0
   </td>
   <td>64.6
   </td>
   <td>71.6
   </td>
  </tr>
  <tr>
   <td>CommonSenseQA
   </td>
   <td>7
   </td>
   <td>acc_char
   </td>
   <td>72.6
   </td>
   <td>75.0
   </td>
   <td>83.8
   </td>
   <td>84.1
   </td>
   <td>85.8
   </td>
  </tr>
  <tr>
   <td>Winogrande
   </td>
   <td>5
   </td>
   <td>acc_char
   </td>
   <td>-
   </td>
   <td>60.5
   </td>
   <td>-
   </td>
   <td>83.3
   </td>
   <td>86.7
   </td>
  </tr>
  <tr>
   <td>BIG-Bench Hard (CoT)
   </td>
   <td>3
   </td>
   <td>average/em
   </td>
   <td>61.1
   </td>
   <td>64.2
   </td>
   <td>81.3
   </td>
   <td>81.6
   </td>
   <td>85.9
   </td>
  </tr>
  <tr>
   <td>ARC-Challenge
   </td>
   <td>25
   </td>
   <td>acc_char
   </td>
   <td>79.4
   </td>
   <td>79.7
   </td>
   <td>93.1
   </td>
   <td>92.9
   </td>
   <td>96.1
   </td>
  </tr>
  <tr>
   <td>Knowledge reasoning
   </td>
   <td>TriviaQA-Wiki
   </td>
   <td>5
   </td>
   <td>em
   </td>
   <td>78.5
   </td>
   <td>77.6
   </td>
   <td>89.7
   </td>
   <td>89.8
   </td>
   <td>91.8
   </td>
  </tr>
  <tr>
   <td rowspan="4" >Reading comprehension
   </td>
   <td>SQuAD
   </td>
   <td>1
   </td>
   <td>em
   </td>
   <td>76.4
   </td>
   <td>77.0
   </td>
   <td>85.6
   </td>
   <td>81.8
   </td>
   <td>89.3
   </td>
  </tr>
  <tr>
   <td>QuAC (F1)
   </td>
   <td>1
   </td>
   <td>f1
   </td>
   <td>44.4
   </td>
   <td>44.9
   </td>
   <td>51.1
   </td>
   <td>51.1
   </td>
   <td>53.6
   </td>
  </tr>
  <tr>
   <td>BoolQ
   </td>
   <td>0
   </td>
   <td>acc_char
   </td>
   <td>75.7
   </td>
   <td>75.0
   </td>
   <td>79.0
   </td>
   <td>79.4
   </td>
   <td>80.0
   </td>
  </tr>
  <tr>
   <td>DROP (F1)
   </td>
   <td>3
   </td>
   <td>f1
   </td>
   <td>58.4
   </td>
   <td>59.5
   </td>
   <td>79.7
   </td>
   <td>79.6
   </td>
   <td>84.8
   </td>
  </tr>
</table>



### Instruction tuned models


<table>
  <tr>
   <td><strong>Category</strong>
   </td>
   <td><strong>Benchmark</strong>
   </td>
   <td><strong># Shots</strong>
   </td>
   <td><strong>Metric</strong>
   </td>
   <td><strong>Llama 3 8B Instruct</strong>
   </td>
   <td><strong>Llama 3.1 8B Instruct</strong>
   </td>
   <td><strong>Llama 3 70B Instruct</strong>
   </td>
   <td><strong>Llama 3.1 70B Instruct</strong>
   </td>
   <td><strong>Llama 3.1 405B Instruct</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="4" >General
   </td>
   <td>MMLU
   </td>
   <td>5
   </td>
   <td>macro_avg/acc
   </td>
   <td>68.5
   </td>
   <td>69.4
   </td>
   <td>82.0
   </td>
   <td>83.6
   </td>
   <td>87.3
   </td>
  </tr>
  <tr>
   <td>MMLU (CoT)
   </td>
   <td>0
   </td>
   <td>macro_avg/acc
   </td>
   <td>65.3
   </td>
   <td>73.0
   </td>
   <td>80.9
   </td>
   <td>86.0
   </td>
   <td>88.6
   </td>
  </tr>
  <tr>
   <td>MMLU-Pro (CoT)
   </td>
   <td>5
   </td>
   <td>micro_avg/acc_char
   </td>
   <td>45.5
   </td>
   <td>48.3
   </td>
   <td>63.4
   </td>
   <td>66.4
   </td>
   <td>73.3
   </td>
  </tr>
  <tr>
   <td>IFEval
   </td>
   <td>
   </td>
   <td>
   </td>
   <td>76.8
   </td>
   <td>80.4
   </td>
   <td>82.9
   </td>
   <td>87.5
   </td>
   <td>88.6
   </td>
  </tr>
  <tr>
   <td rowspan="2" >Reasoning
   </td>
   <td>ARC-C
   </td>
   <td>0
   </td>
   <td>acc
   </td>
   <td>82.4
   </td>
   <td>83.4
   </td>
   <td>94.4
   </td>
   <td>94.8
   </td>
   <td>96.9
   </td>
  </tr>
  <tr>
   <td>GPQA
   </td>
   <td>0
   </td>
   <td>em
   </td>
   <td>34.6
   </td>
   <td>30.4
   </td>
   <td>39.5
   </td>
   <td>46.7
   </td>
   <td>50.7
   </td>
  </tr>
  <tr>
   <td rowspan="4" >Code
   </td>
   <td>HumanEval
   </td>
   <td>0
   </td>
   <td>pass@1
   </td>
   <td>60.4
   </td>
   <td>72.6
   </td>
   <td>81.7
   </td>
   <td>80.5
   </td>
   <td>89.0
   </td>
  </tr>
  <tr>
   <td>MBPP ++ base version
   </td>
   <td>0
   </td>
   <td>pass@1
   </td>
   <td>70.6
   </td>
   <td>72.8
   </td>
   <td>82.5
   </td>
   <td>86.0
   </td>
   <td>88.6
   </td>
  </tr>
  <tr>
   <td>Multipl-E HumanEval
   </td>
   <td>0
   </td>
   <td>pass@1
   </td>
   <td>-
   </td>
   <td>50.8
   </td>
   <td>-
   </td>
   <td>65.5
   </td>
   <td>75.2
   </td>
  </tr>
  <tr>
   <td>Multipl-E MBPP
   </td>
   <td>0
   </td>
   <td>pass@1
   </td>
   <td>-
   </td>
   <td>52.4
   </td>
   <td>-
   </td>
   <td>62.0
   </td>
   <td>65.7
   </td>
  </tr>
  <tr>
   <td rowspan="2" >Math
   </td>
   <td>GSM-8K (CoT)
   </td>
   <td>8
   </td>
   <td>em_maj1@1
   </td>
   <td>80.6
   </td>
   <td>84.5
   </td>
   <td>93.0
   </td>
   <td>95.1
   </td>
   <td>96.8
   </td>
  </tr>
  <tr>
   <td>MATH (CoT)
   </td>
   <td>0
   </td>
   <td>final_em
   </td>
   <td>29.1
   </td>
   <td>51.9
   </td>
   <td>51.0
   </td>
   <td>68.0
   </td>
   <td>73.8
   </td>
  </tr>
  <tr>
   <td rowspan="4" >Tool Use
   </td>
   <td>API-Bank
   </td>
   <td>0
   </td>
   <td>acc
   </td>
   <td>48.3
   </td>
   <td>82.6
   </td>
   <td>85.1
   </td>
   <td>90.0
   </td>
   <td>92.0
   </td>
  </tr>
  <tr>
   <td>BFCL
   </td>
   <td>0
   </td>
   <td>acc
   </td>
   <td>60.3
   </td>
   <td>76.1
   </td>
   <td>83.0
   </td>
   <td>84.8
   </td>
   <td>88.5
   </td>
  </tr>
  <tr>
   <td>Gorilla Benchmark API Bench
   </td>
   <td>0
   </td>
   <td>acc
   </td>
   <td>1.7
   </td>
   <td>8.2
   </td>
   <td>14.7
   </td>
   <td>29.7
   </td>
   <td>35.3
   </td>
  </tr>
  <tr>
   <td>Nexus (0-shot)
   </td>
   <td>0
   </td>
   <td>macro_avg/acc
   </td>
   <td>18.1
   </td>
   <td>38.5
   </td>
   <td>47.8
   </td>
   <td>56.7
   </td>
   <td>58.7
   </td>
  </tr>
  <tr>
   <td>Multilingual
   </td>
   <td>Multilingual MGSM (CoT)
   </td>
   <td>0
   </td>
   <td>em
   </td>
   <td>-
   </td>
   <td>68.9
   </td>
   <td>-
   </td>
   <td>86.9
   </td>
   <td>91.6
   </td>
  </tr>
</table>

#### Multilingual benchmarks

<table>
  <tr>
   <td><strong>Category</strong>
   </td>
   <td><strong>Benchmark</strong>
   </td>
   <td><strong>Language</strong>
   </td>
   <td><strong>Llama 3.1 8B</strong>
   </td>
   <td><strong>Llama 3.1 70B</strong>
   </td>
   <td><strong>Llama 3.1 405B</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="9" ><strong>General</strong>
   </td>
   <td rowspan="9" ><strong>MMLU (5-shot, macro_avg/acc)</strong>
   </td>
   <td>Portuguese
   </td>
   <td>62.12
   </td>
   <td>80.13
   </td>
   <td>84.95
   </td>
  </tr>
  <tr>
   <td>Spanish
   </td>
   <td>62.45
   </td>
   <td>80.05
   </td>
   <td>85.08
   </td>
  </tr>
  <tr>
   <td>Italian
   </td>
   <td>61.63
   </td>
   <td>80.4
   </td>
   <td>85.04
   </td>
  </tr>
  <tr>
   <td>German
   </td>
   <td>60.59
   </td>
   <td>79.27
   </td>
   <td>84.36
   </td>
  </tr>
  <tr>
   <td>French
   </td>
   <td>62.34
   </td>
   <td>79.82
   </td>
   <td>84.66
   </td>
  </tr>
  <tr>
   <td>Hindi
   </td>
   <td>50.88
   </td>
   <td>74.52
   </td>
   <td>80.31
   </td>
  </tr>
  <tr>
   <td>Thai
   </td>
   <td>50.32
   </td>
   <td>72.95
   </td>
   <td>78.21
   </td>
  </tr>
</table>



## Responsibility & Safety

As part of our Responsible release approach, we followed a three-pronged strategy to managing trust & safety risks:



* Enable developers to deploy helpful, safe and flexible experiences for their target audience and for the use cases supported by Llama. 
* Protect developers against adversarial users aiming to exploit Llama capabilities to potentially cause harm.
* Provide protections for the community to help prevent the misuse of our models.


### Responsible deployment 

Llama is a foundational technology designed to be used in a variety of use cases, examples on how Meta’s Llama models have been responsibly deployed can be found in our [Community Stories webpage](https://llama.meta.com/community-stories/). Our approach is to build the most helpful models enabling the world to benefit from the technology power, by aligning our model safety for the generic use cases addressing a standard set of harms. Developers are then in the driver seat to tailor safety for their use case, defining their own policy and deploying the models with the necessary safeguards in their Llama systems. Llama 3.1 was developed following the best practices outlined in our Responsible Use Guide, you can refer to the [Responsible Use Guide](https://llama.meta.com/responsible-use-guide/) to learn more. 


#### Llama 3.1 instruct 

Our main objectives for conducting safety fine-tuning are to provide the research community with a valuable resource for studying the robustness of safety fine-tuning, as well as to offer developers a readily available, safe, and powerful model for various applications to reduce the developer workload to deploy safe AI systems. For more details on the safety mitigations implemented please read the Llama 3 paper. 

**Fine-tuning data**

We employ a multi-faceted approach to data collection, combining human-generated data from our vendors with synthetic data to mitigate potential safety risks. We’ve developed many large language model (LLM)-based classifiers that enable us to thoughtfully select high-quality prompts and responses, enhancing data quality control. 

**Refusals and Tone**

Building on the work we started with Llama 3, we put a great emphasis on model refusals to benign prompts as well as refusal tone. We included both borderline and adversarial prompts in our safety data strategy, and modified our safety data responses to follow  tone guidelines. 


#### Llama 3.1 systems

**Large language models, including Llama 3.1, are not designed to be deployed in isolation but instead should be deployed as part of an overall AI system with additional safety guardrails as required.** Developers are expected to deploy system safeguards when building agentic systems. Safeguards are key to achieve the right helpfulness-safety alignment as well as mitigating safety and security risks inherent to the system and any integration of the model or system with external tools. 

As part of our responsible release approach, we provide the community with [safeguards](https://llama.meta.com/trust-and-safety/) that developers should deploy with Llama models or other LLMs, including Llama Guard 3, Prompt Guard and Code Shield. All our [reference implementations](https://github.com/meta-llama/llama-agentic-system) demos contain these safeguards by default so developers can benefit from system-level safety out-of-the-box. 


#### New capabilities 

Note that this release introduces new capabilities, including a longer context window, multilingual inputs and outputs and possible integrations by developers with third party tools. Building with these new capabilities requires specific considerations in addition to the best practices that generally apply across all Generative AI use cases.

**Tool-use**: Just like in standard software development, developers are responsible for the integration of the LLM with the tools and services of their choice. They should define a clear policy for their use case and assess the integrity of the third party services they use to be aware of the safety and security limitations when using this capability. Refer to the Responsible Use Guide for best practices on the safe deployment of the third party safeguards. 

**Multilinguality**: Llama 3.1 supports 7 languages in addition to English: French, German, Hindi, Italian, Portuguese, Spanish, and Thai. Llama may be able to output text in other languages than those that meet performance thresholds for safety and helpfulness. We strongly discourage developers from using this model to converse in non-supported languages without implementing finetuning and system controls in alignment with their policies and the best practices shared in the Responsible Use Guide. 


### Evaluations

We evaluated Llama models for common use cases as well as specific capabilities. Common use cases evaluations measure safety risks of systems for most commonly built applications including chat bot, coding assistant, tool calls. We built dedicated, adversarial evaluation datasets and evaluated systems composed of Llama models and Llama Guard 3 to filter input prompt and output response. It is important to evaluate applications in context, and we recommend building dedicated evaluation dataset for your use case. Prompt Guard and Code Shield are also available if relevant to the application. 

Capability evaluations measure vulnerabilities of Llama models inherent to specific capabilities, for which were crafted dedicated benchmarks including long context, multilingual, tools calls, coding or memorization.

**Red teaming**

For both scenarios, we conducted recurring red teaming exercises with the goal of discovering risks via adversarial prompting and we used the learnings to improve our benchmarks and safety tuning datasets. 

We partnered early with subject-matter experts in critical risk areas to understand the nature of these real-world harms and how such models may lead to unintended harm for society. Based on these conversations, we derived a set of adversarial goals for the red team to attempt to achieve, such as extracting harmful information or reprogramming the model to act in a potentially harmful capacity.  The red team consisted of experts in cybersecurity, adversarial machine learning, responsible AI, and integrity in addition to multilingual content specialists with background in integrity issues in specific geographic markets.


### Critical and other risks 

We specifically focused our efforts on mitigating the following critical risk areas:

**1- CBRNE (Chemical, Biological, Radiological, Nuclear, and Explosive materials) helpfulness**

To assess risks related to proliferation of chemical and biological weapons, we performed uplift testing designed to assess whether use of Llama 3.1 models could meaningfully increase the capabilities of malicious actors to plan or carry out attacks using these types of weapons. 


**2. Child Safety**

Child Safety risk assessments were conducted using a team of experts, to assess the model’s capability to produce outputs that could result in Child Safety risks and inform on any necessary and appropriate risk mitigations via fine tuning. We leveraged those expert red teaming sessions to expand the coverage of our evaluation benchmarks through Llama 3 model development.  For Llama 3, we conducted new in-depth sessions using objective based methodologies to assess the model risks along multiple attack vectors including the additional languages Llama 3 is trained on. We also partnered with content specialists to perform red teaming exercises assessing potentially violating content while taking account of market specific nuances or experiences. 

**3. Cyber attack enablement**

Our cyber attack uplift study investigated whether LLMs can enhance human capabilities in hacking tasks, both in terms of skill level and speed.

Our attack automation study focused on evaluating the capabilities of LLMs when used as autonomous agents in cyber offensive operations, specifically in the context of ransomware attacks. This evaluation was distinct from previous studies that considered LLMs as interactive assistants. The primary objective was to assess whether these models could effectively function as independent agents in executing complex cyber-attacks without human intervention.

Our study of Llama-3.1-405B’s social engineering uplift for cyber attackers was conducted to assess the effectiveness of AI models in aiding cyber threat actors in spear phishing campaigns. Please read our Llama 3.1 Cyber security whitepaper to learn more.


### Community 

Generative AI safety requires expertise and tooling, and we believe in the strength of the open community to accelerate its progress. We are active members of open consortiums, including the AI Alliance, Partnership on AI and MLCommons, actively contributing to safety standardization and transparency. We encourage the community to adopt taxonomies like the MLCommons Proof of Concept evaluation to facilitate collaboration and transparency on safety and content evaluations. Our Purple Llama tools are open sourced for the community to use and widely distributed across ecosystem partners including cloud service providers. We encourage community contributions to our [Github repository](https://github.com/meta-llama/PurpleLlama). 

We also set up the [Llama Impact Grants](https://llama.meta.com/llama-impact-grants/) program to identify and support the most compelling applications of Meta’s Llama model for societal benefit across three categories: education, climate and open innovation. The 20 finalists from the hundreds of applications can be found [here](https://llama.meta.com/llama-impact-grants/#finalists). 

Finally, we put in place a set of resources including an [output reporting mechanism](https://developers.facebook.com/llama_output_feedback) and [bug bounty program](https://www.facebook.com/whitehat) to continuously improve the Llama technology with the help of the community.


## Ethical Considerations and Limitations

The core values of Llama 3.1 are openness, inclusivity and helpfulness. It is meant to serve everyone, and to work for a wide range of use cases. It is thus designed to be accessible to people across many different backgrounds, experiences and perspectives. Llama 3.1 addresses users and their needs as they are, without insertion unnecessary judgment or normativity, while reflecting the understanding that even content that may appear problematic in some cases can serve valuable purposes in others. It respects the dignity and autonomy of all users, especially in terms of the values of free thought and expression that power innovation and progress. 

But Llama 3.1 is a new technology, and like any new technology, there are risks associated with its use. Testing conducted to date has not covered, nor could it cover, all scenarios. For these reasons, as with all LLMs, Llama 3.1’s potential outputs cannot be predicted in advance, and the model may in some instances produce inaccurate, biased or other objectionable responses to user prompts. Therefore, before deploying any applications of Llama 3.1 models, developers should perform safety testing and tuning tailored to their specific applications of the model. Please refer to available resources including our [Responsible Use Guide](https://llama.meta.com/responsible-use-guide), [Trust and Safety](https://llama.meta.com/trust-and-safety/) solutions, and other [resources](https://llama.meta.com/docs/get-started/) to learn more about responsible development. 