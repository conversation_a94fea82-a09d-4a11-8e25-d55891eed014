"""
Test de fidélité du modèle final fine-tuné
Évaluation sur le dataset wasifis/bank-assistant-qa
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from datasets import load_dataset
import random
import json
from pathlib import Path

print("🧪 TEST DE FIDÉLITÉ - MODÈLE FINAL")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Chargement du tokenizer
print("📝 Chargement tokenizer...")
if Path(model_path).exists():
    tokenizer = AutoTokenizer.from_pretrained(model_path)
else:
    tokenizer = AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        token="*************************************"
    )

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Chargement du modèle de base
print("🦙 Chargement modèle de base...")
if Path(model_path).exists():
    base_model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
else:
    base_model = AutoModelForCausalLM.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True,
        token="*************************************"
    )

# Chargement de l'adaptateur LoRA
print("🔧 Chargement adaptateur LoRA...")
model = PeftModel.from_pretrained(base_model, adapter_path)
model.eval()

print("✅ Modèle fine-tuné chargé!")

# Chargement du dataset de test
print("📊 Chargement dataset de test...")
dataset = load_dataset("wasifis/bank-assistant-qa")
test_data = dataset["train"]

# Sélection d'exemples de test
print("🎯 Sélection d'exemples de test...")
test_samples = random.sample(list(test_data), min(10, len(test_data)))

def generate_response(question, max_length=512):
    """Génère une réponse avec le modèle fine-tuné"""
    
    # Format Llama 3.1
    prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    
    # Tokenisation
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # Génération
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            temperature=0.1,  # Très bas pour fidélité
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            repetition_penalty=1.1
        )
    
    # Décodage
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Extraction de la réponse
    if "<|start_header_id|>assistant<|end_header_id|>" in response:
        response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
    
    return response

def calculate_similarity(generated, expected):
    """Calcule une similarité simple basée sur les mots communs"""
    gen_words = set(generated.lower().split())
    exp_words = set(expected.lower().split())
    
    if len(exp_words) == 0:
        return 0.0
    
    common_words = gen_words.intersection(exp_words)
    return len(common_words) / len(exp_words)

# Test de fidélité
print("\n🧪 DÉBUT DES TESTS DE FIDÉLITÉ")
print("=" * 50)

results = []
total_similarity = 0

for i, sample in enumerate(test_samples):
    question = sample["input"]
    expected = sample["output"]
    
    print(f"\n📋 Test {i+1}/10:")
    print(f"❓ Question: {question[:100]}...")
    
    # Génération
    generated = generate_response(question)
    
    # Calcul de similarité
    similarity = calculate_similarity(generated, expected)
    total_similarity += similarity
    
    print(f"✅ Réponse générée: {generated[:150]}...")
    print(f"🎯 Réponse attendue: {expected[:150]}...")
    print(f"📊 Similarité: {similarity:.2%}")
    
    results.append({
        "question": question,
        "generated": generated,
        "expected": expected,
        "similarity": similarity
    })

# Résultats finaux
average_similarity = total_similarity / len(test_samples)

print("\n" + "=" * 50)
print("📊 RÉSULTATS FINAUX:")
print(f"🎯 Fidélité moyenne: {average_similarity:.2%}")
print(f"📈 Tests réalisés: {len(test_samples)}")

# Interprétation
if average_similarity >= 0.7:
    print("🏆 EXCELLENT: Fidélité très élevée!")
elif average_similarity >= 0.5:
    print("✅ BON: Fidélité satisfaisante")
elif average_similarity >= 0.3:
    print("⚠️ MOYEN: Fidélité modérée")
else:
    print("❌ FAIBLE: Fidélité insuffisante")

# Sauvegarde des résultats
results_file = Path("test_fidelity_results.json")
with open(results_file, 'w', encoding='utf-8') as f:
    json.dump({
        "average_similarity": average_similarity,
        "total_tests": len(test_samples),
        "results": results
    }, f, indent=2, ensure_ascii=False)

print(f"💾 Résultats sauvegardés: {results_file}")

print("\n🎉 TEST DE FIDÉLITÉ TERMINÉ!")
print(f"🏦 Votre chatbot bancaire a une fidélité de {average_similarity:.2%}")
print("🚀 Prêt pour utilisation!")
