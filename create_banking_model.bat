@echo off
echo Creation du modele bancaire personnalise...
echo.

echo Verification d'Ollama...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo Erreur: Ollama n'est pas demarre
    echo Lancez 'ollama serve' d'abord
    pause
    exit /b 1
)

echo Creation du modele banking-assistant...
ollama create banking-assistant -f Modelfile.banking

if %errorlevel% equ 0 (
    echo.
    echo Modele 'banking-assistant' cree avec succes!
    echo.
    echo Test du modele:
    echo.
    ollama run banking-assistant "Quels sont les frais de compte?"
) else (
    echo Erreur lors de la creation du modele
)

echo.
pause
