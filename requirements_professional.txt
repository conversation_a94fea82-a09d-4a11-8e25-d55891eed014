# Requirements pour le chatbot bancaire professionnel
# Avec Whisper local et Coqui TTS

# === CORE ML ===
torch>=2.0.0
transformers>=4.30.0
peft>=0.4.0
datasets>=2.12.0
scikit-learn>=1.3.0
numpy>=1.24.0

# === SPEECH PROCESSING ===
# Whisper (Speech-to-Text)
openai-whisper>=20231117

# Coqui TTS (Text-to-Speech)
TTS>=0.22.0

# Audio processing
librosa>=0.10.0
soundfile>=0.12.0

# === WEB API ===
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6

# === UTILITIES ===
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=2.4.0
loguru>=0.7.0

# === IMAGE PROCESSING (optionnel) ===
pillow>=9.5.0
pytesseract>=0.3.10
opencv-python>=4.7.0

# === DEVELOPMENT ===
pytest>=7.4.0
black>=23.0.0
isort>=5.12.0
