{"name": "bankbot-frontend-full", "version": "1.0.0", "description": "Frontend complet pour chatbot bancaire avec UI avancée", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.2", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-dialog": "^1.0.5", "lucide-react": "^0.263.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3"}}