"""
Fine-tuning de fallback - Version ultra-optimisée pour RTX 4060 8GB
"""

import os
import torch
from pathlib import Path

def check_system():
    """Vérifie le système avant le fine-tuning"""
    print("🔍 Vérification du système")
    print("=" * 30)
    
    # CUDA
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU: {gpu_name}")
        print(f"💾 Mémoire: {gpu_memory:.1f} GB")
        
        if gpu_memory < 6:
            print("⚠️ Mémoire GPU insuffisante")
            return False
        elif gpu_memory >= 8:
            print("✅ Mémoire GPU suffisante pour fine-tuning")
        else:
            print("⚠️ Mémoire GPU limite - optimisations requises")
        return True
    else:
        print("❌ CUDA non disponible")
        return False

def minimal_finetuning():
    """Fine-tuning minimal pour test"""
    print("\n🚀 Fine-tuning minimal (test)")
    print("=" * 30)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from transformers import BitsAndBytesConfig
        from peft import LoraConfig, get_peft_model, TaskType
        
        # Configuration ultra-minimale
        model_path = "./models/Llama-3.1-8B-Instruct"
        
        # Tokenizer
        print("📝 Chargement tokenizer...")
        if Path(model_path).exists():
            tokenizer = AutoTokenizer.from_pretrained(model_path)
        else:
            tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                token="*************************************"
            )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Configuration 4-bit extrême
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        # Modèle
        print("🦙 Chargement modèle (4-bit)...")
        if Path(model_path).exists():
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
        else:
            model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                token="*************************************"
            )
        
        print(f"✅ Modèle chargé: {model.num_parameters():,} paramètres")
        
        # LoRA minimal
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # Très petit rank
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj"],  # Seulement 2 modules
            bias="none"
        )
        
        # Appliquer LoRA
        from peft import prepare_model_for_kbit_training
        model = prepare_model_for_kbit_training(model)
        peft_model = get_peft_model(model, lora_config)
        
        peft_model.print_trainable_parameters()
        
        # Test de génération
        print("\n🧪 Test de génération...")
        test_prompt = "Quels sont les frais de compte?"
        
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert.<|eot_id|><|start_header_id|>user<|end_header_id|>

{test_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(chat_prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = peft_model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        print(f"✅ Test réussi!")
        print(f"Q: {test_prompt}")
        print(f"R: {response}")
        
        print(f"\n🎉 Système prêt pour le fine-tuning complet!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 Test de Fine-tuning Fallback")
    print("=" * 40)
    
    if not check_system():
        print("❌ Système non compatible")
        return
    
    if minimal_finetuning():
        print("\n✅ Test réussi!")
        print("🚀 Prêt pour le fine-tuning complet")
        print("💡 Lancez: python start_finetuning_cuda.py")
    else:
        print("\n❌ Test échoué")
        print("💡 Vérifiez l'installation PyTorch CUDA")

if __name__ == "__main__":
    main()
