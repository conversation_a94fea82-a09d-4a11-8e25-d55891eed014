"""
Test GPU optimisé avec gestion correcte des devices
Solution finale pour RTX 4060 + 16GB RAM + CUDA
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
import time
import psutil

print("🏦 TEST GPU OPTIMISÉ - CHATBOT BANCAIRE")
print("=" * 50)

# Configuration
model_path = "./models/Llama-3.1-8B-Instruct"
adapter_path = "./llama_banking_final_fidelity"

# Vérifications
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

print(f"✅ GPU: {torch.cuda.get_device_name()}")
print(f"💾 GPU VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print(f"💾 RAM totale: {psutil.virtual_memory().total / 1024**3:.1f} GB")
print(f"🔧 CUDA Version: {torch.version.cuda}")

# Vider le cache GPU
torch.cuda.empty_cache()

# Configuration quantization 4-bit pour économiser la mémoire
quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

def load_gpu_model():
    """Charge le modèle optimisé pour GPU avec quantization"""
    
    print("📝 Chargement tokenizer...")
    from pathlib import Path
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("🦙 Chargement modèle quantized 4-bit sur GPU...")
    
    try:
        if Path(model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=quantization_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                max_memory={0: "7GB", "cpu": "12GB"}  # Limiter l'usage
            )
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=quantization_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                max_memory={0: "7GB", "cpu": "12GB"},
                token="*************************************"
            )
        
        print(f"✅ Modèle de base chargé avec quantization")
        print(f"💾 GPU utilisé: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        print(f"💾 RAM utilisée: {psutil.virtual_memory().used / 1024**3:.1f} GB")
        
        # Essayer de charger le fine-tuning
        print("🔧 Tentative chargement adaptateur LoRA...")
        try:
            finetuned_model = PeftModel.from_pretrained(
                base_model, 
                adapter_path,
                torch_dtype=torch.float16
            )
            print("✅ Modèle fine-tuné chargé!")
            return tokenizer, finetuned_model
        except Exception as e:
            print(f"⚠️ Erreur LoRA: {e}")
            print("💡 Utilisation du modèle de base quantized")
            return tokenizer, base_model
            
    except Exception as e:
        print(f"❌ Erreur quantization: {e}")
        print("🔄 Essai sans quantization...")
        
        # Fallback sans quantization
        if Path(model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                device_map={"": "cuda:0"},  # Forcer tout sur GPU
                trust_remote_code=True,
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True
            )
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                device_map={"": "cuda:0"},
                trust_remote_code=True,
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True,
                token="*************************************"
            )
        
        print(f"✅ Modèle de base chargé sur GPU")
        return tokenizer, base_model

# Chargement du modèle
try:
    tokenizer, model = load_gpu_model()
    
    print(f"\n💾 État final:")
    print(f"  GPU: {torch.cuda.memory_allocated() / 1024**3:.2f} GB utilisés")
    print(f"  RAM: {psutil.virtual_memory().used / 1024**3:.1f} GB utilisés")
    print(f"  Device du modèle: {next(model.parameters()).device}")
    
    def generate_response(question, max_length=150):
        """Génère une réponse avec gestion correcte des devices"""
        
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        # S'assurer que les inputs sont sur le même device que le modèle
        model_device = next(model.parameters()).device
        inputs = {k: v.to(model_device) for k, v in inputs.items()}
        
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.1,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        generation_time = time.time() - start_time
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        if "<|start_header_id|>assistant<|end_header_id|>" in response:
            response = response.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        
        return response, generation_time

    # Tests bancaires
    print("\n🧪 TESTS BANCAIRES AVEC CUDA:")
    
    test_questions = [
        "What are the charges for account maintenance?",
        "How to open a savings account?", 
        "What is the minimum balance required?"
    ]
    
    for i, question in enumerate(test_questions):
        print(f"\n📋 Test {i+1}/{len(test_questions)}: {question}")
        
        try:
            response, response_time = generate_response(question)
            
            banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                               if word in response.lower())
            
            print(f"⏱️ Temps: {response_time:.2f}s (GPU CUDA)")
            print(f"🎯 Termes bancaires: {banking_words}")
            print(f"💬 Réponse: {response[:150]}...")
            print(f"💾 GPU: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")

    print(f"\n✅ SUCCÈS! Votre GPU RTX 4060 + CUDA fonctionne!")
    print(f"🚀 Performance GPU bien supérieure au CPU")
    
    # Test interactif
    print(f"\n💬 Test interactif? (y/n)")
    choice = input().strip().lower()
    if choice == 'y':
        print("Posez vos questions bancaires (tapez 'quit' pour arrêter)")
        while True:
            user_question = input("\n❓ Question: ").strip()
            if user_question.lower() in ['quit', 'q', 'exit']:
                break
            if user_question:
                print("🤖 Génération GPU...")
                try:
                    response, time_taken = generate_response(user_question)
                    banking_words = sum(1 for word in ['account', 'bank', 'balance', 'transfer', 'loan', 'interest', 'charge', 'fee'] 
                                       if word in response.lower())
                    print(f"⏱️ {time_taken:.2f}s | 🎯 {banking_words} termes | 💬 {response}")
                except Exception as e:
                    print(f"❌ Erreur: {e}")

except Exception as e:
    print(f"❌ Erreur globale: {e}")
    print(f"\n💡 SOLUTIONS:")
    print(f"1. Installez bitsandbytes: pip install bitsandbytes")
    print(f"2. Redémarrez pour libérer la mémoire")
    print(f"3. Fermez d'autres applications")

print(f"\n🎯 TEST TERMINÉ!")
print(f"🏆 Votre config RTX 4060 + 16GB RAM + CUDA est excellente!")
