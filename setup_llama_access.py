"""
Guide pour configurer l'accès à Llama 3.1 pour le fine-tuning LoRA
"""

def setup_huggingface_access():
    """Guide pour configurer l'accès Hugging Face"""
    print("=== Configuration de l'accès Hugging Face pour Llama 3.1 ===\n")
    
    print("ÉTAPES NÉCESSAIRES :")
    print("1. Créer un compte sur https://huggingface.co")
    print("2. Demander l'accès au modèle Llama 3.1:")
    print("   - Aller sur: https://huggingface.co/meta-llama/Meta-Llama-3.1-8B")
    print("   - Cliquer sur 'Request access'")
    print("   - Accepter les conditions d'utilisation")
    print("   - Attendre l'approbation (généralement quelques heures)")
    
    print("\n3. Créer un token d'accès:")
    print("   - Aller sur: https://huggingface.co/settings/tokens")
    print("   - Créer un nouveau token avec les permissions 'read'")
    print("   - <PERSON><PERSON><PERSON> le token")
    
    print("\n4. Configurer le token localement:")
    print("   Option A: Utiliser huggingface-cli")
    print("   > pip install huggingface_hub")
    print("   > huggingface-cli login")
    print("   > [Coller votre token]")
    
    print("\n   Option B: Variable d'environnement")
    print("   > set HF_TOKEN=votre_token_ici")
    
    print("\n5. Tester l'accès:")
    print("   > python test_llama_access.py")

def test_llama_access():
    """Teste l'accès au modèle Llama"""
    print("\n=== Test d'accès à Llama 3.1 ===")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        print("Test du tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Meta-Llama-3.1-8B")
        print("✅ Tokenizer chargé avec succès!")
        
        print("Test du modèle (chargement des métadonnées seulement)...")
        # Ne pas charger le modèle complet pour économiser la mémoire
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained("meta-llama/Meta-Llama-3.1-8B")
        print(f"✅ Configuration du modèle chargée!")
        print(f"   - Vocabulaire: {config.vocab_size}")
        print(f"   - Couches: {config.num_hidden_layers}")
        print(f"   - Dimension: {config.hidden_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'accès: {e}")
        print("\nVérifiez que:")
        print("1. Vous avez l'accès au modèle Llama 3.1")
        print("2. Votre token Hugging Face est configuré")
        print("3. Vous êtes connecté avec: huggingface-cli login")
        return False

def create_env_file():
    """Crée un fichier .env pour les tokens"""
    env_content = """# Configuration pour le fine-tuning Llama
# Remplacez par votre vrai token Hugging Face
HF_TOKEN=hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Configuration Ollama
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b

# Configuration d'entraînement
LORA_RANK=16
LORA_ALPHA=32
LORA_DROPOUT=0.1
"""
    
    with open(".env.example", 'w') as f:
        f.write(env_content)
    
    print("✅ Fichier .env.example créé")
    print("Copiez-le vers .env et ajoutez votre vrai token HF_TOKEN")

if __name__ == "__main__":
    setup_huggingface_access()
    
    print("\n" + "="*50)
    input("Appuyez sur Entrée après avoir configuré votre accès Hugging Face...")
    
    if test_llama_access():
        print("\n✅ Accès Llama configuré avec succès!")
        print("Vous pouvez maintenant procéder au fine-tuning LoRA.")
    else:
        print("\n❌ Problème d'accès. Suivez les étapes ci-dessus.")
    
    create_env_file()
