"""
Continuer le fine-tuning depuis le checkpoint-100
Version simplifiée sans dépendance trainer_state.json
"""

import os
import torch
from pathlib import Path
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import PeftModel, prepare_model_for_kbit_training
from datasets import load_from_disk

def continue_training():
    print("🔄 Continuation du fine-tuning depuis checkpoint-100")
    print("=" * 50)
    
    try:
        # Configuration 4-bit
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        # Charger le modèle de base
        print("🦙 Chargement du modèle de base...")
        model_path = "./models/Llama-3.1-8B-Instruct"
        
        if Path(model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
            tokenizer = AutoTokenizer.from_pretrained(model_path)
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                token="*************************************"
            )
            tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                token="*************************************"
            )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            tokenizer.pad_token_id = tokenizer.eos_token_id
        
        # Préparer pour l'entraînement
        base_model = prepare_model_for_kbit_training(base_model)
        
        # Charger les adapters LoRA depuis le checkpoint
        print("🔧 Chargement des adapters LoRA depuis checkpoint-100...")
        checkpoint_path = "./llama_banking_finetuned/checkpoint-100"
        model = PeftModel.from_pretrained(base_model, checkpoint_path)
        
        print(f"✅ Modèle avec LoRA chargé")
        model.print_trainable_parameters()
        
        # Dataset
        print("📊 Chargement du dataset...")
        dataset = load_from_disk("./processed_banking_dataset_no_instruction")
        
        # Configuration d'entraînement (nouveau départ depuis checkpoint)
        training_args = TrainingArguments(
            output_dir="./llama_banking_finetuned_continued",  # Nouveau dossier
            num_train_epochs=1,  # 1 epoch supplémentaire
            per_device_train_batch_size=1,
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=16,
            learning_rate=5e-5,  # Learning rate plus petit pour la continuation
            weight_decay=0.01,
            warmup_ratio=0.05,  # Moins de warmup
            lr_scheduler_type="cosine",
            max_grad_norm=1.0,
            optim="paged_adamw_8bit",
            fp16=True,
            gradient_checkpointing=True,
            dataloader_pin_memory=True,
            dataloader_num_workers=2,
            logging_steps=5,
            eval_steps=50,
            save_steps=100,
            save_total_limit=2,
            max_steps=400,  # 400 steps supplémentaires
            report_to=[],
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset.get("test", dataset.get("validation")),
            data_collator=data_collator,
            tokenizer=tokenizer,
        )
        
        print(f"\n🚀 Continuation de l'entraînement...")
        print(f"🎯 400 steps supplémentaires")
        print(f"📁 Sauvegarde: ./llama_banking_finetuned_continued")
        print(f"⏱️ Temps estimé: ~20-25 minutes")
        
        # Lancer l'entraînement
        trainer.train()
        
        # Sauvegarder le modèle final
        print("\n💾 Sauvegarde du modèle final...")
        trainer.save_model()
        tokenizer.save_pretrained("./llama_banking_finetuned_continued")
        
        # Copier aussi dans le dossier principal
        print("📋 Copie vers le dossier principal...")
        import shutil
        
        # Copier les fichiers essentiels
        final_dir = "./llama_banking_finetuned_final"
        Path(final_dir).mkdir(exist_ok=True)
        
        # Copier les adapters finaux
        shutil.copy2(
            "./llama_banking_finetuned_continued/adapter_model.safetensors",
            f"{final_dir}/adapter_model.safetensors"
        )
        shutil.copy2(
            "./llama_banking_finetuned_continued/adapter_config.json",
            f"{final_dir}/adapter_config.json"
        )
        
        # Copier le tokenizer
        for file in ["tokenizer.json", "tokenizer_config.json", "special_tokens_map.json"]:
            src = f"./llama_banking_finetuned_continued/{file}"
            if Path(src).exists():
                shutil.copy2(src, f"{final_dir}/{file}")
        
        print(f"\n✅ Fine-tuning terminé!")
        print(f"📁 Modèle final: {final_dir}")
        print(f"🎯 Total: ~500 steps (100 + 400)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🔄 Continuation du Fine-tuning Llama 3.1 8B")
    print("🎮 RTX 4060 8GB + CUDA 12.9")
    print("=" * 50)
    
    # Vérifications
    if not torch.cuda.is_available():
        print("❌ CUDA requis")
        return
    
    # Vérifier le checkpoint
    checkpoint_path = "./llama_banking_finetuned/checkpoint-100"
    if not Path(checkpoint_path).exists():
        print("❌ Checkpoint-100 non trouvé")
        return
    
    print("📊 Statut:")
    print("  ✅ Checkpoint-100 disponible")
    print("  🎯 400 steps supplémentaires")
    print("  📊 Dataset: 4,272 exemples bancaires")
    print("  💾 Sauvegarde: ./llama_banking_finetuned_final")
    
    confirm = input("\nContinuer le fine-tuning? (y/n): ").lower()
    if confirm != 'y':
        print("Continuation annulée")
        return
    
    if continue_training():
        print("\n🎉 FINE-TUNING COMPLET TERMINÉ!")
        print("🏦 Votre chatbot bancaire Llama 3.1 8B est prêt!")
        print("🧪 Testez avec: python test_finetuned_llama.py")
        print("📁 Modèle final: ./llama_banking_finetuned_final")
    else:
        print("\n❌ Échec de la continuation")

if __name__ == "__main__":
    main()
