"""
Script de fine-tuning avec Coda pour chatbot bancaire
Utilise les données wasifis/bank-assistant-qa
"""

import os
import json
import torch
from datasets import load_from_disk, Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodaBankingTrainer:
    """Trainer Coda pour chatbot bancaire"""
    
    def __init__(self, config_path="coda_config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.model = None
        self.tokenizer = None
        self.peft_model = None
    
    def load_model_and_tokenizer(self):
        """Charge le modèle et tokenizer"""
        logger.info("Chargement du modèle de base...")
        
        model_name = self.config["model_config"]["base_model"]
        
        try:
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.config["training_config"]["fp16"] else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            logger.info(f"✅ Modèle chargé: {model_name}")
            logger.info(f"Paramètres: {self.model.num_parameters():,}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def setup_lora(self):
        """Configure LoRA"""
        logger.info("Configuration LoRA...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            **{k: v for k, v in self.config["lora_config"].items() if k != "task_type"}
        )
        
        self.peft_model = get_peft_model(self.model, lora_config)
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré")
        return True
    
    def load_and_prepare_data(self):
        """Charge et prépare les données wasifis"""
        logger.info("Chargement des données wasifis/bank-assistant-qa...")
        
        try:
            # Charger les données depuis processed_data
            data_files = {
                'train': 'processed_data/train.json',
                'validation': 'processed_data/validation.json'
            }
            
            all_data = []
            for split_name, file_path in data_files.items():
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = [json.loads(line) for line in f]
                    all_data.extend(data)
                    logger.info(f"✅ {split_name}: {len(data)} exemples")
            
            # Convertir en format d'entraînement
            training_texts = []
            for item in all_data:
                if 'input' in item and 'output' in item:
                    text = f"Question: {item['input']}\nRéponse: {item['output']}"
                    training_texts.append(text)
            
            # Créer le dataset
            dataset = Dataset.from_dict({"text": training_texts})
            
            # Tokeniser
            def tokenize_function(examples):
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding="max_length",
                    max_length=self.config["training_config"]["max_length"],
                    return_tensors=None
                )
                tokenized["labels"] = tokenized["input_ids"].copy()
                return tokenized
            
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=["text"]
            )
            
            # Diviser en train/validation
            split_dataset = tokenized_dataset.train_test_split(test_size=0.1)
            
            logger.info(f"✅ Données préparées:")
            logger.info(f"  Train: {len(split_dataset['train'])} exemples")
            logger.info(f"  Validation: {len(split_dataset['test'])} exemples")
            
            return split_dataset
            
        except Exception as e:
            logger.error(f"❌ Erreur de préparation des données: {e}")
            return None
    
    def train_model(self, dataset):
        """Lance l'entraînement"""
        logger.info("Démarrage de l'entraînement Coda...")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_config"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["test"],
            data_collator=data_collator,
        )
        
        try:
            # Entraînement
            trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_config"]["output_dir"])
            
            logger.info("✅ Entraînement terminé et modèle sauvegardé")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_model(self):
        """Teste le modèle fine-tuné"""
        logger.info("Test du modèle fine-tuné...")
        
        test_questions = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt?"
        ]
        
        for question in test_questions:
            prompt = f"Question: {question}\nRéponse:"
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\nQ: {question}")
            print(f"R: {response}")

def main():
    """Fonction principale"""
    print("🏦 Fine-tuning Coda - Chatbot Bancaire")
    print("=" * 50)
    
    trainer = CodaBankingTrainer()
    
    # 1. Charger le modèle
    if not trainer.load_model_and_tokenizer():
        return
    
    # 2. Configurer LoRA
    trainer.setup_lora()
    
    # 3. Préparer les données
    dataset = trainer.load_and_prepare_data()
    if dataset is None:
        return
    
    # 4. Entraîner
    print("\nVoulez-vous lancer l'entraînement? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if trainer.train_model(dataset):
            print("\n🎉 Fine-tuning Coda terminé avec succès!")
            trainer.test_model()
        else:
            print("\n❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé")

if __name__ == "__main__":
    main()
