"""
Fine-tuning ultra-simple pour haute fidélité
"""

import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk

print("🎯 Fine-tuning Ultra-Simple Haute Fidélité")
print("=" * 50)

# Configuration
output_dir = "./llama_banking_ultra_fidelity"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

torch.cuda.empty_cache()
print(f"✅ GPU: {torch.cuda.get_device_name()}")

# 4-bit config
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

# Tokenizer
print("📝 Chargement tokenizer...")
model_path = "./models/Llama-3.1-8B-Instruct"

if Path(model_path).exists():
    tokenizer = AutoTokenizer.from_pretrained(model_path)
else:
    tokenizer = AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        token="*************************************"
    )

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Modèle
print("🦙 Chargement modèle...")
if Path(model_path).exists():
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
else:
    model = AutoModelForCausalLM.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True,
        token="*************************************"
    )

model = prepare_model_for_kbit_training(model)

# LoRA haute capacité
print("🔧 Configuration LoRA...")
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=32,  # Rank élevé
    lora_alpha=64,
    lora_dropout=0.05,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    bias="none"
)

peft_model = get_peft_model(model, lora_config)
peft_model.print_trainable_parameters()

# Dataset
print("📊 Chargement dataset...")
dataset = load_from_disk("./processed_banking_dataset_no_instruction")
train_dataset = dataset["train"]

print(f"✅ Dataset: {len(train_dataset)} exemples")

# Configuration d'entraînement SIMPLE
training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=3,  # 3 epochs pour mémorisation
    per_device_train_batch_size=1,
    gradient_accumulation_steps=16,
    learning_rate=3e-5,  # Learning rate bas
    weight_decay=0.01,
    warmup_ratio=0.05,
    lr_scheduler_type="cosine",
    max_grad_norm=0.5,
    optim="paged_adamw_8bit",
    fp16=True,
    gradient_checkpointing=True,
    dataloader_pin_memory=False,
    dataloader_num_workers=0,
    logging_steps=10,
    save_steps=300,
    save_total_limit=3,
    report_to=[],
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Trainer
trainer = Trainer(
    model=peft_model,
    args=training_args,
    train_dataset=train_dataset,
    data_collator=data_collator,
    processing_class=tokenizer,
)

# Configuration
print("\n🎯 Configuration HAUTE FIDÉLITÉ:")
print(f"  📊 LoRA rank: 32")
print(f"  🧠 Learning rate: 3e-5")
print(f"  🔄 Epochs: 3")
print(f"  📈 Steps totaux: ~{len(train_dataset) // 16 * 3}")
print(f"  💾 Sauvegarde: {output_dir}")
print(f"  ⏱️ Temps estimé: 45-60 minutes")

# Entraînement
print("\n🚀 Début fine-tuning haute fidélité...")

try:
    train_result = trainer.train()
    
    # Sauvegarder
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    print(f"\n✅ FINE-TUNING TERMINÉ!")
    print(f"📊 Loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
    print(f"📁 Modèle: {output_dir}")
    
    # Sauvegarder métriques
    import json
    with open(Path(output_dir) / "metrics.json", 'w') as f:
        json.dump(train_result.metrics, f, indent=2)
    
    print(f"🧪 Testez avec: python test_ultra_fidelity.py")
    
except Exception as e:
    print(f"❌ Erreur: {e}")

print("\n🎉 FINE-TUNING HAUTE FIDÉLITÉ TERMINÉ!")
print("🏦 Chatbot bancaire optimisé pour fidélité maximale!")
