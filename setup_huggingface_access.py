"""
Configuration de l'accès Hugging Face pour télécharger Llama 3.1 8B
"""

import os
import subprocess
import sys
from pathlib import Path

def install_huggingface_hub():
    """Installe huggingface_hub si nécessaire"""
    print("=== Installation Hugging Face Hub ===")
    
    try:
        import huggingface_hub
        print("✅ huggingface_hub déjà installé")
        return True
    except ImportError:
        print("📦 Installation de huggingface_hub...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "huggingface_hub"])
            print("✅ huggingface_hub installé")
            return True
        except Exception as e:
            print(f"❌ Erreur d'installation: {e}")
            return False

def setup_hf_token():
    """Configure le token Hugging Face"""
    print("\n=== Configuration du token Hugging Face ===")
    
    # Vérifier si le token existe déjà
    try:
        from huggingface_hub import HfApi
        api = HfApi()
        user_info = api.whoami()
        print(f"✅ Déjà connecté en tant que: {user_info['name']}")
        return True
    except Exception:
        print("🔑 Token non configuré")
    
    print("\n📋 Étapes pour obtenir votre token:")
    print("1. Allez sur: https://huggingface.co/settings/tokens")
    print("2. Cliquez sur 'New token'")
    print("3. Name: 'llama-finetuning'")
    print("4. Type: 'Write'")
    print("5. Copiez le token (format: hf_xxxxxxxxxx)")
    
    token = input("\n🔑 Collez votre token Hugging Face: ").strip()
    
    if not token.startswith('hf_'):
        print("❌ Token invalide (doit commencer par 'hf_')")
        return False
    
    try:
        # Méthode 1: huggingface-cli login
        print("🔄 Configuration du token...")
        subprocess.run([sys.executable, "-m", "huggingface_hub.commands.huggingface_cli", "login", "--token", token], check=True)
        print("✅ Token configuré avec succès")
        return True
    except Exception as e:
        print(f"⚠️ Erreur avec huggingface-cli: {e}")
        
        # Méthode 2: Variable d'environnement
        try:
            os.environ['HUGGINGFACE_HUB_TOKEN'] = token
            print("✅ Token configuré via variable d'environnement")
            return True
        except Exception as e:
            print(f"❌ Erreur de configuration: {e}")
            return False

def request_llama_access():
    """Guide pour demander l'accès à Llama"""
    print("\n=== Accès au modèle Llama 3.1 ===")
    
    print("📋 Étapes pour obtenir l'accès:")
    print("1. Allez sur: https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct")
    print("2. Cliquez sur 'Request access'")
    print("3. Remplissez le formulaire:")
    print("   - Nom complet")
    print("   - Organisation (optionnel)")
    print("   - Utilisation prévue: 'Research and fine-tuning'")
    print("4. Acceptez les conditions d'utilisation")
    print("5. Attendez l'approbation (généralement quelques heures)")
    
    print("\n⏰ L'accès est généralement accordé rapidement")
    print("📧 Vous recevrez un email de confirmation")

def test_llama_access():
    """Teste l'accès au modèle Llama"""
    print("\n=== Test d'accès à Llama 3.1 ===")
    
    try:
        from huggingface_hub import HfApi
        api = HfApi()
        
        # Tester l'accès au modèle
        model_id = "meta-llama/Llama-3.1-8B-Instruct"
        
        try:
            model_info = api.model_info(model_id)
            print(f"✅ Accès confirmé à {model_id}")
            print(f"📊 Taille du modèle: ~16GB")
            print(f"🏷️ Tags: {', '.join(model_info.tags[:5])}")
            return True
        except Exception as e:
            if "gated" in str(e).lower():
                print(f"🔒 Modèle protégé - accès requis")
                print(f"💡 Demandez l'accès sur: https://huggingface.co/{model_id}")
                return False
            else:
                print(f"❌ Erreur d'accès: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de test: {e}")
        return False

def download_llama_model():
    """Télécharge le modèle Llama 3.1 8B"""
    print("\n=== Téléchargement Llama 3.1 8B ===")
    
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = "./models/Llama-3.1-8B-Instruct"
    
    print(f"📥 Téléchargement de {model_id}")
    print(f"📁 Destination: {local_dir}")
    print(f"⚠️ Taille: ~16GB - Cela peut prendre du temps")
    
    confirm = input("Voulez-vous télécharger maintenant? (y/n): ").lower()
    if confirm != 'y':
        print("Téléchargement annulé")
        return False
    
    try:
        from huggingface_hub import snapshot_download
        
        print("🔄 Téléchargement en cours...")
        snapshot_download(
            repo_id=model_id,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True
        )
        
        print(f"✅ Modèle téléchargé dans: {local_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de téléchargement: {e}")
        return False

def create_model_config():
    """Crée un fichier de configuration pour le modèle"""
    config = {
        "model_name": "meta-llama/Llama-3.1-8B-Instruct",
        "local_path": "./models/Llama-3.1-8B-Instruct",
        "model_type": "llama",
        "size": "8B",
        "context_length": 8192,
        "download_date": "2025-01-16",
        "usage": "banking_chatbot_finetuning"
    }
    
    import json
    with open("llama_model_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration sauvegardée: llama_model_config.json")

def main():
    """Fonction principale"""
    print("🦙 Configuration Hugging Face pour Llama 3.1 8B")
    print("=" * 60)
    
    # 1. Installer huggingface_hub
    if not install_huggingface_hub():
        print("❌ Installation échouée")
        return
    
    # 2. Configurer le token
    if not setup_hf_token():
        print("❌ Configuration du token échouée")
        return
    
    # 3. Tester l'accès
    if not test_llama_access():
        print("❌ Pas d'accès au modèle")
        request_llama_access()
        print("\n💡 Revenez après avoir obtenu l'accès")
        return
    
    # 4. Menu de téléchargement
    print("\n" + "=" * 60)
    print("OPTIONS:")
    print("1. 📥 Télécharger le modèle maintenant")
    print("2. ⏭️ Configurer seulement (télécharger plus tard)")
    print("3. 🚪 Quitter")
    
    choice = input("Votre choix (1-3): ").strip()
    
    if choice == "1":
        if download_llama_model():
            create_model_config()
            print("\n🎉 Modèle prêt pour le fine-tuning!")
        
    elif choice == "2":
        create_model_config()
        print("\n✅ Configuration créée")
        print("💡 Pour télécharger plus tard:")
        print("   python setup_huggingface_access.py")
        
    elif choice == "3":
        print("👋 Au revoir!")
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
