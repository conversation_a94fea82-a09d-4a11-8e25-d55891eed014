"""
Configuration pour fine-tuning avec Coda (local)
Utilise les données wasifis/bank-assistant-qa
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def install_coda_dependencies():
    """Installe les dépendances pour Coda"""
    print("=== Installation des dépendances Coda ===")
    
    dependencies = [
        "torch>=2.0.0",
        "transformers>=4.35.0",
        "datasets>=2.14.0",
        "peft>=0.6.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",  # Pour la quantification
        "scipy>=1.11.0",
        "sentencepiece>=0.1.99",
        "protobuf>=3.20.0"
    ]
    
    print("Installation des packages Python...")
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur avec {dep}: {e}")
    
    print("✅ Dépendances installées")

def download_base_model():
    """Télécharge un modèle de base compatible"""
    print("\n=== Téléchargement du modèle de base ===")
    
    # Utiliser un modèle plus petit et libre pour les tests
    model_options = [
        "microsoft/DialoGPT-medium",  # 355M paramètres
        "microsoft/DialoGPT-small",   # 117M paramètres  
        "gpt2-large",                 # 774M paramètres
        "gpt2"                        # 124M paramètres (fallback)
    ]
    
    print("Modèles recommandés pour le fine-tuning local:")
    for i, model in enumerate(model_options, 1):
        print(f"  {i}. {model}")
    
    print("\nNote: Ces modèles sont libres d'accès et optimisés pour le fine-tuning local")
    return model_options[0]  # DialoGPT-medium par défaut

def prepare_coda_config():
    """Prépare la configuration pour Coda"""
    print("\n=== Configuration Coda ===")
    
    config = {
        "model_config": {
            "base_model": "microsoft/DialoGPT-medium",
            "model_type": "causal_lm",
            "torch_dtype": "float16",
            "device_map": "auto"
        },
        "lora_config": {
            "task_type": "CAUSAL_LM",
            "r": 16,                    # Rank LoRA
            "lora_alpha": 32,           # Alpha parameter
            "lora_dropout": 0.1,        # Dropout
            "target_modules": ["c_attn", "c_proj"],  # Modules pour DialoGPT
            "bias": "none",
            "inference_mode": False
        },
        "training_config": {
            "output_dir": "./coda_banking_model",
            "num_train_epochs": 3,
            "per_device_train_batch_size": 4,
            "per_device_eval_batch_size": 4,
            "gradient_accumulation_steps": 4,
            "learning_rate": 2e-4,
            "weight_decay": 0.01,
            "warmup_steps": 100,
            "logging_steps": 10,
            "eval_steps": 100,
            "save_steps": 100,
            "max_length": 512,
            "fp16": True,               # Utiliser FP16 pour économiser la mémoire
            "dataloader_pin_memory": False,
            "remove_unused_columns": False
        },
        "data_config": {
            "dataset_path": "./processed_data",
            "max_samples": None,        # None = tous les échantillons
            "train_split": 0.8,
            "val_split": 0.1,
            "test_split": 0.1
        }
    }
    
    # Sauvegarder la configuration
    with open("coda_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Configuration Coda sauvegardée: coda_config.json")
    return config

def create_coda_training_script():
    """Crée le script de fine-tuning avec Coda"""
    print("\n=== Création du script Coda ===")
    
    script_content = '''"""
Script de fine-tuning avec Coda pour chatbot bancaire
Utilise les données wasifis/bank-assistant-qa
"""

import os
import json
import torch
from datasets import load_from_disk, Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodaBankingTrainer:
    """Trainer Coda pour chatbot bancaire"""
    
    def __init__(self, config_path="coda_config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.model = None
        self.tokenizer = None
        self.peft_model = None
    
    def load_model_and_tokenizer(self):
        """Charge le modèle et tokenizer"""
        logger.info("Chargement du modèle de base...")
        
        model_name = self.config["model_config"]["base_model"]
        
        try:
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.config["training_config"]["fp16"] else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            logger.info(f"✅ Modèle chargé: {model_name}")
            logger.info(f"Paramètres: {self.model.num_parameters():,}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def setup_lora(self):
        """Configure LoRA"""
        logger.info("Configuration LoRA...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            **{k: v for k, v in self.config["lora_config"].items() if k != "task_type"}
        )
        
        self.peft_model = get_peft_model(self.model, lora_config)
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré")
        return True
    
    def load_and_prepare_data(self):
        """Charge et prépare les données wasifis"""
        logger.info("Chargement des données wasifis/bank-assistant-qa...")
        
        try:
            # Charger les données depuis processed_data
            data_files = {
                'train': 'processed_data/train.json',
                'validation': 'processed_data/validation.json'
            }
            
            all_data = []
            for split_name, file_path in data_files.items():
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = [json.loads(line) for line in f]
                    all_data.extend(data)
                    logger.info(f"✅ {split_name}: {len(data)} exemples")
            
            # Convertir en format d'entraînement
            training_texts = []
            for item in all_data:
                if 'input' in item and 'output' in item:
                    text = f"Question: {item['input']}\\nRéponse: {item['output']}"
                    training_texts.append(text)
            
            # Créer le dataset
            dataset = Dataset.from_dict({"text": training_texts})
            
            # Tokeniser
            def tokenize_function(examples):
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding="max_length",
                    max_length=self.config["training_config"]["max_length"],
                    return_tensors=None
                )
                tokenized["labels"] = tokenized["input_ids"].copy()
                return tokenized
            
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=["text"]
            )
            
            # Diviser en train/validation
            split_dataset = tokenized_dataset.train_test_split(test_size=0.1)
            
            logger.info(f"✅ Données préparées:")
            logger.info(f"  Train: {len(split_dataset['train'])} exemples")
            logger.info(f"  Validation: {len(split_dataset['test'])} exemples")
            
            return split_dataset
            
        except Exception as e:
            logger.error(f"❌ Erreur de préparation des données: {e}")
            return None
    
    def train_model(self, dataset):
        """Lance l'entraînement"""
        logger.info("Démarrage de l'entraînement Coda...")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_config"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["test"],
            data_collator=data_collator,
        )
        
        try:
            # Entraînement
            trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_config"]["output_dir"])
            
            logger.info("✅ Entraînement terminé et modèle sauvegardé")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_model(self):
        """Teste le modèle fine-tuné"""
        logger.info("Test du modèle fine-tuné...")
        
        test_questions = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt?"
        ]
        
        for question in test_questions:
            prompt = f"Question: {question}\\nRéponse:"
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\\nQ: {question}")
            print(f"R: {response}")

def main():
    """Fonction principale"""
    print("🏦 Fine-tuning Coda - Chatbot Bancaire")
    print("=" * 50)
    
    trainer = CodaBankingTrainer()
    
    # 1. Charger le modèle
    if not trainer.load_model_and_tokenizer():
        return
    
    # 2. Configurer LoRA
    trainer.setup_lora()
    
    # 3. Préparer les données
    dataset = trainer.load_and_prepare_data()
    if dataset is None:
        return
    
    # 4. Entraîner
    print("\\nVoulez-vous lancer l'entraînement? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if trainer.train_model(dataset):
            print("\\n🎉 Fine-tuning Coda terminé avec succès!")
            trainer.test_model()
        else:
            print("\\n❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé")

if __name__ == "__main__":
    main()
'''
    
    with open("coda_training.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Script Coda créé: coda_training.py")

def create_model_usage_script():
    """Crée un script pour utiliser le modèle fine-tuné"""
    usage_script = '''"""
Utilisation du modèle fine-tuné avec Coda
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import json

class CodaBankingChatbot:
    """Chatbot utilisant le modèle Coda fine-tuné"""
    
    def __init__(self, model_path="./coda_banking_model"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.load_model()
    
    def load_model(self):
        """Charge le modèle fine-tuné"""
        try:
            print("Chargement du modèle fine-tuné...")
            
            # Charger la configuration
            with open("coda_config.json", 'r') as f:
                config = json.load(f)
            
            base_model_name = config["model_config"]["base_model"]
            
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(base_model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            base_model = AutoModelForCausalLM.from_pretrained(base_model_name)
            
            # Charger les adapters LoRA
            self.model = PeftModel.from_pretrained(base_model, self.model_path)
            
            print("✅ Modèle fine-tuné chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
    
    def chat(self, question):
        """Chat avec le modèle fine-tuné"""
        if self.model is None:
            return "Modèle non chargé"
        
        prompt = f"Question: {question}\\nRéponse:"
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(prompt):].strip()
        return response
    
    def interactive_chat(self):
        """Chat interactif"""
        print("\\n🏦 Chatbot Bancaire Coda Fine-tuné")
        print("=" * 40)
        print("💬 Posez vos questions (tapez 'quit' pour quitter)")
        print("-" * 40)
        
        while True:
            user_input = input("\\n👤 Vous: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                print("👋 Au revoir!")
                break
            
            if user_input:
                response = self.chat(user_input)
                print(f"🤖 Assistant: {response}")

def main():
    chatbot = CodaBankingChatbot()
    
    if chatbot.model:
        chatbot.interactive_chat()
    else:
        print("❌ Impossible de charger le modèle")
        print("Assurez-vous d'avoir exécuté le fine-tuning d'abord")

if __name__ == "__main__":
    main()
'''
    
    with open("use_coda_model.py", 'w', encoding='utf-8') as f:
        f.write(usage_script)
    
    print("✅ Script d'utilisation créé: use_coda_model.py")

def main():
    """Fonction principale"""
    print("🏦 Configuration Fine-tuning Coda")
    print("=" * 40)
    
    # 1. Installer les dépendances
    print("1. Installation des dépendances...")
    install_coda_dependencies()
    
    # 2. Configurer Coda
    print("\\n2. Configuration Coda...")
    config = prepare_coda_config()
    
    # 3. Créer les scripts
    print("\\n3. Création des scripts...")
    create_coda_training_script()
    create_model_usage_script()
    
    print("\\n" + "=" * 50)
    print("🎉 CONFIGURATION CODA TERMINÉE!")
    print("=" * 50)
    print("📁 Fichiers créés:")
    print("  ✅ coda_config.json - Configuration")
    print("  ✅ coda_training.py - Script d'entraînement")
    print("  ✅ use_coda_model.py - Script d'utilisation")
    
    print("\\n🚀 ÉTAPES SUIVANTES:")
    print("1. Lancer l'entraînement: python coda_training.py")
    print("2. Tester le modèle: python use_coda_model.py")
    
    print("\\n📊 DONNÉES:")
    print("✅ Base: wasifis/bank-assistant-qa (4,272 exemples)")
    print("✅ Modèle: DialoGPT-medium (355M paramètres)")
    print("✅ Technique: LoRA (16 rank, 32 alpha)")

if __name__ == "__main__":
    main()
