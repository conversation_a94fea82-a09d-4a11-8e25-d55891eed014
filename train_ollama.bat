@echo off
REM Script de fine-tuning Ollama pour chatbot bancaire

echo 🏦 Fine-tuning Ollama - Chatbot Bancaire
echo ========================================

REM Vérifier qu'Ollama est démarré
echo 1. Vérification d'Ollama...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama n'est pas démarré. Lancez 'ollama serve' d'abord.
    exit /b 1
)
echo ✅ Ollama est actif

REM Créer le modèle fine-tuné
echo 2. Création du modèle fine-tuné...
ollama create banking-assistant -f Modelfile

if %errorlevel% equ 0 (
    echo ✅ Modèle 'banking-assistant' créé avec succès!
    echo.
    echo 🎯 Pour utiliser le modèle:
    echo ollama run banking-assistant
    echo.
    echo 🧪 Test rapide:
    ollama run banking-assistant "Quels sont les frais de tenue de compte?"
) else (
    echo ❌ Erreur lors de la création du modèle
    exit /b 1
)
