"""
Fine-tuning Llama 3.1 8B - Version simplifiée haute fidélité
"""

import os
import torch
from pathlib import Path
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk

def main():
    print("🎯 Fine-tuning Haute Fidélité - Version Simplifiée")
    print("=" * 50)
    
    # Configuration
    output_dir = "./llama_banking_high_fidelity_v2"
    
    # 1. Configuration CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA requis")
        return
    
    torch.cuda.empty_cache()
    print(f"✅ GPU: {torch.cuda.get_device_name()}")
    
    # 2. Configuration 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    # 3. Charger le tokenizer
    print("📝 Chargement tokenizer...")
    model_path = "./models/Llama-3.1-8B-Instruct"
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id
    
    # 4. Charger le modèle
    print("🦙 Chargement modèle...")
    if Path(model_path).exists():
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            token="*************************************"
        )
    
    # 5. Préparer pour l'entraînement
    model = prepare_model_for_kbit_training(model)
    
    # 6. Configuration LoRA haute capacité
    print("🔧 Configuration LoRA haute capacité...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=32,  # Rank élevé
        lora_alpha=64,  # Alpha élevé
        lora_dropout=0.05,  # Dropout faible
        target_modules=[
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj"
        ],
        bias="none"
    )
    
    # Appliquer LoRA
    peft_model = get_peft_model(model, lora_config)
    peft_model.print_trainable_parameters()
    
    # 7. Charger le dataset
    print("📊 Chargement dataset...")
    dataset = load_from_disk("./processed_banking_dataset_no_instruction")
    
    # Split train/eval
    train_dataset = dataset["train"]
    train_size = int(0.9 * len(train_dataset))
    
    train_split = train_dataset.select(range(train_size))
    eval_split = train_dataset.select(range(train_size, len(train_dataset)))
    
    print(f"✅ Train: {len(train_split)} exemples")
    print(f"✅ Eval: {len(eval_split)} exemples")
    
    # 8. Configuration d'entraînement HAUTE FIDÉLITÉ
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=3,  # 3 epochs complets
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=16,
        learning_rate=5e-5,  # Learning rate bas pour mémorisation
        weight_decay=0.01,
        warmup_ratio=0.05,
        lr_scheduler_type="cosine",
        max_grad_norm=0.5,
        optim="paged_adamw_8bit",
        fp16=True,
        gradient_checkpointing=True,
        dataloader_pin_memory=False,
        dataloader_num_workers=0,
        logging_steps=5,
        eval_steps=200,
        save_steps=200,
        save_total_limit=5,
        evaluation_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to=[],
    )
    
    # 9. Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # 10. Trainer
    trainer = Trainer(
        model=peft_model,
        args=training_args,
        train_dataset=train_split,
        eval_dataset=eval_split,
        data_collator=data_collator,
        processing_class=tokenizer,
    )
    
    # 11. Confirmer
    print("\n🎯 Configuration HAUTE FIDÉLITÉ:")
    print(f"  📊 LoRA rank: 32 (haute capacité)")
    print(f"  🧠 Learning rate: 5e-5 (mémorisation)")
    print(f"  🔄 Epochs: 3 complets")
    print(f"  📈 Validation: Chaque 100 steps")
    print(f"  💾 Sauvegarde: {output_dir}")
    print(f"  ⏱️ Temps estimé: 45-60 minutes")
    
    confirm = input("\nLancer le fine-tuning haute fidélité? (y/n): ").lower()
    if confirm != 'y':
        print("Fine-tuning annulé")
        return
    
    # 12. Entraînement
    print("\n🚀 Début fine-tuning haute fidélité...")
    
    try:
        train_result = trainer.train()
        
        # Sauvegarder
        trainer.save_model()
        tokenizer.save_pretrained(output_dir)
        
        # Évaluation finale
        eval_results = trainer.evaluate()
        
        print(f"\n✅ FINE-TUNING TERMINÉ!")
        print(f"📊 Train loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
        print(f"📊 Eval loss finale: {eval_results.get('eval_loss', 'N/A')}")
        print(f"📁 Modèle sauvegardé: {output_dir}")
        print(f"🧪 Testez avec: python test_high_fidelity_model.py")
        
        # Sauvegarder les métriques
        import json
        with open(Path(output_dir) / "final_metrics.json", 'w') as f:
            json.dump({
                'train_metrics': train_result.metrics,
                'eval_metrics': eval_results
            }, f, indent=2)
        
        print(f"📈 Métriques sauvegardées: {output_dir}/final_metrics.json")
        
    except Exception as e:
        print(f"❌ Erreur d'entraînement: {e}")
        return
    
    print("\n🎉 FINE-TUNING HAUTE FIDÉLITÉ TERMINÉ!")
    print("🏦 Votre chatbot bancaire est optimisé pour fidélité maximale!")

if __name__ == "__main__":
    main()
