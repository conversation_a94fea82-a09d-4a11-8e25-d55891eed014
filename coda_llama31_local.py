"""
Fine-tuning Coda avec Llama 3.1 local
Utilise le modèle llama3.1 téléchargé via llama-stack
"""

import os
import json
import torch
from pathlib import Path
from datasets import Dataset
from transformers import (
    LlamaTokenizer,
    LlamaForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodaLlama31Trainer:
    """Trainer Coda pour Llama 3.1 local"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.config = self.create_llama31_config()
    
    def create_llama31_config(self):
        """Crée la configuration pour Llama 3.1 local"""
        config = {
            "model_config": {
                # Chemin vers le modèle Llama 3.1 local
                "model_path": self.find_local_llama_path(),
                "model_type": "llama",
                "torch_dtype": "float16",
                "device_map": "auto",
                "load_in_8bit": True,  # Quantification pour économiser la mémoire
                "trust_remote_code": True
            },
            "lora_config": {
                "task_type": "CAUSAL_LM",
                "r": 16,                    # Rank LoRA
                "lora_alpha": 32,           # Alpha parameter
                "lora_dropout": 0.1,        # Dropout
                "target_modules": [         # Modules spécifiques à Llama
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ],
                "bias": "none",
                "inference_mode": False
            },
            "training_config": {
                "output_dir": "./coda_llama31_model",
                "num_train_epochs": 2,      # Moins d'epochs pour Llama
                "per_device_train_batch_size": 1,  # Batch size très petit
                "per_device_eval_batch_size": 1,
                "gradient_accumulation_steps": 8,  # Compenser le petit batch
                "learning_rate": 1e-4,      # Learning rate plus petit
                "weight_decay": 0.01,
                "warmup_steps": 50,
                "logging_steps": 5,
                "eval_steps": 50,
                "save_steps": 50,
                "max_length": 512,
                "fp16": True,
                "dataloader_pin_memory": False,
                "remove_unused_columns": False,
                "gradient_checkpointing": True,  # Économiser la mémoire
                "optim": "adamw_8bit"       # Optimiseur 8-bit
            }
        }
        
        # Sauvegarder la configuration
        with open("coda_llama31_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ Configuration Llama 3.1 créée: coda_llama31_config.json")
        return config
    
    def find_local_llama_path(self):
        """Trouve le chemin du modèle Llama 3.1 local"""
        # Chemins possibles pour Llama Stack
        possible_paths = [
            "~/.llama/checkpoints/Llama3.1-8B",
            "~/.cache/llama-stack/models/Llama3.1-8B",
            "./models/Llama3.1-8B",
            "meta-llama/Llama-3.1-8B"  # Fallback vers Hugging Face
        ]
        
        for path in possible_paths:
            expanded_path = Path(path).expanduser()
            if expanded_path.exists():
                logger.info(f"✅ Modèle Llama trouvé: {expanded_path}")
                return str(expanded_path)
        
        # Si aucun chemin local trouvé, utiliser Hugging Face
        logger.warning("⚠️ Modèle local non trouvé, utilisation de Hugging Face")
        return "meta-llama/Llama-3.1-8B"
    
    def load_llama31_model(self):
        """Charge le modèle Llama 3.1 local"""
        logger.info("Chargement de Llama 3.1...")
        
        model_path = self.config["model_config"]["model_path"]
        
        try:
            # Charger le tokenizer Llama
            logger.info("Chargement du tokenizer Llama...")
            try:
                self.tokenizer = LlamaTokenizer.from_pretrained(model_path)
            except:
                # Fallback vers AutoTokenizer
                from transformers import AutoTokenizer
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Configurer le padding token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            logger.info("✅ Tokenizer Llama chargé")
            
            # Charger le modèle avec optimisations mémoire
            logger.info("Chargement du modèle Llama 3.1 (cela peut prendre du temps)...")
            
            self.model = LlamaForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto" if torch.cuda.is_available() else None,
                load_in_8bit=self.config["model_config"]["load_in_8bit"],
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            logger.info(f"✅ Llama 3.1 chargé: {self.model.num_parameters():,} paramètres")
            
            # Préparer pour l'entraînement
            self.model.train()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement Llama: {e}")
            logger.info("💡 Suggestions:")
            logger.info("1. Vérifiez que llama-stack est installé")
            logger.info("2. Téléchargez le modèle: llama model download --source meta --model-id Meta-Llama-3.1-8B")
            logger.info("3. Ou configurez l'accès Hugging Face")
            return False
    
    def setup_lora_for_llama(self):
        """Configure LoRA spécifiquement pour Llama"""
        logger.info("Configuration LoRA pour Llama 3.1...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=self.config["lora_config"]["r"],
            lora_alpha=self.config["lora_config"]["lora_alpha"],
            lora_dropout=self.config["lora_config"]["lora_dropout"],
            target_modules=self.config["lora_config"]["target_modules"],
            bias=self.config["lora_config"]["bias"]
        )
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, lora_config)
        
        # Afficher les paramètres entraînables
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré pour Llama 3.1")
        return True
    
    def load_wasifis_data_for_llama(self):
        """Charge et prépare les données wasifis pour Llama"""
        logger.info("Chargement des données wasifis/bank-assistant-qa pour Llama...")
        
        try:
            # Charger les données
            data_files = {
                'train': 'processed_data/train.json',
                'validation': 'processed_data/validation.json'
            }
            
            all_data = []
            for split_name, file_path in data_files.items():
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = [json.loads(line) for line in f]
                    all_data.extend(data)
                    logger.info(f"✅ {split_name}: {len(data)} exemples")
            
            # Prendre un sous-ensemble pour les tests (Llama est plus lourd)
            max_samples = 1000  # Limiter pour les tests
            if len(all_data) > max_samples:
                all_data = all_data[:max_samples]
                logger.info(f"📊 Limitation à {max_samples} exemples pour les tests")
            
            # Convertir en format Llama avec prompt système
            training_texts = []
            for item in all_data:
                if 'input' in item and 'output' in item:
                    # Format de chat Llama 3.1
                    text = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nVous êtes un assistant bancaire expert et professionnel.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{item['input']}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{item['output']}<|eot_id|>"
                    training_texts.append(text)
            
            # Créer le dataset
            dataset = Dataset.from_dict({"text": training_texts})
            
            # Tokeniser avec le tokenizer Llama
            def tokenize_function(examples):
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding="max_length",
                    max_length=self.config["training_config"]["max_length"],
                    return_tensors=None
                )
                tokenized["labels"] = tokenized["input_ids"].copy()
                return tokenized
            
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=["text"],
                desc="Tokenisation Llama"
            )
            
            # Diviser en train/validation
            split_dataset = tokenized_dataset.train_test_split(test_size=0.1)
            
            logger.info(f"✅ Données préparées pour Llama:")
            logger.info(f"  Train: {len(split_dataset['train'])} exemples")
            logger.info(f"  Validation: {len(split_dataset['test'])} exemples")
            
            return split_dataset
            
        except Exception as e:
            logger.error(f"❌ Erreur de préparation des données: {e}")
            return None
    
    def train_llama_with_lora(self, dataset):
        """Lance l'entraînement LoRA sur Llama 3.1"""
        logger.info("Démarrage de l'entraînement LoRA sur Llama 3.1...")
        
        # Arguments d'entraînement optimisés pour Llama
        training_args = TrainingArguments(**self.config["training_config"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["test"],
            data_collator=data_collator,
        )
        
        try:
            # Entraînement
            logger.info("🚀 Démarrage de l'entraînement...")
            trainer.train()
            
            # Sauvegarder les adapters LoRA
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_config"]["output_dir"])
            
            logger.info("✅ Entraînement terminé et adapters LoRA sauvegardés")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False
    
    def test_llama_model(self):
        """Teste le modèle Llama fine-tuné"""
        logger.info("Test du modèle Llama 3.1 fine-tuné...")
        
        test_questions = [
            "Quels sont les frais de tenue de compte?",
            "Comment ouvrir un compte épargne?",
            "Quelles sont les conditions pour un prêt?"
        ]
        
        for question in test_questions:
            # Format de prompt Llama 3.1
            prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nVous êtes un assistant bancaire expert.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
            
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = self.peft_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"\n❓ {question}")
            print(f"🤖 {response}")

def main():
    """Fonction principale"""
    print("🦙 Fine-tuning Coda avec Llama 3.1 Local")
    print("=" * 50)
    
    trainer = CodaLlama31Trainer()
    
    # 1. Charger Llama 3.1
    print("\n1. Chargement de Llama 3.1...")
    if not trainer.load_llama31_model():
        print("❌ Impossible de charger Llama 3.1")
        return
    
    # 2. Configurer LoRA
    print("\n2. Configuration LoRA...")
    trainer.setup_lora_for_llama()
    
    # 3. Préparer les données
    print("\n3. Préparation des données...")
    dataset = trainer.load_wasifis_data_for_llama()
    if dataset is None:
        print("❌ Problème avec les données")
        return
    
    # 4. Entraîner
    print("\n4. Voulez-vous lancer l'entraînement LoRA sur Llama 3.1? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        if trainer.train_llama_with_lora(dataset):
            print("\n🎉 Fine-tuning Llama 3.1 terminé avec succès!")
            trainer.test_llama_model()
        else:
            print("\n❌ Échec de l'entraînement")
    else:
        print("Entraînement annulé")
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ:")
    print("✅ Modèle: Llama 3.1-8B (local)")
    print("✅ Technique: LoRA (16 rank, 32 alpha)")
    print("✅ Données: wasifis/bank-assistant-qa")
    print("✅ Optimisations: 8-bit, gradient checkpointing")

if __name__ == "__main__":
    main()
