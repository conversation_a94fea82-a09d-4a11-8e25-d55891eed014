@echo off
echo 🧹 Nettoyage du projet BankBot AI pour Swiss Transfer...
echo.

REM Supprimer les dossiers node_modules
echo Suppression des node_modules...
if exist "chat-bank-nexus-main(frontend v0)\node_modules" (
    rmdir /s /q "chat-bank-nexus-main(frontend v0)\node_modules"
    echo ✅ node_modules supprimé
)

REM Supprimer les fichiers de cache Python
echo Suppression du cache Python...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul
echo ✅ Cache Python supprimé

REM Supprimer les logs
echo Suppression des logs...
del /s /q *.log 2>nul
if exist "logs" rmdir /s /q "logs"
echo ✅ Logs supprimés

REM Supprimer les fichiers de conversation
echo Suppression des conversations sauvegardées...
del /q conversation_*.json 2>nul
echo ✅ Conversations supprimées

REM Supprimer les fichiers temporaires
echo Suppression des fichiers temporaires...
del /s /q *.tmp 2>nul
del /s /q *.temp 2>nul
if exist "tmp" rmdir /s /q "tmp"
if exist "temp" rmdir /s /q "temp"
echo ✅ Fichiers temporaires supprimés

REM Supprimer les bases de données locales
echo Suppression des bases de données locales...
del /q *.db 2>nul
del /q *.sqlite 2>nul
del /q *.sqlite3 2>nul
echo ✅ Bases de données supprimées

REM Supprimer les fichiers de build
echo Suppression des builds...
if exist "chat-bank-nexus-main(frontend v0)\dist" rmdir /s /q "chat-bank-nexus-main(frontend v0)\dist"
if exist "chat-bank-nexus-main(frontend v0)\build" rmdir /s /q "chat-bank-nexus-main(frontend v0)\build"
echo ✅ Builds supprimés

REM Supprimer les fichiers IDE
echo Suppression des fichiers IDE...
if exist ".vscode" rmdir /s /q ".vscode"
if exist ".idea" rmdir /s /q ".idea"
del /s /q *.swp 2>nul
del /s /q *.swo 2>nul
echo ✅ Fichiers IDE supprimés

REM Supprimer les gros fichiers de modèles (si présents)
echo Suppression des modèles volumineux...
del /s /q *.bin 2>nul
del /s /q *.safetensors 2>nul
del /s /q *.gguf 2>nul
del /s /q *.model 2>nul
del /s /q *.pkl 2>nul
del /s /q *.h5 2>nul
echo ✅ Modèles volumineux supprimés

REM Supprimer les dossiers de cache
echo Suppression des caches...
if exist ".cache" rmdir /s /q ".cache"
if exist "cache" rmdir /s /q "cache"
if exist ".pytest_cache" rmdir /s /q ".pytest_cache"
echo ✅ Caches supprimés

echo.
echo 🎉 Nettoyage terminé !
echo.
echo 📁 Fichiers conservés :
echo   ✅ Code source (.py, .js, .ts, .tsx)
echo   ✅ Configuration (.json, .yaml, .toml)
echo   ✅ Documentation (.md)
echo   ✅ Styles (.css)
echo   ✅ Assets essentiels
echo.
echo 📦 Prêt pour Swiss Transfer !
pause
