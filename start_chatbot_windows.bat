@echo off
echo 🎯 DÉMARRAGE CHATBOT BANCAIRE - WINDOWS
echo ============================================================

echo 🔍 Vérification de Node.js...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm non trouvé. Installez Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ npm trouvé

echo 📦 Installation des dépendances Python...
pip install fastapi uvicorn python-multipart pydantic >nul 2>&1
echo ✅ Dépendances Python installées

echo 📦 Installation des dépendances npm...
cd "chat-bank-nexus-main(frontend v0)"
if not exist node_modules (
    echo 📥 Installation des packages npm...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Erreur installation npm
        pause
        exit /b 1
    )
    echo ✅ Packages npm installés
) else (
    echo ✅ Packages npm déjà installés
)
cd ..

echo.
echo 🚀 DÉMARRAGE DES SERVICES...
echo ============================================================

echo 🔗 Backend API: http://localhost:8000
echo 🌐 Frontend: http://localhost:8080
echo.
echo ⚠️ IMPORTANT: Gardez cette fenêtre ouverte
echo ⌨️ Appuyez sur Ctrl+C pour arrêter
echo.

echo 🚀 Démarrage du backend...
start "Backend RAG" cmd /k "python backend_rag_api.py"

echo ⏳ Attente 5 secondes...
timeout /t 5 /nobreak >nul

echo 🚀 Démarrage du frontend...
start "Frontend React" cmd /k "cd \"chat-bank-nexus-main(frontend v0)\" && npm run dev"

echo.
echo 🎉 CHATBOT DÉMARRÉ!
echo ============================================================
echo 💡 Votre chatbot utilise:
echo    🦙 Votre modèle fine-tuné (llama_banking_final_fidelity)
echo    📊 RAG avec base wasifis/bank-assistant-qa
echo    ⚛️ Frontend React + TypeScript
echo    🚀 API FastAPI
echo ============================================================
echo.
echo 📱 Ouvrez votre navigateur sur: http://localhost:8080
echo.
pause
