"""
Préparation du dataset wasifis/bank-assistant-qa pour fine-tuning
SANS la colonne instruction (seulement input/output)
"""

import json
import os
from datasets import load_dataset, Dataset
from transformers import AutoTokenizer
import torch
from pathlib import Path

class BankingDatasetProcessor:
    """Processeur de dataset bancaire optimisé"""
    
    def __init__(self):
        self.dataset = None
        self.tokenizer = None
        self.processed_dataset = None
        
    def load_tokenizer(self):
        """Charge le tokenizer depuis le modèle local"""
        print("=== Chargement du tokenizer ===")
        
        # Essayer le modèle local d'abord
        local_model_path = "./models/Llama-3.1-8B-Instruct"
        
        try:
            if Path(local_model_path).exists():
                print(f"📁 Chargement depuis: {local_model_path}")
                self.tokenizer = AutoTokenizer.from_pretrained(local_model_path)
            else:
                print("📥 Chargement depuis Hugging Face...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            # Configuration du tokenizer
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            print(f"✅ Tokenizer chargé")
            print(f"📊 Vocabulaire: {len(self.tokenizer)} tokens")
            return True
            
        except Exception as e:
            print(f"❌ Erreur tokenizer: {e}")
            return False
    
    def load_wasifis_dataset(self):
        """Charge le dataset wasifis/bank-assistant-qa"""
        print("\n=== Chargement dataset wasifis/bank-assistant-qa ===")
        
        try:
            # Charger le dataset
            self.dataset = load_dataset("wasifis/bank-assistant-qa")
            
            print(f"✅ Dataset chargé:")
            for split_name, split_data in self.dataset.items():
                print(f"  - {split_name}: {len(split_data)} exemples")
            
            # Examiner la structure
            example = self.dataset['train'][0]
            print(f"\n📋 Structure du dataset:")
            for key, value in example.items():
                print(f"  - {key}: {str(value)[:100]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
            return False
    
    def process_dataset_no_instruction(self):
        """Traite le dataset en excluant la colonne instruction"""
        print("\n=== Traitement dataset (sans instruction) ===")
        
        if self.dataset is None:
            print("❌ Dataset non chargé")
            return False
        
        try:
            def process_examples(examples):
                """Traite les exemples en utilisant seulement input/output"""
                processed_texts = []
                
                for i in range(len(examples['input'])):
                    user_input = examples['input'][i]
                    assistant_output = examples['output'][i]
                    
                    # Format de chat Llama 3.1 (SANS instruction)
                    conversation = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

Vous êtes un assistant bancaire expert et professionnel. Vous aidez les clients avec leurs questions bancaires de manière précise, courtoise et professionnelle.<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

{assistant_output}<|eot_id|>"""
                    
                    processed_texts.append(conversation)
                
                return {"text": processed_texts}
            
            # Traiter chaque split
            processed_splits = {}
            
            for split_name, split_data in self.dataset.items():
                print(f"🔄 Traitement {split_name}...")
                
                # Vérifier les colonnes disponibles
                columns = split_data.column_names
                print(f"  📋 Colonnes: {columns}")
                
                if 'input' not in columns or 'output' not in columns:
                    print(f"  ❌ Colonnes input/output manquantes dans {split_name}")
                    continue
                
                # Supprimer la colonne instruction si elle existe
                columns_to_remove = [col for col in columns if col not in ['input', 'output']]
                if 'instruction' in columns_to_remove:
                    print(f"  🗑️ Suppression de la colonne 'instruction'")
                
                # Traiter les données
                processed_split = split_data.map(
                    process_examples,
                    batched=True,
                    remove_columns=columns,
                    desc=f"Processing {split_name}"
                )
                
                processed_splits[split_name] = processed_split
                print(f"  ✅ {split_name}: {len(processed_split)} exemples traités")
            
            # Créer le dataset final
            from datasets import DatasetDict
            self.processed_dataset = DatasetDict(processed_splits)
            
            print(f"\n✅ Dataset traité (sans instruction):")
            for split_name, split_data in self.processed_dataset.items():
                print(f"  - {split_name}: {len(split_data)} exemples")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de traitement: {e}")
            return False
    
    def tokenize_dataset(self, max_length=2048):
        """Tokenise le dataset"""
        print(f"\n=== Tokenisation (max_length={max_length}) ===")
        
        if self.processed_dataset is None or self.tokenizer is None:
            print("❌ Dataset ou tokenizer non prêt")
            return False
        
        try:
            def tokenize_function(examples):
                # Tokeniser
                tokenized = self.tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,
                    max_length=max_length,
                    return_tensors=None
                )
                
                # Labels = input_ids pour le fine-tuning supervisé
                tokenized["labels"] = tokenized["input_ids"].copy()
                
                return tokenized
            
            # Tokeniser tous les splits
            tokenized_splits = {}
            
            for split_name, split_data in self.processed_dataset.items():
                print(f"🔄 Tokenisation {split_name}...")
                
                tokenized_split = split_data.map(
                    tokenize_function,
                    batched=True,
                    remove_columns=["text"],
                    desc=f"Tokenizing {split_name}"
                )
                
                tokenized_splits[split_name] = tokenized_split
                
                # Statistiques
                lengths = [len(item['input_ids']) for item in tokenized_split]
                avg_length = sum(lengths) / len(lengths)
                max_len = max(lengths)
                
                print(f"  ✅ {split_name}: {len(tokenized_split)} exemples")
                print(f"    📏 Longueur moyenne: {avg_length:.1f} tokens")
                print(f"    📏 Longueur max: {max_len} tokens")
            
            # Mettre à jour le dataset
            from datasets import DatasetDict
            self.processed_dataset = DatasetDict(tokenized_splits)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de tokenisation: {e}")
            return False
    
    def save_processed_dataset(self, output_dir="./processed_banking_dataset_no_instruction"):
        """Sauvegarde le dataset traité"""
        print(f"\n=== Sauvegarde dataset ===")
        
        if self.processed_dataset is None:
            print("❌ Dataset non traité")
            return False
        
        try:
            # Créer le répertoire
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # Sauvegarder le dataset
            self.processed_dataset.save_to_disk(output_dir)
            
            # Sauvegarder le tokenizer
            tokenizer_dir = Path(output_dir) / "tokenizer"
            self.tokenizer.save_pretrained(tokenizer_dir)
            
            # Métadonnées
            metadata = {
                "dataset_source": "wasifis/bank-assistant-qa",
                "processing": "input_output_only_no_instruction",
                "total_examples": sum(len(split) for split in self.processed_dataset.values()),
                "max_length": 2048,
                "format": "llama_chat_format",
                "splits": {name: len(split) for name, split in self.processed_dataset.items()},
                "columns_excluded": ["instruction"],
                "columns_used": ["input", "output"]
            }
            
            with open(Path(output_dir) / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Dataset sauvegardé: {output_dir}")
            print(f"📁 Structure:")
            print(f"  - dataset_dict.json")
            print(f"  - train/")
            print(f"  - test/ (si disponible)")
            print(f"  - tokenizer/")
            print(f"  - metadata.json")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur de sauvegarde: {e}")
            return False

def main():
    """Fonction principale"""
    print("🏦 Préparation Dataset Bancaire (SANS instruction)")
    print("=" * 60)
    
    processor = BankingDatasetProcessor()
    
    # 1. Charger le tokenizer
    if not processor.load_tokenizer():
        print("❌ Impossible de charger le tokenizer")
        return
    
    # 2. Charger le dataset
    if not processor.load_wasifis_dataset():
        print("❌ Impossible de charger le dataset")
        return
    
    # 3. Traiter (sans instruction)
    if not processor.process_dataset_no_instruction():
        print("❌ Erreur de traitement")
        return
    
    # 4. Tokeniser
    if not processor.tokenize_dataset():
        print("❌ Erreur de tokenisation")
        return
    
    # 5. Sauvegarder
    if processor.save_processed_dataset():
        print("\n🎉 Dataset prêt pour le fine-tuning!")
        print("✅ Colonnes 'instruction' exclues")
        print("✅ Format: input → output seulement")
        print("🚀 Prochaine étape: python start_finetuning_cuda.py")
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ:")
    print("✅ Dataset wasifis/bank-assistant-qa chargé")
    print("✅ Colonne 'instruction' supprimée")
    print("✅ Format Llama 3.1 appliqué (input/output)")
    print("✅ Tokenisation complète")
    print("✅ Prêt pour CUDA fine-tuning")

if __name__ == "__main__":
    main()
