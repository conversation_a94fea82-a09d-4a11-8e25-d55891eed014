"""
Chat bancaire simple avec llama3.1:8b via Ollama
"""

import ollama

def main():
    """Chat interactif simple"""
    print("🏦 Chatbot Bancaire avec Llama 3.1-8B")
    print("=" * 50)
    
    # Initialiser Ollama
    client = ollama.Client()
    model = "llama3.1:8b"
    
    # Prompt système pour le banking
    system_prompt = """Vous êtes un assistant bancaire expert et professionnel. 
Répondez aux questions des clients de manière précise, courtoise et professionnelle.
Utilisez vos connaissances bancaires pour fournir des informations utiles.

Domaines d'expertise:
- Comptes bancaires (courant, épargne)
- Cartes bancaires et paiements
- Prêts et crédits
- Services bancaires en ligne
- Frais et tarifs
- Procédures administratives"""
    
    print("✅ Connexion établie avec llama3.1:8b")
    print("💬 Tapez vos questions bancaires (tapez 'quit' pour quitter)")
    print("-" * 50)
    
    # Historique de conversation
    conversation = []
    
    while True:
        # Demander la question à l'utilisateur
        user_input = input("\n👤 Vous: ").strip()
        
        # Vérifier si l'utilisateur veut quitter
        if user_input.lower() in ['quit', 'exit', 'quitter', 'q']:
            print("👋 Au revoir! Merci d'avoir utilisé notre assistant bancaire.")
            break
        
        if not user_input:
            continue
        
        try:
            # Préparer les messages
            messages = [{'role': 'system', 'content': system_prompt}]
            
            # Ajouter l'historique récent (5 derniers échanges)
            messages.extend(conversation[-10:])
            
            # Ajouter la question actuelle
            messages.append({'role': 'user', 'content': user_input})
            
            print("🤖 Assistant: ", end="", flush=True)
            
            # Envoyer à Ollama
            response = client.chat(
                model=model,
                messages=messages,
                options={
                    'temperature': 0.7,
                    'top_p': 0.9,
                    'max_tokens': 512
                }
            )
            
            assistant_response = response['message']['content']
            print(assistant_response)
            
            # Sauvegarder dans l'historique
            conversation.append({'role': 'user', 'content': user_input})
            conversation.append({'role': 'assistant', 'content': assistant_response})
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            print("Vérifiez qu'Ollama est démarré et que llama3.1:8b est disponible")

if __name__ == "__main__":
    main()
