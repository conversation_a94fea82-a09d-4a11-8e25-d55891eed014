"""
Test rapide d'Ollama
"""

import ollama
import time

def test_ollama():
    """Test rapide d'Ollama"""
    print("🔍 Test de connexion Ollama...")
    
    try:
        client = ollama.Client()
        
        # Test 1: Lister les modèles
        print("1. Liste des modèles...")
        models = client.list()
        print(f"   Trouvé {len(models['models'])} modèles")
        
        # Chercher llama3.1:8b
        llama_found = False
        for model in models['models']:
            name = model.get('name', 'unknown')
            if 'llama3.1:8b' in name:
                llama_found = True
                print(f"   ✅ {name} trouvé")
                break
        
        if not llama_found:
            print("   ❌ llama3.1:8b non trouvé")
            return False
        
        # Test 2: Chat simple
        print("2. Test de chat...")
        start_time = time.time()
        
        response = client.chat(
            model='llama3.1:8b',
            messages=[
                {'role': 'user', 'content': 'Bonjour'}
            ]
        )
        
        end_time = time.time()
        
        print(f"   ✅ Réponse reçue en {end_time - start_time:.1f}s")
        print(f"   Réponse: {response['message']['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def demo_banking_chat():
    """Démo du chat bancaire"""
    print("\n🏦 Démo Chat Bancaire")
    print("=" * 30)
    
    client = ollama.Client()
    
    questions = [
        "Quels sont les frais de tenue de compte?",
        "Comment ouvrir un compte épargne?",
        "Quelles sont les conditions pour un prêt?"
    ]
    
    system_prompt = "Vous êtes un assistant bancaire professionnel. Répondez de manière concise et précise."
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. Q: {question}")
        print("   R: ", end="", flush=True)
        
        try:
            response = client.chat(
                model='llama3.1:8b',
                messages=[
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': question}
                ]
            )
            
            answer = response['message']['content']
            # Limiter la réponse à 200 caractères pour la démo
            if len(answer) > 200:
                answer = answer[:200] + "..."
            
            print(answer)
            
        except Exception as e:
            print(f"Erreur: {e}")

if __name__ == "__main__":
    print("🚀 Test rapide Ollama + Démo Banking")
    print("=" * 40)
    
    if test_ollama():
        print("\n✅ Ollama fonctionne!")
        demo_banking_chat()
        
        print("\n" + "=" * 40)
        print("🎯 Votre chatbot bancaire est prêt!")
        print("Vous pouvez maintenant utiliser:")
        print("- python chat_bancaire_simple.py")
        print("- python banking_chatbot_final.py")
    else:
        print("\n❌ Problème avec Ollama")
        print("Vérifiez qu'Ollama est démarré:")
        print("- ollama serve")
        print("- ollama list")
