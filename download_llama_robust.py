"""
Téléchargement robuste de Llama 3.1 8B avec reprise automatique
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def install_requirements():
    """Installe les dépendances avec optimisations"""
    print("📦 Installation des dépendances optimisées...")
    
    packages = [
        "huggingface_hub[hf_xet]",  # Version optimisée
        "transformers",
        "torch",
        "accelerate",
        "requests"
    ]
    
    for package in packages:
        try:
            if package == "huggingface_hub[hf_xet]":
                import huggingface_hub
                print(f"✅ huggingface_hub déjà installé")
                # Installer hf_xet séparément pour optimiser
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "hf_xet"], 
                                        capture_output=True)
                    print("✅ hf_xet installé pour optimisation")
                except:
                    print("⚠️ hf_xet non installé (optionnel)")
            else:
                __import__(package.replace('-', '_'))
                print(f"✅ {package} déjà installé")
        except ImportError:
            print(f"🔄 Installation de {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def download_with_retry(model_id, local_dir, token, max_retries=5):
    """Télécharge avec reprises automatiques"""
    
    for attempt in range(max_retries):
        print(f"\n🔄 Tentative {attempt + 1}/{max_retries}")
        
        try:
            from huggingface_hub import snapshot_download
            
            print(f"🚀 Début du téléchargement...")
            
            # Configuration optimisée
            snapshot_download(
                repo_id=model_id,
                local_dir=str(local_dir),
                local_dir_use_symlinks=False,
                resume_download=True,
                token=token,
                max_workers=4,  # Parallélisation
                tqdm_class=None  # Désactiver la barre de progression pour éviter les conflits
            )
            
            print(f"\n✅ Téléchargement réussi!")
            return True
            
        except KeyboardInterrupt:
            print(f"\n⏸️ Téléchargement interrompu par l'utilisateur")
            return False
            
        except Exception as e:
            print(f"\n❌ Erreur tentative {attempt + 1}: {e}")
            
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 30  # Attente progressive
                print(f"⏳ Attente {wait_time}s avant nouvelle tentative...")
                time.sleep(wait_time)
            else:
                print(f"❌ Échec après {max_retries} tentatives")
                return False
    
    return False

def check_download_progress():
    """Vérifie la progression du téléchargement"""
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    if not local_dir.exists():
        return 0, 0
    
    files = list(local_dir.glob("*"))
    total_size = sum(f.stat().st_size for f in files if f.is_file())
    total_size_gb = total_size / (1024**3)
    
    # Taille attendue du modèle complet
    expected_size_gb = 16.0
    progress_percent = min((total_size_gb / expected_size_gb) * 100, 100)
    
    return total_size_gb, progress_percent

def main():
    """Fonction principale avec reprise robuste"""
    print("🦙 Téléchargement Robuste Llama 3.1 8B")
    print("=" * 50)
    
    # Configuration
    token = "*************************************"
    model_id = "meta-llama/Llama-3.1-8B-Instruct"
    local_dir = Path("./models/Llama-3.1-8B-Instruct")
    
    # Vérifier la progression actuelle
    current_size, progress = check_download_progress()
    
    if progress > 0:
        print(f"📊 Téléchargement existant détecté:")
        print(f"  💾 Taille actuelle: {current_size:.1f} GB")
        print(f"  📈 Progression: {progress:.1f}%")
        
        if progress > 95:
            print(f"✅ Téléchargement semble complet!")
            choice = input("Re-télécharger quand même? (y/n): ").lower()
            if choice != 'y':
                print("✅ Utilisation du modèle existant")
                return
    
    # Créer le répertoire
    local_dir.mkdir(parents=True, exist_ok=True)
    
    # Installer les dépendances
    install_requirements()
    
    print(f"\n📥 Configuration du téléchargement:")
    print(f"  🎯 Modèle: {model_id}")
    print(f"  📁 Destination: {local_dir}")
    print(f"  💾 Taille: ~16 GB")
    print(f"  🔄 Reprise automatique: Activée")
    print(f"  ⚡ Optimisations: hf_xet, parallélisation")
    
    confirm = input(f"\nCommencer/Reprendre le téléchargement? (y/n): ").lower()
    
    if confirm != 'y':
        print("Téléchargement annulé")
        return
    
    # Lancer le téléchargement avec reprises
    start_time = time.time()
    
    if download_with_retry(model_id, local_dir, token):
        end_time = time.time()
        duration = (end_time - start_time) / 60  # en minutes
        
        # Vérifier le résultat final
        final_size, final_progress = check_download_progress()
        
        print(f"\n" + "=" * 50)
        print(f"🎉 TÉLÉCHARGEMENT TERMINÉ!")
        print(f"=" * 50)
        print(f"✅ Modèle: {model_id}")
        print(f"📁 Emplacement: {local_dir}")
        print(f"💾 Taille finale: {final_size:.1f} GB")
        print(f"📈 Progression: {final_progress:.1f}%")
        print(f"⏱️ Durée: {duration:.1f} minutes")
        
        # Lister les fichiers principaux
        files = list(local_dir.glob("*.safetensors")) + list(local_dir.glob("*.pth"))
        print(f"\n📄 Fichiers du modèle:")
        for file in files:
            size_gb = file.stat().st_size / (1024**3)
            print(f"  📄 {file.name} ({size_gb:.1f} GB)")
        
        # Créer un fichier de statut
        import json
        status = {
            "model_id": model_id,
            "local_path": str(local_dir),
            "download_complete": True,
            "final_size_gb": final_size,
            "download_duration_minutes": duration,
            "files_count": len(list(local_dir.glob("*")))
        }
        
        with open("llama_download_status.json", 'w') as f:
            json.dump(status, f, indent=2)
        
        print(f"\n✅ Statut sauvegardé: llama_download_status.json")
        print(f"🚀 Prêt pour le fine-tuning!")
        print(f"\n🎯 Prochaines étapes:")
        print(f"  1. python prepare_banking_dataset.py")
        print(f"  2. python llama_finetuning_cuda.py")
        
    else:
        print(f"\n❌ Téléchargement échoué")
        print(f"💡 Suggestions:")
        print(f"  - Vérifiez votre connexion internet")
        print(f"  - Libérez de l'espace disque")
        print(f"  - Relancez le script (reprise automatique)")

if __name__ == "__main__":
    main()
