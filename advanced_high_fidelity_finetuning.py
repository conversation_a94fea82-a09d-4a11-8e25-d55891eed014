"""
Fine-tuning AVANCÉ Llama 3.1 8B - HAUTE FIDÉLITÉ MAXIMALE
Configuration optimisée pour mémorisation parfaite du dataset wasifis/bank-assistant-qa
"""

import os
import json
import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_environment():
    """Configuration environnement optimisée"""
    print("🔧 Configuration environnement haute fidélité")
    
    if not torch.cuda.is_available():
        raise RuntimeError("❌ CUDA requis")
    
    # Optimisations CUDA pour stabilité
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True
    torch.backends.cudnn.benchmark = True
    
    # Variables d'environnement optimisées
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["CUDA_LAUNCH_BLOCKING"] = "0"
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
    
    # Vider le cache
    torch.cuda.empty_cache()
    
    # Informations GPU
    device = torch.cuda.current_device()
    gpu_props = torch.cuda.get_device_properties(device)
    print(f"✅ GPU: {gpu_props.name}")
    print(f"✅ Mémoire: {gpu_props.total_memory / 1024**3:.1f} GB")

def load_model_and_tokenizer():
    """Charge le modèle avec configuration optimisée"""
    print("🦙 Chargement Llama 3.1 8B (Configuration Avancée)")
    
    # Configuration 4-bit optimisée
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    # Tokenizer
    model_path = "./models/Llama-3.1-8B-Instruct"
    
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id
    
    # Modèle
    if Path(model_path).exists():
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            token="*************************************"
        )
    
    # Préparer pour l'entraînement
    model = prepare_model_for_kbit_training(model)
    
    print(f"✅ Modèle chargé: {model.num_parameters():,} paramètres")
    return model, tokenizer

def setup_advanced_lora(model):
    """Configuration LoRA avancée haute capacité"""
    print("🔧 Configuration LoRA Avancée (Haute Capacité)")
    
    # Configuration LoRA MAXIMALE pour fidélité
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=64,  # Rank TRÈS élevé pour capacité maximale
        lora_alpha=128,  # Alpha très élevé
        lora_dropout=0.03,  # Dropout très faible
        target_modules=[
            # TOUS les modules de projection
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj",
            # Modules supplémentaires pour capacité maximale
            "embed_tokens", "lm_head"
        ],
        bias="none",
        modules_to_save=["embed_tokens", "lm_head"]  # Sauvegarder ces modules
    )
    
    # Appliquer LoRA
    peft_model = get_peft_model(model, lora_config)
    peft_model.print_trainable_parameters()
    
    print("✅ LoRA avancé configuré pour fidélité maximale")
    return peft_model

def load_and_prepare_dataset():
    """Charge et prépare le dataset avec validation"""
    print("📊 Chargement et préparation dataset")
    
    dataset = load_from_disk("./processed_banking_dataset_no_instruction")
    train_dataset = dataset["train"]
    
    # Split optimisé train/validation (85/15)
    total_size = len(train_dataset)
    train_size = int(0.85 * total_size)
    
    train_split = train_dataset.select(range(train_size))
    eval_split = train_dataset.select(range(train_size, total_size))
    
    print(f"✅ Train: {len(train_split)} exemples")
    print(f"✅ Validation: {len(eval_split)} exemples")
    
    return train_split, eval_split

def create_advanced_training_args():
    """Configuration d'entraînement avancée"""
    print("⚙️ Configuration entraînement avancé")
    
    output_dir = "./llama_banking_advanced_fidelity"
    
    # Configuration HAUTE FIDÉLITÉ
    training_args = TrainingArguments(
        # Répertoire de sortie
        output_dir=output_dir,
        
        # Epochs et steps
        num_train_epochs=5,  # 5 epochs complets pour mémorisation
        max_steps=-1,  # Pas de limite, utiliser epochs
        
        # Batch et accumulation
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=32,  # Accumulation élevée
        
        # Learning rate TRÈS BAS pour mémorisation
        learning_rate=8e-6,  # Encore plus bas
        weight_decay=0.005,  # Weight decay réduit
        warmup_ratio=0.02,   # Warmup minimal
        lr_scheduler_type="cosine_with_restarts",  # Scheduler avancé
        
        # Optimisation
        optim="paged_adamw_8bit",
        max_grad_norm=0.3,  # Gradient clipping strict
        
        # Précision et mémoire
        fp16=True,
        gradient_checkpointing=True,
        dataloader_pin_memory=False,
        dataloader_num_workers=0,
        
        # Logging et sauvegarde
        logging_steps=3,
        eval_steps=100,
        save_steps=200,
        save_total_limit=10,  # Plus de checkpoints
        
        # Validation et sélection du meilleur modèle
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,

        # Autres
        report_to=[],
        remove_unused_columns=False,
        dataloader_drop_last=False,
    )
    
    return training_args, output_dir

def train_advanced_model(peft_model, tokenizer, train_dataset, eval_dataset, training_args):
    """Entraînement avancé avec monitoring"""
    print("🚀 Démarrage Fine-tuning Avancé")
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer avec configuration avancée
    trainer = Trainer(
        model=peft_model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        data_collator=data_collator,
        processing_class=tokenizer,
    )
    
    # Informations pré-entraînement
    total_steps = len(train_dataset) // 32 * 5  # 32 = gradient_accumulation_steps
    print(f"📊 Configuration finale:")
    print(f"  🎯 Epochs: 5")
    print(f"  📈 Steps totaux estimés: {total_steps}")
    print(f"  🧠 Learning rate: 8e-6")
    print(f"  📊 LoRA rank: 64 (capacité maximale)")
    print(f"  💾 Batch effectif: 32")
    print(f"  ⏱️ Temps estimé: 60-90 minutes")
    
    try:
        # Entraînement
        print("\n🚀 Début entraînement haute fidélité...")
        train_result = trainer.train()
        
        # Sauvegarder le modèle final
        trainer.save_model()
        tokenizer.save_pretrained(training_args.output_dir)
        
        # Évaluation finale
        eval_results = trainer.evaluate()
        
        # Sauvegarder les métriques
        final_metrics = {
            'train_metrics': train_result.metrics,
            'eval_metrics': eval_results,
            'config': {
                'epochs': 5,
                'learning_rate': 8e-6,
                'lora_rank': 64,
                'total_steps': total_steps
            }
        }
        
        with open(Path(training_args.output_dir) / "advanced_metrics.json", 'w') as f:
            json.dump(final_metrics, f, indent=2)
        
        print(f"\n✅ FINE-TUNING AVANCÉ TERMINÉ!")
        print(f"📊 Train loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
        print(f"📊 Eval loss finale: {eval_results.get('eval_loss', 'N/A')}")
        print(f"📁 Modèle sauvegardé: {training_args.output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'entraînement: {e}")
        return False

def main():
    """Fonction principale"""
    print("🎯 FINE-TUNING AVANCÉ HAUTE FIDÉLITÉ")
    print("🏦 Dataset: wasifis/bank-assistant-qa")
    print("🎮 RTX 4060 8GB + CUDA 12.9")
    print("=" * 60)
    
    try:
        # 1. Configuration environnement
        setup_environment()
        
        # 2. Charger modèle et tokenizer
        print("\n1. Chargement du modèle...")
        model, tokenizer = load_model_and_tokenizer()
        
        # 3. Configuration LoRA avancée
        print("\n2. Configuration LoRA avancée...")
        peft_model = setup_advanced_lora(model)
        
        # 4. Préparation dataset
        print("\n3. Préparation dataset...")
        train_dataset, eval_dataset = load_and_prepare_dataset()
        
        # 5. Configuration entraînement
        print("\n4. Configuration entraînement avancé...")
        training_args, output_dir = create_advanced_training_args()
        
        # 6. Confirmation
        print("\n5. CONFIGURATION HAUTE FIDÉLITÉ:")
        print("  🎯 5 epochs complets")
        print("  📊 LoRA rank 64 (capacité maximale)")
        print("  🧠 Learning rate 8e-6 (mémorisation)")
        print("  📈 Validation continue")
        print("  💾 Early stopping")
        print(f"  📁 Sauvegarde: {output_dir}")
        print("  ⏱️ Temps estimé: 60-90 minutes")
        
        confirm = input("\nLancer le fine-tuning avancé haute fidélité? (y/n): ").lower()
        if confirm != 'y':
            print("Fine-tuning annulé")
            return
        
        # 7. Entraînement
        print("\n6. Lancement du fine-tuning avancé...")
        success = train_advanced_model(peft_model, tokenizer, train_dataset, eval_dataset, training_args)
        
        if success:
            print("\n🎉 FINE-TUNING AVANCÉ TERMINÉ!")
            print("🏦 Chatbot bancaire optimisé pour fidélité maximale!")
            print(f"📁 Modèle final: {output_dir}")
            print("🧪 Testez avec: python test_advanced_fidelity.py")
        else:
            print("\n❌ Échec du fine-tuning avancé")
            
    except Exception as e:
        print(f"❌ Erreur générale: {e}")

if __name__ == "__main__":
    main()
