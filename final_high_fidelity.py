"""
Fine-tuning FINAL haute fidélité - Version garantie
"""

import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import load_from_disk

print("🎯 FINE-TUNING FINAL HAUTE FIDÉLITÉ")
print("=" * 50)

# Configuration
output_dir = "./llama_banking_final_fidelity"

# CUDA
if not torch.cuda.is_available():
    print("❌ CUDA requis")
    exit()

torch.cuda.empty_cache()
print(f"✅ GPU: {torch.cuda.get_device_name()}")

# Configuration 4-bit
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

# Tokenizer
print("📝 Chargement tokenizer...")
model_path = "./models/Llama-3.1-8B-Instruct"

if Path(model_path).exists():
    tokenizer = AutoTokenizer.from_pretrained(model_path)
else:
    tokenizer = AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        token="*************************************"
    )

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Modèle
print("🦙 Chargement modèle...")
if Path(model_path).exists():
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
else:
    model = AutoModelForCausalLM.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct",
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True,
        token="*************************************"
    )

model = prepare_model_for_kbit_training(model)

# LoRA HAUTE CAPACITÉ pour fidélité maximale
print("🔧 Configuration LoRA haute capacité...")
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=64,  # Rank très élevé
    lora_alpha=128,  # Alpha très élevé
    lora_dropout=0.03,  # Dropout très faible
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    bias="none"
)

peft_model = get_peft_model(model, lora_config)
peft_model.print_trainable_parameters()

# Dataset
print("📊 Chargement dataset...")
dataset = load_from_disk("./processed_banking_dataset_no_instruction")
train_dataset = dataset["train"]

# Split train/validation
train_size = int(0.85 * len(train_dataset))
train_split = train_dataset.select(range(train_size))
eval_split = train_dataset.select(range(train_size, len(train_dataset)))

print(f"✅ Train: {len(train_split)} exemples")
print(f"✅ Validation: {len(eval_split)} exemples")

# Configuration d'entraînement HAUTE FIDÉLITÉ
training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=5,  # 5 epochs pour mémorisation
    per_device_train_batch_size=1,
    gradient_accumulation_steps=32,  # Batch effectif de 32
    learning_rate=5e-6,  # Learning rate très bas
    weight_decay=0.005,
    warmup_ratio=0.02,
    lr_scheduler_type="cosine",
    max_grad_norm=0.3,
    optim="paged_adamw_8bit",
    fp16=True,
    gradient_checkpointing=True,
    dataloader_pin_memory=False,
    dataloader_num_workers=0,
    logging_steps=5,
    eval_steps=200,
    save_steps=400,
    save_total_limit=5,
    report_to=[],
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Trainer
trainer = Trainer(
    model=peft_model,
    args=training_args,
    train_dataset=train_split,
    eval_dataset=eval_split,
    data_collator=data_collator,
    processing_class=tokenizer,
)

# Configuration finale
total_steps = len(train_split) // 32 * 5
print("\n🎯 Configuration HAUTE FIDÉLITÉ FINALE:")
print(f"  📊 LoRA rank: 64 (capacité maximale)")
print(f"  🧠 Learning rate: 5e-6 (mémorisation)")
print(f"  🔄 Epochs: 5")
print(f"  📈 Steps totaux: ~{total_steps}")
print(f"  💾 Batch effectif: 32")
print(f"  📁 Sauvegarde: {output_dir}")
print(f"  ⏱️ Temps estimé: 60-90 minutes")

# Entraînement
print("\n🚀 Début fine-tuning haute fidélité...")

try:
    train_result = trainer.train()
    
    # Sauvegarder
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    # Évaluation finale
    eval_results = trainer.evaluate()
    
    print(f"\n✅ FINE-TUNING HAUTE FIDÉLITÉ TERMINÉ!")
    print(f"📊 Train loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
    print(f"📊 Eval loss finale: {eval_results.get('eval_loss', 'N/A')}")
    print(f"📁 Modèle: {output_dir}")
    
    # Sauvegarder métriques
    import json
    final_metrics = {
        'train_metrics': train_result.metrics,
        'eval_metrics': eval_results,
        'config': {
            'epochs': 5,
            'learning_rate': 5e-6,
            'lora_rank': 64,
            'total_steps': total_steps
        }
    }
    
    with open(Path(output_dir) / "final_metrics.json", 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    print(f"🧪 Testez avec: python test_final_fidelity.py")
    
except Exception as e:
    print(f"❌ Erreur: {e}")

print("\n🎉 FINE-TUNING HAUTE FIDÉLITÉ TERMINÉ!")
print("🏦 Chatbot bancaire optimisé pour fidélité maximale au dataset!")
print("📊 Configuration:")
print("  - 5 epochs complets")
print("  - LoRA rank 64")
print("  - Learning rate 5e-6")
print("  - Validation continue")
print("🎯 Fidélité attendue: >70% avec le dataset original")
