"""
Fine-tuning Llama 3.1 8B sur CUDA avec dataset bancaire
Optimisé pour RTX 4060 8GB avec quantification 4-bit
"""

import os
import json
import torch
from datetime import datetime
from pathlib import Path

# Transformers et PEFT
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    prepare_model_for_kbit_training
)
from datasets import load_from_disk

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LlamaBankingFineTuner:
    """Fine-tuner optimisé pour RTX 4060 8GB"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.peft_model = None
        self.dataset = None
        
        # Configuration optimisée pour RTX 4060
        self.config = {
            "model_settings": {
                "model_path": "./models/Llama-3.1-8B-Instruct",
                "load_in_4bit": True,
                "bnb_4bit_compute_dtype": "float16",
                "bnb_4bit_use_double_quant": True,
                "bnb_4bit_quant_type": "nf4"
            },
            "lora_settings": {
                "r": 32,  # Rank adapté pour 8GB
                "lora_alpha": 64,
                "lora_dropout": 0.05,
                "target_modules": [
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ],
                "bias": "none"
            },
            "training_settings": {
                "output_dir": "./llama_banking_finetuned",
                "num_train_epochs": 2,  # Moins d'epochs pour 8GB
                "per_device_train_batch_size": 1,
                "per_device_eval_batch_size": 1,
                "gradient_accumulation_steps": 16,  # Compenser le petit batch
                "learning_rate": 1e-4,
                "weight_decay": 0.01,
                "warmup_ratio": 0.1,
                "lr_scheduler_type": "cosine",
                "max_grad_norm": 1.0,
                "optim": "paged_adamw_8bit",
                "fp16": True,
                "gradient_checkpointing": True,
                "dataloader_pin_memory": True,
                "dataloader_num_workers": 2,  # Réduit pour 8GB
                "logging_steps": 5,
                "eval_steps": 50,
                "save_steps": 100,
                "save_total_limit": 2,
                "max_steps": 500,  # Limiter pour test rapide
                "report_to": [],  # Pas de wandb pour simplifier
            }
        }
    
    def setup_cuda_environment(self):
        """Configure CUDA pour RTX 4060"""
        logger.info("=== Configuration CUDA RTX 4060 ===")
        
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA non disponible")
        
        # Optimisations CUDA
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        # Variables d'environnement pour 8GB
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        os.environ["CUDA_LAUNCH_BLOCKING"] = "0"
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
        
        # Informations GPU
        device = torch.cuda.current_device()
        gpu_props = torch.cuda.get_device_properties(device)
        logger.info(f"✅ GPU: {gpu_props.name}")
        logger.info(f"✅ Mémoire: {gpu_props.total_memory / 1024**3:.1f} GB")
        
        # Vider le cache
        torch.cuda.empty_cache()
    
    def load_model_and_tokenizer(self):
        """Charge le modèle avec quantification 4-bit"""
        logger.info("=== Chargement Llama 3.1 8B (4-bit) ===")
        
        model_path = self.config["model_settings"]["model_path"]
        
        try:
            # Configuration 4-bit pour RTX 4060
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Charger le tokenizer
            logger.info("Chargement du tokenizer...")
            if Path(model_path).exists():
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    token="*************************************"
                )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # Charger le modèle avec quantification
            logger.info("Chargement du modèle (4-bit quantization)...")
            if Path(model_path).exists():
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    "meta-llama/Llama-3.1-8B-Instruct",
                    quantization_config=bnb_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    token="*************************************"
                )
            
            # Préparer pour l'entraînement
            self.model = prepare_model_for_kbit_training(self.model)
            
            logger.info(f"✅ Modèle chargé en 4-bit")
            logger.info(f"✅ Paramètres: {self.model.num_parameters():,}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def setup_lora(self):
        """Configure LoRA optimisé pour 8GB"""
        logger.info("=== Configuration LoRA (optimisé 8GB) ===")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            **self.config["lora_settings"]
        )
        
        # Appliquer LoRA
        self.peft_model = get_peft_model(self.model, lora_config)
        
        # Afficher les paramètres entraînables
        self.peft_model.print_trainable_parameters()
        
        logger.info("✅ LoRA configuré pour RTX 4060")
        return True
    
    def load_dataset(self):
        """Charge le dataset préparé"""
        logger.info("=== Chargement du dataset ===")
        
        dataset_path = "./processed_banking_dataset_no_instruction"
        
        try:
            self.dataset = load_from_disk(dataset_path)
            
            logger.info(f"✅ Dataset chargé:")
            for split_name, split_data in self.dataset.items():
                logger.info(f"  - {split_name}: {len(split_data)} exemples")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur dataset: {e}")
            logger.info("💡 Exécutez d'abord: python prepare_dataset_no_instruction.py")
            return False
    
    def train(self):
        """Lance l'entraînement"""
        logger.info("=== Démarrage Fine-tuning ===")
        
        # Arguments d'entraînement
        training_args = TrainingArguments(**self.config["training_settings"])
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.peft_model,
            args=training_args,
            train_dataset=self.dataset["train"],
            eval_dataset=self.dataset.get("test", self.dataset.get("validation")),
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        try:
            # Entraînement
            logger.info("🚀 Début de l'entraînement...")
            train_result = trainer.train()
            
            # Sauvegarder
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config["training_settings"]["output_dir"])
            
            # Métriques
            metrics = train_result.metrics
            with open(Path(self.config["training_settings"]["output_dir"]) / "training_metrics.json", 'w') as f:
                json.dump(metrics, f, indent=2)
            
            logger.info("✅ Fine-tuning terminé!")
            logger.info(f"✅ Modèle sauvegardé: {self.config['training_settings']['output_dir']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur d'entraînement: {e}")
            return False

def main():
    """Fonction principale"""
    print("🦙 Fine-tuning Llama 3.1 8B - Chatbot Bancaire")
    print("🎮 Optimisé pour RTX 4060 8GB + CUDA 12.9")
    print("=" * 60)
    
    # Vérifications
    if not torch.cuda.is_available():
        print("❌ CUDA requis")
        return
    
    fine_tuner = LlamaBankingFineTuner()
    
    # 1. Configuration CUDA
    fine_tuner.setup_cuda_environment()
    
    # 2. Charger le modèle
    print("\n1. Chargement du modèle...")
    if not fine_tuner.load_model_and_tokenizer():
        print("❌ Échec du chargement")
        return
    
    # 3. LoRA
    print("\n2. Configuration LoRA...")
    fine_tuner.setup_lora()
    
    # 4. Dataset
    print("\n3. Chargement du dataset...")
    if not fine_tuner.load_dataset():
        print("❌ Dataset non trouvé")
        print("💡 Exécutez: python prepare_dataset_no_instruction.py")
        return
    
    # 5. Confirmer
    print("\n4. Prêt pour le fine-tuning!")
    print("⚠️ Configuration RTX 4060 8GB:")
    print("  - Quantification 4-bit")
    print("  - LoRA rank 32")
    print("  - Batch size 1 + gradient accumulation 16")
    print("  - Max 500 steps (test rapide)")
    
    confirm = input("\nLancer le fine-tuning? (y/n): ").lower()
    if confirm != 'y':
        print("Fine-tuning annulé")
        return
    
    # 6. Entraîner
    print("\n5. Lancement du fine-tuning...")
    if fine_tuner.train():
        print("\n🎉 FINE-TUNING TERMINÉ!")
        print("🚀 Testez avec: python test_finetuned_llama.py")
    else:
        print("\n❌ Échec du fine-tuning")

if __name__ == "__main__":
    main()
