"""
Version RAPIDE du backend RAG pour tests
Chargement optimisé et réponses plus rapides
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import time
from typing import Optional

app = FastAPI(title="Banking RAG API - Fast Version", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:8080",
        "http://localhost:5173",
        "http://localhost:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles Pydantic
class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    response_time: float
    contexts_found: int
    similarity_score: float
    conversation_id: str

class FastRAGSystem:
    def __init__(self):
        self.vectorizer = None
        self.question_vectors = None
        self.qa_pairs = []
        self.is_loaded = False
        
    async def load_knowledge_base_only(self):
        """Charge SEULEMENT la base de connaissances (rapide)"""
        if self.is_loaded:
            return
            
        print("📊 Chargement rapide de la base de connaissances...")
        
        clean_file = "cleaned_banking_qa.json"
        
        try:
            with open(clean_file, 'r', encoding='utf-8') as f:
                self.qa_pairs = json.load(f)
        except FileNotFoundError:
            from datasets import load_dataset
            dataset = load_dataset("wasifis/bank-assistant-qa")
            train_data = dataset["train"]
            
            self.qa_pairs = []
            for item in train_data:
                cleaned_item = {
                    "input": item["input"].strip(),
                    "output": item["output"].strip()
                }
                if cleaned_item["input"] and cleaned_item["output"]:
                    self.qa_pairs.append(cleaned_item)
            
            with open(clean_file, 'w', encoding='utf-8') as f:
                json.dump(self.qa_pairs, f, indent=2, ensure_ascii=False)
        
        # Index TF-IDF rapide
        questions = [pair["input"] for pair in self.qa_pairs]
        self.vectorizer = TfidfVectorizer(
            stop_words='english',
            max_features=1000,  # Réduit pour vitesse
            ngram_range=(1, 2)
        )
        self.question_vectors = self.vectorizer.fit_transform(questions)
        
        self.is_loaded = True
        print(f"✅ Base chargée rapidement: {len(self.qa_pairs)} exemples")
    
    def retrieve_relevant_context(self, query, top_k=3):
        """Recherche rapide"""
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.question_vectors).flatten()
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        relevant_contexts = []
        for idx in top_indices:
            if similarities[idx] > 0.1:
                relevant_contexts.append({
                    "question": self.qa_pairs[idx]["input"],
                    "answer": self.qa_pairs[idx]["output"],
                    "similarity": similarities[idx]
                })
        
        return relevant_contexts
    
    async def generate_fast_response(self, user_question: str):
        """Génère une réponse RAPIDE sans modèle lourd"""
        if not self.is_loaded:
            await self.load_knowledge_base_only()
        
        start_time = time.time()
        
        # Rechercher contextes
        relevant_contexts = self.retrieve_relevant_context(user_question, top_k=3)
        
        if relevant_contexts:
            # Utiliser directement la meilleure réponse de la base
            best_context = relevant_contexts[0]
            
            # Développer la réponse
            if "limit" in user_question.lower():
                response = f"Regarding your question about limits:\n\n{best_context['answer']}\n\nThese limits are designed to ensure secure banking operations while providing flexibility for your financial needs."
            elif "charge" in user_question.lower() or "fee" in user_question.lower():
                response = f"Here are the detailed charges and fees:\n\n{best_context['answer']}\n\nThese charges are structured to provide transparent pricing for banking services."
            elif "facility" in user_question.lower() or "service" in user_question.lower():
                response = f"The available facilities and services include:\n\n{best_context['answer']}\n\nThese facilities are designed to enhance your banking experience."
            else:
                response = f"Based on the banking information available:\n\n{best_context['answer']}\n\nFor more specific information, please contact customer service."
        else:
            response = "I understand your question. However, I couldn't find specific information in our banking database. Please contact customer service for detailed assistance with your inquiry."
        
        response_time = time.time() - start_time
        max_similarity = max([ctx['similarity'] for ctx in relevant_contexts]) if relevant_contexts else 0.0
        
        return response, response_time, len(relevant_contexts), max_similarity

# Système RAG rapide
fast_rag_system = FastRAGSystem()

@app.on_event("startup")
async def startup_event():
    """Chargement rapide au démarrage"""
    print("🚀 Démarrage API RAG RAPIDE...")
    await fast_rag_system.load_knowledge_base_only()

@app.get("/")
async def root():
    return {"message": "Banking RAG API - Fast Version", "status": "active"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "model_loaded": fast_rag_system.is_loaded,
        "gpu_available": False,  # Pas de GPU pour version rapide
        "version": "fast"
    }

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Endpoint rapide pour le chat"""
    try:
        if not request.message.strip():
            raise HTTPException(status_code=400, detail="Message cannot be empty")
        
        # Générer réponse rapide
        response, response_time, contexts_found, similarity_score = await fast_rag_system.generate_fast_response(request.message)
        
        conversation_id = request.conversation_id or f"conv_{int(time.time())}"
        
        return ChatResponse(
            response=response,
            response_time=response_time,
            contexts_found=contexts_found,
            similarity_score=similarity_score,
            conversation_id=conversation_id
        )
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Lancement API RAG RAPIDE...")
    print("📡 Frontend: http://localhost:8080")
    print("🔗 API: http://localhost:8000")
    print("⚡ Version rapide - Pas de modèle lourd")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
