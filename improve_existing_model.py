"""
Amélioration du modèle existant pour haute fidélité
Utilise le modèle ./llama_banking_fresh comme point de départ
"""

import torch
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, BitsAndBytesConfig
)
from peft import PeftModel, prepare_model_for_kbit_training
from datasets import load_from_disk

def main():
    print("🔧 Amélioration du modèle existant pour haute fidélité")
    print("=" * 60)
    
    # Vérifier si le modèle existant est disponible
    existing_model_path = "./llama_banking_fresh"
    if not Path(existing_model_path).exists():
        print("❌ Modèle existant non trouvé")
        print("💡 Lancez d'abord: python fresh_finetuning.py")
        return
    
    output_dir = "./llama_banking_improved"
    
    # CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA requis")
        return
    
    torch.cuda.empty_cache()
    print(f"✅ GPU: {torch.cuda.get_device_name()}")
    
    # Configuration 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    # Tokenizer
    print("📝 Chargement tokenizer...")
    base_model_path = "./models/Llama-3.1-8B-Instruct"
    
    if Path(base_model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Modèle de base
    print("🦙 Chargement modèle de base...")
    if Path(base_model_path).exists():
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            token="*************************************"
        )
    
    # Charger le modèle fine-tuné existant
    print("🔧 Chargement du modèle fine-tuné existant...")
    model = PeftModel.from_pretrained(base_model, existing_model_path)
    
    # Préparer pour l'entraînement supplémentaire
    model = prepare_model_for_kbit_training(model)
    
    print("✅ Modèle existant chargé et prêt pour amélioration")
    model.print_trainable_parameters()
    
    # Dataset
    print("📊 Chargement dataset...")
    dataset = load_from_disk("./processed_banking_dataset_no_instruction")
    train_dataset = dataset["train"]
    
    print(f"✅ Dataset: {len(train_dataset)} exemples")
    
    # Configuration d'entraînement pour amélioration
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=2,  # 2 epochs supplémentaires
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,  # Plus petit pour stabilité
        learning_rate=1e-5,  # Learning rate très bas pour fine-tuning
        weight_decay=0.01,
        warmup_ratio=0.03,
        lr_scheduler_type="linear",
        max_grad_norm=0.3,  # Gradient clipping strict
        optim="paged_adamw_8bit",
        fp16=True,
        gradient_checkpointing=True,
        dataloader_pin_memory=False,
        dataloader_num_workers=0,
        logging_steps=5,
        save_steps=200,
        save_total_limit=2,
        report_to=[],
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=data_collator,
        processing_class=tokenizer,
    )
    
    # Configuration
    print("\n🎯 Configuration AMÉLIORATION:")
    print(f"  📊 Modèle de base: {existing_model_path}")
    print(f"  🧠 Learning rate: 1e-5 (très bas)")
    print(f"  🔄 Epochs supplémentaires: 2")
    print(f"  📈 Steps totaux: ~{len(train_dataset) // 8 * 2}")
    print(f"  💾 Sauvegarde: {output_dir}")
    print(f"  ⏱️ Temps estimé: 20-30 minutes")
    
    confirm = input("\nLancer l'amélioration du modèle? (y/n): ").lower()
    if confirm != 'y':
        print("Amélioration annulée")
        return
    
    # Entraînement d'amélioration
    print("\n🚀 Début amélioration du modèle...")
    
    try:
        train_result = trainer.train()
        
        # Sauvegarder
        trainer.save_model()
        tokenizer.save_pretrained(output_dir)
        
        print(f"\n✅ AMÉLIORATION TERMINÉE!")
        print(f"📊 Loss finale: {train_result.metrics.get('train_loss', 'N/A')}")
        print(f"📁 Modèle amélioré: {output_dir}")
        
        # Sauvegarder métriques
        import json
        with open(Path(output_dir) / "improvement_metrics.json", 'w') as f:
            json.dump(train_result.metrics, f, indent=2)
        
        print(f"🧪 Testez avec le nouveau modèle amélioré!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'amélioration: {e}")
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 AMÉLIORATION TERMINÉE!")
        print("🏦 Votre chatbot bancaire a été amélioré pour une meilleure fidélité!")
        print("📁 Nouveau modèle: ./llama_banking_improved")
    else:
        print("\n❌ Échec de l'amélioration")
