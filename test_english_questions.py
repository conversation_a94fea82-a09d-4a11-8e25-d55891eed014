"""
Test avec des questions en anglais du dataset original
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from datasets import load_dataset
from pathlib import Path

def test_with_original_questions():
    """Teste avec des questions exactes du dataset"""
    print("🧪 Test avec questions originales du dataset")
    print("=" * 50)
    
    # Charger le dataset original
    dataset = load_dataset("wasifis/bank-assistant-qa")
    
    # Prendre quelques questions exactes
    test_samples = [
        dataset['train'][0],
        dataset['train'][100], 
        dataset['train'][200],
        dataset['train'][500],
        dataset['train'][1000]
    ]
    
    # Charger le modèle fine-tuné
    print("🦙 Chargement du modèle...")
    
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    model_path = "./models/Llama-3.1-8B-Instruct"
    if Path(model_path).exists():
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        base_model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            token="*************************************"
        )
        base_model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-3.1-8B-Instruct",
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            token="*************************************"
        )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Modèle fine-tuné
    model = PeftModel.from_pretrained(base_model, "./llama_banking_fresh")
    
    print("✅ Modèle chargé")
    
    # Tester chaque échantillon
    for i, sample in enumerate(test_samples, 1):
        print(f"\n--- Test {i}/5 ---")
        print(f"❓ Question originale: {sample['input']}")
        print(f"✅ Réponse attendue: {sample['output'][:100]}...")
        
        # Générer la réponse
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a professional banking assistant.<|eot_id|><|start_header_id|>user<|end_header_id|>

{sample['input']}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = tokenizer(chat_prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 100,
                temperature=0.3,  # Plus déterministe
                do_sample=True,
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        print(f"🤖 Réponse générée: {response[:100]}...")
        
        # Comparaison simple
        if any(word in response.lower() for word in sample['output'].lower().split()[:5]):
            print("✅ Contient des mots-clés de la réponse attendue")
        else:
            print("❌ Réponse très différente de l'attendue")

if __name__ == "__main__":
    test_with_original_questions()
