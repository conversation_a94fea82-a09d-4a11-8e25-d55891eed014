"""
Test du modèle haute fidélité avec vérification contre dataset
"""

import torch
import json
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
from datasets import load_dataset
from pathlib import Path
import random
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class HighFidelityTester:
    """Testeur pour modèle haute fidélité"""
    
    def __init__(self, model_path="./llama_banking_high_fidelity"):
        self.model_path = Path(model_path)
        self.model = None
        self.tokenizer = None
        self.original_dataset = None
        self.load_components()
    
    def load_components(self):
        """Charge tous les composants"""
        print("=== Chargement des composants ===")
        
        # Dataset original
        print("📊 Chargement dataset original...")
        self.original_dataset = load_dataset("wasifis/bank-assistant-qa")
        print(f"✅ {len(self.original_dataset['train'])} exemples chargés")
        
        # Modèle fine-tuné
        print("🦙 Chargement modèle haute fidélité...")
        
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        # Tokenizer
        base_model_path = "./models/Llama-3.1-8B-Instruct"
        if Path(base_model_path).exists():
            self.tokenizer = AutoTokenizer.from_pretrained(base_model_path)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                token="*************************************"
            )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Modèle de base
        if Path(base_model_path).exists():
            base_model = AutoModelForCausalLM.from_pretrained(
                base_model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
        else:
            base_model = AutoModelForCausalLM.from_pretrained(
                "meta-llama/Llama-3.1-8B-Instruct",
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                token="*************************************"
            )
        
        # Modèle fine-tuné
        self.model = PeftModel.from_pretrained(base_model, self.model_path)
        print("✅ Modèle haute fidélité chargé")
    
    def generate_response(self, question, max_length=200):
        """Génère une réponse"""
        
        # Format exact du dataset
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are an expert bank customer service bot for NUST Bank. Provide accurate, helpful, and concise answers to banking questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        inputs = self.tokenizer(chat_prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_length,
                temperature=0.1,  # Très déterministe pour fidélité
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(chat_prompt):].strip()
        
        if response.endswith("<|eot_id|>"):
            response = response[:-9].strip()
        
        return response
    
    def calculate_similarity(self, text1, text2):
        """Calcule la similarité TF-IDF"""
        vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
        try:
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            return similarity
        except:
            return 0.0
    
    def test_exact_dataset_questions(self, num_tests=10):
        """Teste avec des questions exactes du dataset"""
        print(f"\n🧪 Test avec {num_tests} questions exactes du dataset")
        print("=" * 60)
        
        # Sélectionner des échantillons aléatoirement
        dataset_samples = random.sample(list(self.original_dataset['train']), num_tests)
        
        results = []
        high_fidelity_count = 0
        
        for i, sample in enumerate(dataset_samples, 1):
            print(f"\n--- Test {i}/{num_tests} ---")
            print(f"❓ Question: {sample['input']}")
            print(f"✅ Réponse attendue: {sample['output'][:100]}...")
            
            # Générer la réponse
            generated_response = self.generate_response(sample['input'])
            print(f"🤖 Réponse générée: {generated_response[:100]}...")
            
            # Calculer la similarité
            similarity = self.calculate_similarity(generated_response, sample['output'])
            print(f"📊 Similarité: {similarity:.3f}")
            
            # Évaluer la fidélité
            if similarity > 0.5:
                print("✅ HAUTE FIDÉLITÉ")
                high_fidelity_count += 1
            elif similarity > 0.3:
                print("⚠️ FIDÉLITÉ MOYENNE")
            else:
                print("❌ FAIBLE FIDÉLITÉ")
            
            results.append({
                'question': sample['input'],
                'expected': sample['output'],
                'generated': generated_response,
                'similarity': similarity,
                'high_fidelity': similarity > 0.5
            })
        
        # Statistiques
        fidelity_rate = (high_fidelity_count / num_tests) * 100
        avg_similarity = sum(r['similarity'] for r in results) / len(results)
        
        print(f"\n📊 RÉSULTATS FIDÉLITÉ:")
        print(f"✅ Haute fidélité: {high_fidelity_count}/{num_tests} ({fidelity_rate:.1f}%)")
        print(f"📈 Similarité moyenne: {avg_similarity:.3f}")
        
        if fidelity_rate >= 70:
            print("🎉 EXCELLENT: Modèle très fidèle au dataset!")
        elif fidelity_rate >= 50:
            print("👍 BON: Modèle généralement fidèle")
        else:
            print("⚠️ ATTENTION: Fidélité insuffisante")
        
        return results
    
    def test_specific_banking_topics(self):
        """Teste des sujets bancaires spécifiques"""
        print(f"\n🏦 Test sujets bancaires spécifiques")
        print("=" * 40)
        
        # Trouver des questions par sujet dans le dataset
        topics = {
            "account fees": [],
            "loan": [],
            "card": [],
            "deposit": [],
            "charges": []
        }
        
        # Classifier les questions par sujet
        for item in self.original_dataset['train']:
            question = item['input'].lower()
            for topic in topics:
                if topic in question:
                    topics[topic].append(item)
                    break
        
        # Tester chaque sujet
        for topic, questions in topics.items():
            if questions:
                print(f"\n📋 Sujet: {topic.upper()}")
                sample = random.choice(questions)
                print(f"❓ Question: {sample['input']}")
                
                response = self.generate_response(sample['input'])
                similarity = self.calculate_similarity(response, sample['output'])
                
                print(f"🤖 Réponse: {response[:150]}...")
                print(f"📊 Fidélité: {similarity:.3f}")
    
    def interactive_fidelity_test(self):
        """Test interactif avec vérification de fidélité"""
        print(f"\n💬 Test Interactif avec Vérification Fidélité")
        print("=" * 50)
        print("Posez des questions bancaires (tapez 'quit' pour quitter)")
        
        while True:
            user_input = input("\n👤 Question: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                break
            
            if user_input:
                # Générer la réponse
                response = self.generate_response(user_input)
                print(f"🤖 Réponse: {response}")
                
                # Chercher des questions similaires dans le dataset
                best_match = None
                best_similarity = 0
                
                for item in random.sample(list(self.original_dataset['train']), 100):
                    q_similarity = self.calculate_similarity(user_input, item['input'])
                    if q_similarity > best_similarity:
                        best_similarity = q_similarity
                        best_match = item
                
                if best_match and best_similarity > 0.3:
                    print(f"\n📋 Question similaire trouvée (sim: {best_similarity:.3f}):")
                    print(f"   Q: {best_match['input']}")
                    print(f"   R: {best_match['output'][:100]}...")
                    
                    r_similarity = self.calculate_similarity(response, best_match['output'])
                    print(f"📊 Fidélité de la réponse: {r_similarity:.3f}")

def main():
    """Fonction principale"""
    print("🎯 Test Modèle Haute Fidélité")
    print("=" * 40)
    
    # Vérifier si le modèle existe
    model_path = "./llama_banking_high_fidelity"
    if not Path(model_path).exists():
        print("❌ Modèle haute fidélité non trouvé")
        print("💡 Lancez d'abord: python high_fidelity_finetuning.py")
        return
    
    tester = HighFidelityTester(model_path)
    
    print("\nOptions de test:")
    print("1. 🧪 Test fidélité (10 questions exactes)")
    print("2. 🏦 Test sujets bancaires spécifiques")
    print("3. 💬 Test interactif avec vérification")
    print("4. 📊 Test complet (toutes les options)")
    
    choice = input("\nVotre choix (1-4): ").strip()
    
    if choice == "1":
        tester.test_exact_dataset_questions(10)
    
    elif choice == "2":
        tester.test_specific_banking_topics()
    
    elif choice == "3":
        tester.interactive_fidelity_test()
    
    elif choice == "4":
        tester.test_exact_dataset_questions(10)
        tester.test_specific_banking_topics()
        tester.interactive_fidelity_test()
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
