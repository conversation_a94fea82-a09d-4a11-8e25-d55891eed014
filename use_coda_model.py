"""
Utilisation du modèle fine-tuné avec Coda
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import json

class CodaBankingChatbot:
    """Chatbot utilisant le modèle Coda fine-tuné"""
    
    def __init__(self, model_path="./coda_banking_model"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.load_model()
    
    def load_model(self):
        """Charge le modèle fine-tuné"""
        try:
            print("Chargement du modèle fine-tuné...")
            
            # Charger la configuration
            with open("coda_config.json", 'r') as f:
                config = json.load(f)
            
            base_model_name = config["model_config"]["base_model"]
            
            # Charger le tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(base_model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Charger le modèle de base
            base_model = AutoModelForCausalLM.from_pretrained(base_model_name)
            
            # Charger les adapters LoRA
            self.model = PeftModel.from_pretrained(base_model, self.model_path)
            
            print("✅ Modèle fine-tuné chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur de chargement: {e}")
    
    def chat(self, question):
        """Chat avec le modèle fine-tuné"""
        if self.model is None:
            return "Modèle non chargé"
        
        prompt = f"Question: {question}\nRéponse:"
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(prompt):].strip()
        return response
    
    def interactive_chat(self):
        """Chat interactif"""
        print("\n🏦 Chatbot Bancaire Coda Fine-tuné")
        print("=" * 40)
        print("💬 Posez vos questions (tapez 'quit' pour quitter)")
        print("-" * 40)
        
        while True:
            user_input = input("\n👤 Vous: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'quitter']:
                print("👋 Au revoir!")
                break
            
            if user_input:
                response = self.chat(user_input)
                print(f"🤖 Assistant: {response}")

def main():
    chatbot = CodaBankingChatbot()
    
    if chatbot.model:
        chatbot.interactive_chat()
    else:
        print("❌ Impossible de charger le modèle")
        print("Assurez-vous d'avoir exécuté le fine-tuning d'abord")

if __name__ == "__main__":
    main()
