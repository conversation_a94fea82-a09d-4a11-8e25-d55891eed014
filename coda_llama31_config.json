{"model_config": {"model_path": "meta-llama/Llama-3.1-8B", "model_type": "llama", "torch_dtype": "float16", "device_map": "auto", "load_in_8bit": true, "trust_remote_code": true}, "lora_config": {"task_type": "CAUSAL_LM", "r": 16, "lora_alpha": 32, "lora_dropout": 0.1, "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "bias": "none", "inference_mode": false}, "training_config": {"output_dir": "./coda_llama31_model", "num_train_epochs": 2, "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 8, "learning_rate": 0.0001, "weight_decay": 0.01, "warmup_steps": 50, "logging_steps": 5, "eval_steps": 50, "save_steps": 50, "max_length": 512, "fp16": true, "dataloader_pin_memory": false, "remove_unused_columns": false, "gradient_checkpointing": true, "optim": "adamw_8bit"}}