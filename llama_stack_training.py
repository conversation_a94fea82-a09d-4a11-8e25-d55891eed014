"""
Fine-tuning LoRA avec Llama Stack
"""

import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from datasets import load_from_disk

def load_llama_stack_model():
    """Charge le modèle depuis Llama Stack"""
    print("=== Chargement du modèle Llama Stack ===")
    
    # Charger la configuration
    with open("llama_stack_config.json", 'r') as f:
        config = json.load(f)
    
    model_id = config["llama_stack"]["model_id"]
    
    try:
        # Le modèle devrait être disponible localement après téléchargement
        # Adapter le chemin selon l'installation de Llama Stack
        
        print(f"Chargement de {model_id}...")
        
        # Essayer de charger depuis le cache local de Llama Stack
        tokenizer = AutoTokenizer.from_pretrained(
            f"meta-llama/{model_id}",
            trust_remote_code=True
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            f"meta-llama/{model_id}",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            trust_remote_code=True
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"✅ Modèle chargé: {model.num_parameters():,} paramètres")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Erreur de chargement: {e}")
        print("Vérifiez que le modèle a été téléchargé avec Llama Stack")
        return None, None

def apply_lora_to_llama_stack():
    """Applique LoRA au modèle Llama Stack"""
    print("\n=== Application LoRA ===")
    
    # Charger le modèle
    model, tokenizer = load_llama_stack_model()
    if model is None:
        return None, None
    
    # Charger la config LoRA
    with open("llama_stack_config.json", 'r') as f:
        config = json.load(f)
    
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        **config["lora_config"]
    )
    
    # Appliquer LoRA
    peft_model = get_peft_model(model, lora_config)
    peft_model.print_trainable_parameters()
    
    print("✅ LoRA appliqué avec succès")
    return peft_model, tokenizer

def train_with_llama_stack():
    """Lance l'entraînement avec le modèle Llama Stack"""
    print("\n=== Entraînement avec Llama Stack ===")
    
    # Appliquer LoRA
    peft_model, tokenizer = apply_lora_to_llama_stack()
    if peft_model is None:
        return False
    
    # Charger les données
    try:
        dataset = load_from_disk("./formatted_data")
        print(f"Dataset chargé: {len(dataset['train'])} exemples")
    except:
        print("❌ Données non trouvées. Exécutez d'abord prepare_training_data.py")
        return False
    
    # Configuration d'entraînement
    from transformers import TrainingArguments, Trainer, DataCollatorForLanguageModeling
    
    training_args = TrainingArguments(
        output_dir="./llama_stack_results",
        num_train_epochs=3,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,
        learning_rate=2e-4,
        weight_decay=0.01,
        warmup_steps=100,
        logging_steps=10,
        eval_steps=100,
        save_steps=100,
        eval_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        report_to=None,
        remove_unused_columns=False,
        fp16=torch.cuda.is_available(),
    )
    
    # Tokeniser les données
    def tokenize_function(examples):
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=512,
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized
    
    tokenized_dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=dataset["train"].column_names
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=peft_model,
        args=training_args,
        train_dataset=tokenized_dataset["train"],
        eval_dataset=tokenized_dataset["validation"],
        data_collator=data_collator,
    )
    
    # Entraînement
    print("Démarrage de l'entraînement...")
    try:
        trainer.train()
        trainer.save_model()
        tokenizer.save_pretrained("./llama_stack_results")
        print("✅ Entraînement terminé et modèle sauvegardé")
        return True
    except Exception as e:
        print(f"❌ Erreur d'entraînement: {e}")
        return False

if __name__ == "__main__":
    train_with_llama_stack()
