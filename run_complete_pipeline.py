"""
Pipeline complet pour le fine-tuning Llama 3.1 8B
Orchestration de toutes les étapes du projet
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

class LlamaBankingPipeline:
    """Pipeline complet pour le chatbot bancaire"""
    
    def __init__(self):
        self.steps_completed = []
        self.project_dir = Path.cwd()
        
    def log_step(self, step_name: str, success: bool = True):
        """Log une étape du pipeline"""
        status = "✅" if success else "❌"
        print(f"{status} {step_name}")
        
        if success:
            self.steps_completed.append(step_name)
    
    def check_requirements(self):
        """Vérifie les prérequis"""
        print("=== Vérification des prérequis ===")
        
        requirements = {
            "Python 3.8+": sys.version_info >= (3, 8),
            "CUDA disponible": self.check_cuda(),
            "Espace disque (>50GB)": self.check_disk_space(),
            "Mémoire GPU (>16GB)": self.check_gpu_memory()
        }
        
        all_ok = True
        for req, status in requirements.items():
            self.log_step(req, status)
            if not status:
                all_ok = False
        
        return all_ok
    
    def check_cuda(self):
        """Vérifie CUDA"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def check_disk_space(self):
        """Vérifie l'espace disque"""
        try:
            import shutil
            free_space = shutil.disk_usage('.').free
            return free_space > 50 * 1024**3  # 50GB
        except:
            return True  # Assume OK si on ne peut pas vérifier
    
    def check_gpu_memory(self):
        """Vérifie la mémoire GPU"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                return gpu_memory > 16 * 1024**3  # 16GB
            return False
        except:
            return False
    
    def run_step(self, script_name: str, step_description: str):
        """Exécute une étape du pipeline"""
        print(f"\n{'='*60}")
        print(f"ÉTAPE: {step_description}")
        print(f"Script: {script_name}")
        print(f"{'='*60}")
        
        if not Path(script_name).exists():
            self.log_step(f"Script {script_name} trouvé", False)
            return False
        
        try:
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, timeout=3600)
            
            if result.returncode == 0:
                self.log_step(step_description, True)
                print(result.stdout)
                return True
            else:
                self.log_step(step_description, False)
                print(f"Erreur: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.log_step(f"{step_description} (timeout)", False)
            return False
        except Exception as e:
            self.log_step(f"{step_description} (exception)", False)
            print(f"Exception: {e}")
            return False
    
    def interactive_step(self, script_name: str, step_description: str):
        """Exécute une étape interactive"""
        print(f"\n{'='*60}")
        print(f"ÉTAPE INTERACTIVE: {step_description}")
        print(f"Script: {script_name}")
        print(f"{'='*60}")
        
        print(f"Voulez-vous exécuter cette étape? (y/n)")
        if input().lower() != 'y':
            self.log_step(f"{step_description} (ignoré)", True)
            return True
        
        try:
            subprocess.run([sys.executable, script_name])
            self.log_step(step_description, True)
            return True
        except Exception as e:
            self.log_step(step_description, False)
            print(f"Erreur: {e}")
            return False
    
    def create_project_summary(self):
        """Crée un résumé du projet"""
        summary = {
            "project": "Llama 3.1 8B Banking Chatbot",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "steps_completed": self.steps_completed,
            "files_created": [
                "setup_cuda_environment.py",
                "prepare_banking_dataset.py", 
                "llama_finetuning_cuda.py",
                "test_finetuned_llama.py",
                "deploy_banking_chatbot.py"
            ],
            "model_info": {
                "base_model": "meta-llama/Llama-3.1-8B-Instruct",
                "dataset": "wasifis/bank-assistant-qa",
                "technique": "LoRA (Low-Rank Adaptation)",
                "optimization": "4-bit quantization + CUDA"
            },
            "next_steps": [
                "Fine-tuner le modèle avec vos données",
                "Tester les performances",
                "Déployer l'interface web",
                "Monitorer et améliorer"
            ]
        }
        
        with open("project_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📋 Résumé du projet sauvegardé: project_summary.json")
    
    def run_complete_pipeline(self):
        """Exécute le pipeline complet"""
        print("🚀 PIPELINE COMPLET - CHATBOT BANCAIRE LLAMA 3.1 8B")
        print("="*70)
        
        # 1. Vérifications
        if not self.check_requirements():
            print("\n❌ Prérequis non satisfaits")
            print("💡 Installez CUDA et assurez-vous d'avoir suffisamment de ressources")
            return False
        
        # 2. Configuration CUDA
        if not self.interactive_step("setup_cuda_environment.py", 
                                    "Configuration environnement CUDA"):
            print("⚠️ Problème avec la configuration CUDA")
        
        # 3. Préparation dataset
        if not self.run_step("prepare_banking_dataset.py", 
                           "Préparation dataset wasifis/bank-assistant-qa"):
            print("❌ Échec de préparation du dataset")
            return False
        
        # 4. Fine-tuning (interactif car long)
        print(f"\n⚠️ ATTENTION: Le fine-tuning peut prendre plusieurs heures")
        print(f"Assurez-vous d'avoir:")
        print(f"  - Accès à Llama 3.1 sur Hugging Face")
        print(f"  - GPU avec >16GB de mémoire")
        print(f"  - Connexion internet stable")
        
        if not self.interactive_step("llama_finetuning_cuda.py", 
                                   "Fine-tuning Llama 3.1 8B avec LoRA"):
            print("⚠️ Fine-tuning non effectué")
        
        # 5. Test (optionnel)
        if Path("./llama_banking_finetuned").exists():
            if not self.interactive_step("test_finetuned_llama.py", 
                                       "Test du modèle fine-tuné"):
                print("⚠️ Tests non effectués")
        
        # 6. Déploiement (optionnel)
        if Path("./llama_banking_finetuned").exists():
            if not self.interactive_step("deploy_banking_chatbot.py", 
                                       "Déploiement du chatbot"):
                print("⚠️ Déploiement non effectué")
        
        # 7. Résumé
        self.create_project_summary()
        
        print(f"\n🎉 PIPELINE TERMINÉ!")
        print(f"✅ Étapes complétées: {len(self.steps_completed)}")
        print(f"📁 Fichiers créés dans: {self.project_dir}")
        
        return True
    
    def quick_setup(self):
        """Configuration rapide pour développement"""
        print("⚡ CONFIGURATION RAPIDE")
        print("="*30)
        
        steps = [
            ("setup_cuda_environment.py", "Configuration CUDA"),
            ("prepare_banking_dataset.py", "Préparation dataset")
        ]
        
        for script, description in steps:
            if not self.run_step(script, description):
                print(f"❌ Échec: {description}")
                return False
        
        print("\n✅ Configuration rapide terminée!")
        print("🚀 Prêt pour le fine-tuning!")
        return True

def main():
    """Fonction principale"""
    pipeline = LlamaBankingPipeline()
    
    print("🏦 CHATBOT BANCAIRE LLAMA 3.1 8B")
    print("="*40)
    print("Choisissez une option:")
    print("1. 🚀 Pipeline complet (recommandé)")
    print("2. ⚡ Configuration rapide")
    print("3. 📋 Vérification des prérequis seulement")
    print("4. 🚪 Quitter")
    
    choice = input("\nVotre choix (1-4): ").strip()
    
    if choice == "1":
        pipeline.run_complete_pipeline()
    
    elif choice == "2":
        pipeline.quick_setup()
    
    elif choice == "3":
        pipeline.check_requirements()
    
    elif choice == "4":
        print("👋 Au revoir!")
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
